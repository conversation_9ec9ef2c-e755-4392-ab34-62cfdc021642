<?php

/**
 * Test All Action Buttons
 * Elite Transfer System - Button Testing Script
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

$db = DatabaseManager::getInstance();

// Create test transfer if none exists
$testTransfer = $db->selectOne("SELECT * FROM transfers WHERE (deleted_at IS NULL OR deleted_at = '') LIMIT 1");

if (!$testTransfer) {
    // Create a test transfer
    $transferData = [
        'transfer_code' => 'TEST' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT),
        'pickup_code' => 'PCK' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT),
        'sender_name' => 'مرسل تجريبي',
        'sender_phone' => '+1234567890',
        'sender_email' => '<EMAIL>',
        'recipient_name' => 'مستلم تجريبي',
        'recipient_phone' => '+0987654321',
        'recipient_email' => '<EMAIL>',
        'amount' => 1000.00,
        'fee' => 50.00,
        'total_amount' => 1050.00,
        'currency_from' => 'USD',
        'currency_to' => 'USD',
        'exchange_rate' => 1.0,
        'purpose' => 'اختبار النظام',
        'notes' => 'تحويل تجريبي لاختبار الأزرار',
        'status' => 'pending',
        'user_id' => getUserId(),
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $transferId = $db->insert('transfers', $transferData);
    $testTransfer = $db->selectOne("SELECT * FROM transfers WHERE id = :id", ['id' => $transferId]);
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            color: #495057;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .test-button {
            padding: 15px 20px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn-test-approve { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; }
        .btn-test-reject { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #212529; }
        .btn-test-complete { background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); color: white; }
        .btn-test-view { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; }
        .btn-test-print { background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%); color: white; }
        .btn-test-edit { background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; }
        .btn-test-duplicate { background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white; }
        .btn-test-history { background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%); color: #212529; }
        .btn-test-delete { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; }
        
        .transfer-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-pending { background: rgba(255, 193, 7, 0.2); color: #ff8f00; }
        .status-processing { background: rgba(13, 110, 253, 0.2); color: #0d6efd; }
        .status-completed { background: rgba(25, 135, 84, 0.2); color: #198754; }
        .status-cancelled { background: rgba(220, 53, 69, 0.2); color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-card">
            <h1 class="test-title">
                <i class="bi bi-gear-fill me-3"></i>
                اختبار جميع أزرار الإجراءات
            </h1>
            
            <!-- Transfer Info -->
            <div class="transfer-info">
                <h5 class="mb-3">معلومات التحويل التجريبي:</h5>
                <div class="info-item">
                    <strong>رمز التحويل:</strong>
                    <span><?= $testTransfer['transfer_code'] ?></span>
                </div>
                <div class="info-item">
                    <strong>المرسل:</strong>
                    <span><?= $testTransfer['sender_name'] ?></span>
                </div>
                <div class="info-item">
                    <strong>المستلم:</strong>
                    <span><?= $testTransfer['recipient_name'] ?></span>
                </div>
                <div class="info-item">
                    <strong>المبلغ:</strong>
                    <span>$<?= number_format($testTransfer['amount'], 2) ?></span>
                </div>
                <div class="info-item">
                    <strong>الحالة:</strong>
                    <span class="status-badge status-<?= $testTransfer['status'] ?>">
                        <?= getStatusLabel($testTransfer['status']) ?>
                    </span>
                </div>
            </div>
            
            <!-- Test Buttons -->
            <div class="button-grid">
                <?php if ($testTransfer['status'] === 'pending'): ?>
                <button class="test-button btn-test-approve" onclick="testApprove(<?= $testTransfer['id'] ?>)">
                    <i class="bi bi-check-lg"></i>
                    اختبار الموافقة
                </button>
                <button class="test-button btn-test-reject" onclick="testReject(<?= $testTransfer['id'] ?>)">
                    <i class="bi bi-x-lg"></i>
                    اختبار الرفض
                </button>
                <?php elseif ($testTransfer['status'] === 'processing'): ?>
                <button class="test-button btn-test-complete" onclick="testComplete(<?= $testTransfer['id'] ?>)">
                    <i class="bi bi-check-circle-fill"></i>
                    اختبار الإكمال
                </button>
                <?php endif; ?>
                
                <button class="test-button btn-test-view" onclick="testView(<?= $testTransfer['id'] ?>)">
                    <i class="bi bi-eye-fill"></i>
                    اختبار العرض
                </button>
                
                <button class="test-button btn-test-print" onclick="testPrint(<?= $testTransfer['id'] ?>)">
                    <i class="bi bi-printer-fill"></i>
                    اختبار الطباعة
                </button>
                
                <button class="test-button btn-test-edit" onclick="testEdit(<?= $testTransfer['id'] ?>)">
                    <i class="bi bi-pencil-fill"></i>
                    اختبار التعديل
                </button>
                
                <button class="test-button btn-test-duplicate" onclick="testDuplicate(<?= $testTransfer['id'] ?>)">
                    <i class="bi bi-files"></i>
                    اختبار النسخ
                </button>
                
                <button class="test-button btn-test-history" onclick="testHistory(<?= $testTransfer['id'] ?>)">
                    <i class="bi bi-clock-history"></i>
                    اختبار السجل
                </button>
                
                <button class="test-button btn-test-delete" onclick="testDelete(<?= $testTransfer['id'] ?>)">
                    <i class="bi bi-trash-fill"></i>
                    اختبار الحذف
                </button>
            </div>
            
            <!-- Navigation -->
            <div class="text-center mt-4">
                <a href="transfers_safe.php" class="btn btn-primary btn-lg me-3">
                    <i class="bi bi-arrow-left me-2"></i>
                    العودة إلى إدارة التحويلات
                </a>
                <button class="btn btn-success btn-lg" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    إعادة تحميل الصفحة
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        // Test functions for all buttons
        function testApprove(transferId) {
            performAction('approve_transfer', transferId, 'موافقة');
        }
        
        function testReject(transferId) {
            Swal.fire({
                title: 'اختبار رفض التحويل',
                input: 'textarea',
                inputLabel: 'سبب الرفض',
                inputPlaceholder: 'اكتب سبب رفض التحويل...',
                inputValue: 'سبب تجريبي للاختبار',
                showCancelButton: true,
                confirmButtonText: 'رفض',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#ffc107'
            }).then((result) => {
                if (result.isConfirmed) {
                    performAction('reject_transfer', transferId, 'رفض', { reason: result.value });
                }
            });
        }
        
        function testComplete(transferId) {
            performAction('complete_transfer', transferId, 'إكمال');
        }
        
        function testView(transferId) {
            Swal.fire({
                title: 'اختبار عرض التحويل',
                text: 'سيتم فتح صفحة عرض التحويل في نافذة جديدة',
                icon: 'info',
                confirmButtonText: 'فتح',
                showCancelButton: true,
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.open(`view_transfer.php?id=${transferId}`, '_blank');
                }
            });
        }
        
        function testPrint(transferId) {
            Swal.fire({
                title: 'اختبار طباعة التحويل',
                text: 'سيتم فتح صفحة الطباعة في نافذة جديدة',
                icon: 'info',
                confirmButtonText: 'طباعة',
                showCancelButton: true,
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.open(`print_transfer.php?id=${transferId}`, '_blank');
                }
            });
        }
        
        function testEdit(transferId) {
            Swal.fire({
                title: 'اختبار تعديل التحويل',
                text: 'سيتم الانتقال إلى صفحة تعديل التحويل',
                icon: 'info',
                confirmButtonText: 'تعديل',
                showCancelButton: true,
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = `edit_transfer.php?id=${transferId}`;
                }
            });
        }
        
        function testDuplicate(transferId) {
            Swal.fire({
                title: 'اختبار نسخ التحويل',
                text: 'سيتم الانتقال إلى صفحة إنشاء تحويل جديد بنفس البيانات',
                icon: 'question',
                confirmButtonText: 'نسخ',
                showCancelButton: true,
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#6f42c1'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = `create_transfer_fixed.php?duplicate=${transferId}`;
                }
            });
        }
        
        function testHistory(transferId) {
            // Test the history function from transfers_safe.php
            $.ajax({
                url: 'transfers_safe.php',
                method: 'POST',
                data: {
                    action: 'get_transfer_history',
                    transfer_id: transferId
                },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        let timelineHtml = '<div class="text-start"><div class="timeline">';
                        
                        response.history.forEach(function(item) {
                            const date = new Date(item.timestamp);
                            const timeAgo = getTimeAgo(date);
                            
                            timelineHtml += `
                                <div class="timeline-item mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="timeline-marker bg-${item.color} rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                                        <div class="timeline-content">
                                            <h6 class="mb-1 fw-bold">
                                                <i class="bi bi-${item.icon} me-2"></i>
                                                ${item.title}
                                            </h6>
                                            <p class="mb-1 text-muted">${item.description}</p>
                                            <small class="text-muted">${timeAgo}</small>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                        
                        timelineHtml += '</div></div>';
                        
                        Swal.fire({
                            title: `اختبار سجل التحويل - ${response.transfer.transfer_code}`,
                            html: timelineHtml,
                            width: 600,
                            confirmButtonText: 'إغلاق',
                            confirmButtonColor: '#6c757d'
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ!',
                            text: response.message || 'فشل في تحميل السجل',
                            confirmButtonText: 'حسناً'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ!',
                        text: 'خطأ في الاتصال بالخادم',
                        confirmButtonText: 'حسناً'
                    });
                }
            });
        }
        
        function testDelete(transferId) {
            Swal.fire({
                title: 'اختبار حذف التحويل',
                text: 'هل أنت متأكد من حذف هذا التحويل؟ (هذا اختبار)',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545'
            }).then((result) => {
                if (result.isConfirmed) {
                    performAction('delete_transfer', transferId, 'حذف');
                }
            });
        }
        
        // Helper function to perform AJAX actions
        function performAction(action, transferId, actionName, extraData = {}) {
            const data = {
                action: action,
                transfer_id: transferId,
                ...extraData
            };
            
            $.ajax({
                url: 'transfers_safe.php',
                method: 'POST',
                data: data,
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'نجح الاختبار!',
                            text: `تم اختبار ${actionName} بنجاح: ${response.message}`,
                            confirmButtonText: 'ممتاز'
                        }).then(() => {
                            location.reload(); // Reload to see changes
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'فشل الاختبار!',
                            text: `فشل في اختبار ${actionName}: ${response.message || 'خطأ غير معروف'}`,
                            confirmButtonText: 'حسناً'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في الاختبار!',
                        text: `خطأ في الاتصال بالخادم أثناء اختبار ${actionName}`,
                        confirmButtonText: 'حسناً'
                    });
                }
            });
        }
        
        // Helper function to calculate time ago
        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);
            
            if (diffInSeconds < 60) {
                return 'منذ لحظات';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `منذ ${minutes} دقيقة`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `منذ ${hours} ساعة`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `منذ ${days} يوم`;
            }
        }
    </script>
</body>
</html>
