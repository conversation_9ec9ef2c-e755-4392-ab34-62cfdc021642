<?php

/**
 * Switch to MySQL Database
 * Elite Transfer System - Automated MySQL Setup
 */

echo "🔄 Switching to MySQL Database - Elite Transfer System\n";
echo str_repeat("=", 60) . "\n\n";

// Step 1: Test MySQL availability
echo "📋 Step 1: Testing MySQL availability...\n";
try {
    $pdo = new PDO("mysql:host=localhost;port=3306", 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "✅ MySQL server is available\n";
} catch (Exception $e) {
    echo "❌ MySQL server is not available\n";
    echo "Error: " . $e->getMessage() . "\n\n";
    echo "🔧 Please start MySQL in XAMPP Control Panel first\n";
    exit(1);
}

// Step 2: Create database if not exists
echo "\n📋 Step 2: Creating database...\n";
try {
    $pdo->exec("CREATE DATABASE IF NOT EXISTS elite_transfer CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database 'elite_transfer' is ready\n";
} catch (Exception $e) {
    echo "❌ Failed to create database: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 3: Update .env file
echo "\n📋 Step 3: Updating .env file...\n";
$envFile = __DIR__ . '/.env';
$envContent = file_get_contents($envFile);

// Update database configuration
$updates = [
    'DB_CONNECTION=sqlite' => 'DB_CONNECTION=mysql',
    'DB_DATABASE=database/elite_transfer_production.db' => 'DB_DATABASE=elite_transfer',
    '# DB_HOST=localhost' => 'DB_HOST=localhost',
    '# DB_PORT=3306' => 'DB_PORT=3306',
    '# DB_USERNAME=root' => 'DB_USERNAME=root',
    '# DB_PASSWORD=' => 'DB_PASSWORD='
];

foreach ($updates as $old => $new) {
    $envContent = str_replace($old, $new, $envContent);
}

// Add MySQL settings if not present
if (strpos($envContent, 'DB_HOST=localhost') === false) {
    $envContent .= "\n# MySQL Configuration\n";
    $envContent .= "DB_HOST=localhost\n";
    $envContent .= "DB_PORT=3306\n";
    $envContent .= "DB_USERNAME=root\n";
    $envContent .= "DB_PASSWORD=\n";
}

file_put_contents($envFile, $envContent);
echo "✅ .env file updated successfully\n";

// Step 4: Test new connection
echo "\n📋 Step 4: Testing new MySQL connection...\n";
try {
    // Reload environment
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
    
    $dsn = "mysql:host=localhost;port=3306;dbname=elite_transfer;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ MySQL connection successful\n";
    
    // Check if tables exist
    $tables = $pdo->query("SHOW TABLES")->fetchAll();
    echo "   Tables found: " . count($tables) . "\n";
    
} catch (Exception $e) {
    echo "❌ MySQL connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 5: Setup database if needed
echo "\n📋 Step 5: Setting up database structure...\n";
if (count($tables) < 10) {
    echo "⚠️  Database needs setup, running setup script...\n";
    
    // Run setup script
    $output = [];
    $returnCode = 0;
    exec('php setup_production_database.php 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ Database setup completed successfully\n";
    } else {
        echo "⚠️  Database setup had some issues, but continuing...\n";
        foreach ($output as $line) {
            echo "   $line\n";
        }
    }
} else {
    echo "✅ Database structure is already complete\n";
}

// Step 6: Final verification
echo "\n📋 Step 6: Final verification...\n";
try {
    require_once 'public/includes/database_manager.php';
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    $connectionInfo = $dbManager->getConnectionInfo();
    echo "✅ Database Manager connected successfully\n";
    echo "   Type: {$connectionInfo['type']}\n";
    echo "   Database: {$connectionInfo['database']}\n";
    echo "   Host: {$connectionInfo['host']}\n";
    
    // Test basic operations
    $userCount = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $countryCount = $db->query("SELECT COUNT(*) FROM countries")->fetchColumn();
    
    echo "   Users: $userCount\n";
    echo "   Countries: $countryCount\n";
    
} catch (Exception $e) {
    echo "❌ Final verification failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 Successfully switched to MySQL!\n\n";

echo "📝 Summary:\n";
echo "   ✅ MySQL server: Connected\n";
echo "   ✅ Database: Created and configured\n";
echo "   ✅ .env file: Updated\n";
echo "   ✅ Database structure: Ready\n";
echo "   ✅ Connection: Working\n\n";

echo "🚀 Your application is now using MySQL database!\n";
echo "💡 MySQL provides better performance and scalability for production use.\n\n";

echo "🔧 Next steps:\n";
echo "1. Test the application: http://localhost/dashboard.php\n";
echo "2. Check database status: http://localhost/database_status.php\n";
echo "3. Run quick test: php quick_db_test.php\n\n";

echo "📊 Benefits of MySQL:\n";
echo "   • Better concurrent user support\n";
echo "   • Advanced indexing and optimization\n";
echo "   • Better backup and recovery options\n";
echo "   • Industry standard for web applications\n";

?>
