<?php

/**
 * Enhanced Database Manager v2.0
 * Elite Transfer System - High-performance database connection and operations
 */

// Load configuration
require_once __DIR__ . '/config.php';

class DatabaseManager {
    private static $instance = null;
    private $connection = null;
    private $queryCount = 0;
    private $queryLog = [];
    private $transactionLevel = 0;
    private $cache = [];
    private $cacheEnabled = true;
    
    private function __construct() {
        $this->connect();
        $this->cacheEnabled = CACHE_ENABLED;
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => true,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET . " COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
            
            // Set timezone
            $this->connection->exec("SET time_zone = '+03:00'");
            
            logMessage('INFO', 'Database connection established successfully');
            
        } catch (PDOException $e) {
            logMessage('ERROR', 'Database connection failed: ' . $e->getMessage());
            throw new Exception('Database connection failed: ' . $e->getMessage());
        }
    }
    
    public function getConnection() {
        if ($this->connection === null) {
            $this->connect();
        }
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $this->queryCount++;
            $startTime = microtime(true);
            
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            
            $executionTime = microtime(true) - $startTime;
            
            // Log slow queries
            if ($executionTime > 1.0) {
                logMessage('WARNING', 'Slow query detected', [
                    'sql' => $sql,
                    'params' => $params,
                    'execution_time' => $executionTime
                ]);
            }
            
            // Store query log
            $this->queryLog[] = [
                'sql' => $sql,
                'params' => $params,
                'execution_time' => $executionTime,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            return $stmt;
            
        } catch (PDOException $e) {
            logMessage('ERROR', 'Query execution failed', [
                'sql' => $sql,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw new Exception('Query execution failed: ' . $e->getMessage());
        }
    }
    
    public function select($sql, $params = []) {
        $cacheKey = md5($sql . serialize($params));
        
        // Check cache first
        if ($this->cacheEnabled && isset($this->cache[$cacheKey])) {
            $cacheData = $this->cache[$cacheKey];
            if (time() - $cacheData['timestamp'] < CACHE_LIFETIME) {
                return $cacheData['data'];
            }
        }
        
        $stmt = $this->query($sql, $params);
        $result = $stmt->fetchAll();
        
        // Cache the result
        if ($this->cacheEnabled) {
            $this->cache[$cacheKey] = [
                'data' => $result,
                'timestamp' => time()
            ];
        }
        
        return $result;
    }
    
    public function selectOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    public function execute($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function insert($table, $data) {
        $columns = array_keys($data);
        $placeholders = ':' . implode(', :', $columns);
        $sql = "INSERT INTO {$table} (" . implode(', ', $columns) . ") VALUES ({$placeholders})";
        
        $stmt = $this->query($sql, $data);
        return $this->connection->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setParts = [];
        foreach (array_keys($data) as $column) {
            $setParts[] = "{$column} = :{$column}";
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $setParts) . " WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function beginTransaction() {
        if ($this->transactionLevel === 0) {
            $this->connection->beginTransaction();
        }
        $this->transactionLevel++;
        
        logMessage('DEBUG', 'Transaction started', ['level' => $this->transactionLevel]);
    }
    
    public function commit() {
        if ($this->transactionLevel === 1) {
            $this->connection->commit();
        }
        $this->transactionLevel = max(0, $this->transactionLevel - 1);
        
        logMessage('DEBUG', 'Transaction committed', ['level' => $this->transactionLevel]);
    }
    
    public function rollback() {
        if ($this->transactionLevel === 1) {
            $this->connection->rollback();
        }
        $this->transactionLevel = max(0, $this->transactionLevel - 1);
        
        logMessage('DEBUG', 'Transaction rolled back', ['level' => $this->transactionLevel]);
    }
    
    public function getQueryCount() {
        return $this->queryCount;
    }
    
    public function getQueryLog() {
        return $this->queryLog;
    }
    
    public function clearCache() {
        $this->cache = [];
        logMessage('DEBUG', 'Database cache cleared');
    }
    
    public function getConnectionInfo() {
        return [
            'type' => 'MySQL',
            'host' => DB_HOST,
            'database' => DB_NAME,
            'charset' => DB_CHARSET,
            'query_count' => $this->queryCount,
            'cache_enabled' => $this->cacheEnabled,
            'cache_size' => count($this->cache),
            'transaction_level' => $this->transactionLevel
        ];
    }
    
    // Transfer-specific methods
    public function getTransfers($filters = [], $limit = 10, $offset = 0) {
        $where = ["(t.deleted_at IS NULL OR t.deleted_at = '')"];
        $params = [];
        
        if (!empty($filters['search'])) {
            $search = '%' . $filters['search'] . '%';
            $where[] = "(t.transfer_code LIKE :search OR t.pickup_code LIKE :search2 OR t.sender_name LIKE :search3 OR t.recipient_name LIKE :search4)";
            $params['search'] = $search;
            $params['search2'] = $search;
            $params['search3'] = $search;
            $params['search4'] = $search;
        }
        
        if (!empty($filters['status'])) {
            $where[] = "t.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['date_from'])) {
            $where[] = "DATE(t.created_at) >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $where[] = "DATE(t.created_at) <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        $sql = "
            SELECT t.*, 
                   COALESCE(u.name, 'غير محدد') as user_name,
                   COALESCE(sc.name, 'غير محدد') as sender_country_name,
                   COALESCE(rc.name, 'غير محدد') as recipient_country_name
            FROM transfers t
            LEFT JOIN users u ON t.user_id = u.id AND (u.deleted_at IS NULL OR u.deleted_at = '')
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.recipient_country_id = rc.id
            WHERE {$whereClause}
            ORDER BY t.created_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        return $this->select($sql, $params);
    }
    
    public function getTransferCount($filters = []) {
        $where = ["(t.deleted_at IS NULL OR t.deleted_at = '')"];
        $params = [];
        
        if (!empty($filters['search'])) {
            $search = '%' . $filters['search'] . '%';
            $where[] = "(t.transfer_code LIKE :search OR t.pickup_code LIKE :search2 OR t.sender_name LIKE :search3 OR t.recipient_name LIKE :search4)";
            $params['search'] = $search;
            $params['search2'] = $search;
            $params['search3'] = $search;
            $params['search4'] = $search;
        }
        
        if (!empty($filters['status'])) {
            $where[] = "t.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['date_from'])) {
            $where[] = "DATE(t.created_at) >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $where[] = "DATE(t.created_at) <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        $sql = "SELECT COUNT(*) FROM transfers t WHERE {$whereClause}";
        $result = $this->selectOne($sql, $params);
        
        return intval($result['COUNT(*)']);
    }
    
    public function getTransferByCode($code) {
        $sql = "
            SELECT t.*, 
                   COALESCE(u.name, 'غير محدد') as user_name,
                   COALESCE(sc.name, 'غير محدد') as sender_country_name,
                   COALESCE(rc.name, 'غير محدد') as recipient_country_name
            FROM transfers t
            LEFT JOIN users u ON t.user_id = u.id AND (u.deleted_at IS NULL OR u.deleted_at = '')
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.recipient_country_id = rc.id
            WHERE (t.transfer_code = :code OR t.pickup_code = :code2)
            AND (t.deleted_at IS NULL OR t.deleted_at = '')
        ";
        
        return $this->selectOne($sql, ['code' => $code, 'code2' => $code]);
    }
    
    public function getCountries() {
        $sql = "SELECT id, name, currency, code FROM countries ORDER BY name";
        return $this->select($sql);
    }
    
    public function getUsers($filters = [], $limit = 10, $offset = 0) {
        $where = ["(deleted_at IS NULL OR deleted_at = '')"];
        $params = [];
        
        if (!empty($filters['search'])) {
            $search = '%' . $filters['search'] . '%';
            $where[] = "(name LIKE :search OR email LIKE :search2)";
            $params['search'] = $search;
            $params['search2'] = $search;
        }
        
        if (!empty($filters['role'])) {
            $where[] = "role = :role";
            $params['role'] = $filters['role'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        $sql = "
            SELECT id, user_code, name, email, phone, role, status, created_at, last_login
            FROM users 
            WHERE {$whereClause}
            ORDER BY created_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        return $this->select($sql, $params);
    }
    
    public function getStatistics() {
        $stats = [];
        
        // Total transfers
        $result = $this->selectOne("SELECT COUNT(*) as count FROM transfers WHERE deleted_at IS NULL");
        $stats['total_transfers'] = intval($result['count']);
        
        // Transfers by status
        $statusStats = $this->select("
            SELECT status, COUNT(*) as count 
            FROM transfers 
            WHERE deleted_at IS NULL 
            GROUP BY status
        ");
        
        foreach ($statusStats as $stat) {
            $stats[$stat['status'] . '_transfers'] = intval($stat['count']);
        }
        
        // Total amount
        $result = $this->selectOne("
            SELECT COALESCE(SUM(total_amount), 0) as total
            FROM transfers
            WHERE status = 'completed' AND deleted_at IS NULL
        ");
        $stats['total_amount'] = floatval($result['total']);
        
        // Today's transfers
        $result = $this->selectOne("
            SELECT COUNT(*) as count 
            FROM transfers 
            WHERE DATE(created_at) = CURDATE() AND deleted_at IS NULL
        ");
        $stats['today_transfers'] = intval($result['count']);
        
        // Today's amount
        $result = $this->selectOne("
            SELECT COALESCE(SUM(total_amount), 0) as total
            FROM transfers
            WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND deleted_at IS NULL
        ");
        $stats['today_amount'] = floatval($result['total']);
        
        return $stats;
    }
    
    public function __destruct() {
        if ($this->transactionLevel > 0) {
            $this->rollback();
            logMessage('WARNING', 'Auto-rollback performed in destructor');
        }
    }
}

?>
