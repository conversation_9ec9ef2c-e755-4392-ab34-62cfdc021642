<?php

/**
 * Advanced Notifications System
 * Elite Transfer System - Real-time Notification Management
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Check authentication
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = DatabaseManager::getInstance();

// Create notifications table if not exists
$db->execute("
    CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        data JSON,
        read_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at),
        INDEX idx_read_at (read_at)
    )
");

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'get_notifications':
                $userId = getUserId();
                $page = intval($_POST['page'] ?? 1);
                $limit = intval($_POST['limit'] ?? 20);
                $unreadOnly = isset($_POST['unread_only']) && $_POST['unread_only'] === 'true';
                
                $offset = ($page - 1) * $limit;
                $conditions = ['user_id = :user_id'];
                $params = ['user_id' => $userId];
                
                if ($unreadOnly) {
                    $conditions[] = 'read_at IS NULL';
                }
                
                $whereClause = implode(' AND ', $conditions);
                
                // Get total count
                $totalQuery = "SELECT COUNT(*) as total FROM notifications WHERE $whereClause";
                $total = $db->selectOne($totalQuery, $params)['total'];
                
                // Get notifications
                $notificationsQuery = "SELECT * FROM notifications WHERE $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
                $notifications = $db->select($notificationsQuery, $params);
                
                // Format notifications
                foreach ($notifications as &$notification) {
                    $notification['time_ago'] = getTimeAgo($notification['created_at']);
                    $notification['data'] = json_decode($notification['data'], true);
                }
                
                echo json_encode([
                    'success' => true,
                    'notifications' => $notifications,
                    'total' => intval($total),
                    'unread_count' => $db->selectOne("SELECT COUNT(*) as count FROM notifications WHERE user_id = :user_id AND read_at IS NULL", ['user_id' => $userId])['count']
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'mark_as_read':
                $notificationId = intval($_POST['notification_id'] ?? 0);
                $userId = getUserId();
                
                if ($notificationId) {
                    $db->update('notifications', 
                        ['read_at' => date('Y-m-d H:i:s')], 
                        'id = :id AND user_id = :user_id', 
                        ['id' => $notificationId, 'user_id' => $userId]
                    );
                }
                
                echo json_encode(['success' => true], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'mark_all_as_read':
                $userId = getUserId();
                
                $db->update('notifications', 
                    ['read_at' => date('Y-m-d H:i:s')], 
                    'user_id = :user_id AND read_at IS NULL', 
                    ['user_id' => $userId]
                );
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم تمييز جميع الإشعارات كمقروءة'
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'delete_notification':
                $notificationId = intval($_POST['notification_id'] ?? 0);
                $userId = getUserId();
                
                if ($notificationId) {
                    $db->delete('notifications', 'id = :id AND user_id = :user_id', [
                        'id' => $notificationId, 
                        'user_id' => $userId
                    ]);
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم حذف الإشعار'
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'send_notification':
                $userId = intval($_POST['user_id'] ?? 0);
                $type = trim($_POST['type'] ?? 'info');
                $title = trim($_POST['title'] ?? '');
                $message = trim($_POST['message'] ?? '');
                $data = $_POST['data'] ?? [];
                
                if (!$userId || empty($title) || empty($message)) {
                    throw new Exception('البيانات المطلوبة مفقودة');
                }
                
                $notificationData = [
                    'user_id' => $userId,
                    'type' => $type,
                    'title' => $title,
                    'message' => $message,
                    'data' => json_encode($data),
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                $notificationId = $db->insert('notifications', $notificationData);
                
                if ($notificationId) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم إرسال الإشعار بنجاح',
                        'notification_id' => $notificationId
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception('فشل في إرسال الإشعار');
                }
                break;
                
            case 'broadcast_notification':
                $type = trim($_POST['type'] ?? 'info');
                $title = trim($_POST['title'] ?? '');
                $message = trim($_POST['message'] ?? '');
                $roles = $_POST['roles'] ?? [];
                $data = $_POST['data'] ?? [];
                
                if (empty($title) || empty($message)) {
                    throw new Exception('العنوان والرسالة مطلوبان');
                }
                
                // Get users based on roles
                $conditions = ["(deleted_at IS NULL OR deleted_at = '')", "status = 'active'"];
                $params = [];
                
                if (!empty($roles)) {
                    $rolePlaceholders = implode(',', array_fill(0, count($roles), '?'));
                    $conditions[] = "role IN ($rolePlaceholders)";
                    $params = array_merge($params, $roles);
                }
                
                $whereClause = implode(' AND ', $conditions);
                $users = $db->select("SELECT id FROM users WHERE $whereClause", $params);
                
                $sentCount = 0;
                foreach ($users as $user) {
                    $notificationData = [
                        'user_id' => $user['id'],
                        'type' => $type,
                        'title' => $title,
                        'message' => $message,
                        'data' => json_encode($data),
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    
                    if ($db->insert('notifications', $notificationData)) {
                        $sentCount++;
                    }
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => "تم إرسال الإشعار إلى $sentCount مستخدم",
                    'sent_count' => $sentCount
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'get_notification_stats':
                $userId = getUserId();
                
                $stats = [
                    'total' => $db->selectOne("SELECT COUNT(*) as count FROM notifications WHERE user_id = :user_id", ['user_id' => $userId])['count'],
                    'unread' => $db->selectOne("SELECT COUNT(*) as count FROM notifications WHERE user_id = :user_id AND read_at IS NULL", ['user_id' => $userId])['count'],
                    'today' => $db->selectOne("SELECT COUNT(*) as count FROM notifications WHERE user_id = :user_id AND DATE(created_at) = CURDATE()", ['user_id' => $userId])['count'],
                    'week' => $db->selectOne("SELECT COUNT(*) as count FROM notifications WHERE user_id = :user_id AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)", ['user_id' => $userId])['count']
                ];
                
                echo json_encode([
                    'success' => true,
                    'stats' => $stats
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

// Helper function to calculate time ago
function getTimeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'منذ لحظات';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return "منذ $minutes دقيقة";
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return "منذ $hours ساعة";
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return "منذ $days يوم";
    } else {
        $months = floor($time / 2592000);
        return "منذ $months شهر";
    }
}

// Notification Helper Functions
class NotificationHelper {
    private $db;
    
    public function __construct() {
        $this->db = DatabaseManager::getInstance();
    }
    
    public function sendTransferNotification($userId, $transferId, $type, $data = []) {
        $notifications = [
            'transfer_created' => [
                'title' => 'تم إنشاء تحويل جديد',
                'message' => 'تم إنشاء تحويل جديد برمز: ' . ($data['transfer_code'] ?? ''),
                'type' => 'info'
            ],
            'transfer_approved' => [
                'title' => 'تمت الموافقة على التحويل',
                'message' => 'تمت الموافقة على التحويل برمز: ' . ($data['transfer_code'] ?? ''),
                'type' => 'success'
            ],
            'transfer_completed' => [
                'title' => 'تم إكمال التحويل',
                'message' => 'تم إكمال التحويل برمز: ' . ($data['transfer_code'] ?? '') . ' بنجاح',
                'type' => 'success'
            ],
            'transfer_failed' => [
                'title' => 'فشل في التحويل',
                'message' => 'فشل التحويل برمز: ' . ($data['transfer_code'] ?? ''),
                'type' => 'error'
            ],
            'transfer_cancelled' => [
                'title' => 'تم إلغاء التحويل',
                'message' => 'تم إلغاء التحويل برمز: ' . ($data['transfer_code'] ?? ''),
                'type' => 'warning'
            ]
        ];
        
        if (isset($notifications[$type])) {
            $notification = $notifications[$type];
            $data['transfer_id'] = $transferId;
            
            return $this->sendNotification($userId, $notification['type'], $notification['title'], $notification['message'], $data);
        }
        
        return false;
    }
    
    public function sendSystemNotification($userId, $title, $message, $type = 'info', $data = []) {
        return $this->sendNotification($userId, $type, $title, $message, $data);
    }
    
    public function broadcastSystemNotification($title, $message, $type = 'info', $roles = [], $data = []) {
        $conditions = ["(deleted_at IS NULL OR deleted_at = '')", "status = 'active'"];
        $params = [];
        
        if (!empty($roles)) {
            $rolePlaceholders = implode(',', array_fill(0, count($roles), '?'));
            $conditions[] = "role IN ($rolePlaceholders)";
            $params = array_merge($params, $roles);
        }
        
        $whereClause = implode(' AND ', $conditions);
        $users = $this->db->select("SELECT id FROM users WHERE $whereClause", $params);
        
        $sentCount = 0;
        foreach ($users as $user) {
            if ($this->sendNotification($user['id'], $type, $title, $message, $data)) {
                $sentCount++;
            }
        }
        
        return $sentCount;
    }
    
    private function sendNotification($userId, $type, $title, $message, $data = []) {
        $notificationData = [
            'user_id' => $userId,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => json_encode($data),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->db->insert('notifications', $notificationData);
    }
    
    public function getUnreadCount($userId) {
        return $this->db->selectOne("SELECT COUNT(*) as count FROM notifications WHERE user_id = :user_id AND read_at IS NULL", ['user_id' => $userId])['count'];
    }
    
    public function markAsRead($notificationId, $userId) {
        return $this->db->update('notifications', 
            ['read_at' => date('Y-m-d H:i:s')], 
            'id = :id AND user_id = :user_id', 
            ['id' => $notificationId, 'user_id' => $userId]
        );
    }
    
    public function markAllAsRead($userId) {
        return $this->db->update('notifications', 
            ['read_at' => date('Y-m-d H:i:s')], 
            'user_id = :user_id AND read_at IS NULL', 
            ['user_id' => $userId]
        );
    }
    
    public function deleteNotification($notificationId, $userId) {
        return $this->db->delete('notifications', 'id = :id AND user_id = :user_id', [
            'id' => $notificationId, 
            'user_id' => $userId
        ]);
    }
    
    public function cleanupOldNotifications($days = 30) {
        return $this->db->delete('notifications', 'created_at < DATE_SUB(NOW(), INTERVAL :days DAY)', ['days' => $days]);
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الإشعارات المتقدم - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
</head>
