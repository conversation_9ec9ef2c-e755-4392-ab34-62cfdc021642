<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\OtpCode;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Show login form
     */
    public function showLogin()
    {
        return view('auth.login');
    }

    /**
     * Handle login request
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Check rate limiting
        $key = 'login.' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            return response()->json([
                'success' => false,
                'message' => "Too many login attempts. Please try again in {$seconds} seconds."
            ], 429);
        }

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            RateLimiter::hit($key, 300); // 5 minutes lockout
            
            // Log failed login attempt
            AuditLog::create([
                'user_id' => $user?->id,
                'action' => 'login_failed',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'severity' => 'medium',
                'description' => 'Failed login attempt for email: ' . $request->email,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        // Check if user is active
        if (!$user->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Account is deactivated. Please contact support.'
            ], 403);
        }

        // Check if account is locked
        if ($user->locked_until && $user->locked_until->isFuture()) {
            return response()->json([
                'success' => false,
                'message' => 'Account is temporarily locked. Please try again later.'
            ], 423);
        }

        // Clear rate limiting on successful login
        RateLimiter::clear($key);

        // Reset login attempts
        $user->update([
            'login_attempts' => 0,
            'locked_until' => null,
            'last_login_at' => now(),
        ]);

        // Check if 2FA is enabled
        if ($user->two_factor_enabled) {
            // Generate and send OTP
            $otp = $this->generateOtp($user, 'login', $user->phone);
            
            return response()->json([
                'success' => true,
                'requires_otp' => true,
                'message' => 'OTP sent to your registered phone number',
                'user_id' => $user->id,
            ]);
        }

        // Login user
        Auth::login($user);
        
        // Log successful login
        AuditLog::create([
            'user_id' => $user->id,
            'action' => 'login_success',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'severity' => 'low',
            'description' => 'User logged in successfully',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'user' => $user->only(['id', 'name', 'email', 'role']),
            'redirect' => $this->getRedirectUrl($user),
        ]);
    }

    /**
     * Verify OTP for 2FA login
     */
    public function verifyOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'otp' => 'required|string|size:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::findOrFail($request->user_id);
        
        $otpRecord = OtpCode::where('user_id', $user->id)
            ->where('code', $request->otp)
            ->where('type', 'login')
            ->where('is_used', false)
            ->where('expires_at', '>', now())
            ->first();

        if (!$otpRecord) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired OTP'
            ], 401);
        }

        // Mark OTP as used
        $otpRecord->update([
            'is_used' => true,
            'used_at' => now(),
        ]);

        // Login user
        Auth::login($user);

        // Log successful 2FA login
        AuditLog::create([
            'user_id' => $user->id,
            'action' => 'login_2fa_success',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'severity' => 'low',
            'description' => 'User completed 2FA login successfully',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'user' => $user->only(['id', 'name', 'email', 'role']),
            'redirect' => $this->getRedirectUrl($user),
        ]);
    }

    /**
     * Show registration form
     */
    public function showRegister()
    {
        return view('auth.register');
    }

    /**
     * Handle registration request
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'required|string|max:20|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'country_id' => 'required|exists:countries,id',
            'national_id' => 'nullable|string|max:50',
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female,other',
            'address' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'country_id' => $request->country_id,
            'national_id' => $request->national_id,
            'date_of_birth' => $request->date_of_birth,
            'gender' => $request->gender,
            'address' => $request->address,
            'role' => 'customer',
            'kyc_status' => 'pending',
        ]);

        // Generate email verification OTP
        $this->generateOtp($user, 'verification', $user->email);

        // Log registration
        AuditLog::create([
            'user_id' => $user->id,
            'action' => 'user_registered',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'severity' => 'low',
            'description' => 'New user registered',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Registration successful. Please verify your email.',
            'user_id' => $user->id,
        ]);
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        $user = Auth::user();
        
        if ($user) {
            // Log logout
            AuditLog::create([
                'user_id' => $user->id,
                'action' => 'logout',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'severity' => 'low',
                'description' => 'User logged out',
            ]);
        }

        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * Generate OTP code
     */
    private function generateOtp(User $user, string $type, string $identifier): string
    {
        $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        
        OtpCode::create([
            'user_id' => $user->id,
            'code' => $code,
            'type' => $type,
            'identifier' => $identifier,
            'expires_at' => now()->addMinutes(config('app.otp_expiry_minutes', 5)),
        ]);

        // Here you would send the OTP via SMS/Email
        // For now, we'll just return the code
        return $code;
    }

    /**
     * Get redirect URL based on user role
     */
    private function getRedirectUrl(User $user): string
    {
        return match ($user->role) {
            'super_admin', 'admin' => '/admin/dashboard',
            'agent' => '/agent/dashboard',
            'customer' => '/dashboard',
            default => '/dashboard',
        };
    }
}
