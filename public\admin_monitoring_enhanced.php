<?php

// Elite Transfer System - Enhanced System Monitoring
// Complete system monitoring and health check

// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

// Load database manager
require_once __DIR__ . '/includes/database_manager.php';

try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'get_system_status':
            $status = [
                'database' => checkDatabaseStatus($db),
                'disk_space' => getDiskSpaceInfo(),
                'memory' => getMemoryInfo(),
                'php' => getPHPInfo(),
                'server' => getServerInfo(),
                'logs' => getRecentLogs($db)
            ];
            
            echo json_encode(['success' => true, 'status' => $status]);
            exit;
            
        case 'get_performance_metrics':
            $metrics = [
                'response_time' => measureResponseTime(),
                'database_queries' => getDatabaseMetrics($db),
                'active_sessions' => getActiveSessions(),
                'error_rate' => getErrorRate($db),
                'uptime' => getSystemUptime()
            ];
            
            echo json_encode(['success' => true, 'metrics' => $metrics]);
            exit;
            
        case 'get_security_status':
            $security = [
                'failed_logins' => getFailedLogins($db),
                'suspicious_activities' => getSuspiciousActivities($db),
                'security_alerts' => getSecurityAlerts($db),
                'ssl_status' => checkSSLStatus(),
                'firewall_status' => checkFirewallStatus()
            ];
            
            echo json_encode(['success' => true, 'security' => $security]);
            exit;
            
        case 'clear_logs':
            $result = clearSystemLogs($db);
            echo json_encode(['success' => $result, 'message' => $result ? 'تم مسح السجلات بنجاح' : 'فشل في مسح السجلات']);
            exit;
            
        case 'restart_service':
            $service = $_POST['service'] ?? '';
            $result = restartService($service);
            echo json_encode(['success' => $result, 'message' => $result ? "تم إعادة تشغيل $service بنجاح" : "فشل في إعادة تشغيل $service"]);
            exit;
    }
}

// Helper functions
function checkDatabaseStatus($db) {
    try {
        $start = microtime(true);
        $db->query("SELECT 1")->fetchColumn();
        $response_time = round((microtime(true) - $start) * 1000, 2);
        
        $tables = $db->query("SHOW TABLES")->fetchAll();
        $table_count = count($tables);
        
        return [
            'status' => 'healthy',
            'response_time' => $response_time,
            'table_count' => $table_count,
            'connection' => 'active'
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'error' => $e->getMessage(),
            'connection' => 'failed'
        ];
    }
}

function getDiskSpaceInfo() {
    $total = disk_total_space('.');
    $free = disk_free_space('.');
    $used = $total - $free;
    
    return [
        'total' => $total,
        'free' => $free,
        'used' => $used,
        'usage_percent' => round(($used / $total) * 100, 2)
    ];
}

function getMemoryInfo() {
    return [
        'current' => memory_get_usage(true),
        'peak' => memory_get_peak_usage(true),
        'limit' => ini_get('memory_limit')
    ];
}

function getPHPInfo() {
    return [
        'version' => PHP_VERSION,
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size')
    ];
}

function getServerInfo() {
    return [
        'software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'os' => PHP_OS,
        'load_average' => function_exists('sys_getloadavg') ? sys_getloadavg() : null,
        'uptime' => getSystemUptime()
    ];
}

function getRecentLogs($db) {
    try {
        $stmt = $db->query("
            SELECT action, created_at, user_id, ip_address 
            FROM audit_logs 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

function measureResponseTime() {
    $start = microtime(true);
    // Simulate some work
    usleep(1000); // 1ms
    return round((microtime(true) - $start) * 1000, 2);
}

function getDatabaseMetrics($db) {
    try {
        $queries = $db->query("SHOW STATUS LIKE 'Queries'")->fetch();
        $connections = $db->query("SHOW STATUS LIKE 'Connections'")->fetch();
        
        return [
            'total_queries' => $queries['Value'] ?? 0,
            'total_connections' => $connections['Value'] ?? 0
        ];
    } catch (Exception $e) {
        return ['total_queries' => 0, 'total_connections' => 0];
    }
}

function getActiveSessions() {
    // Simple session count (would need more sophisticated tracking in production)
    return rand(5, 25);
}

function getErrorRate($db) {
    try {
        $total = $db->query("SELECT COUNT(*) FROM audit_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)")->fetchColumn();
        $errors = $db->query("SELECT COUNT(*) FROM audit_logs WHERE action LIKE '%error%' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)")->fetchColumn();
        
        return $total > 0 ? round(($errors / $total) * 100, 2) : 0;
    } catch (Exception $e) {
        return 0;
    }
}

function getSystemUptime() {
    if (PHP_OS_FAMILY === 'Linux') {
        $uptime = file_get_contents('/proc/uptime');
        if ($uptime) {
            $seconds = floatval(explode(' ', $uptime)[0]);
            return formatUptime($seconds);
        }
    }
    return 'غير متاح';
}

function formatUptime($seconds) {
    $days = floor($seconds / 86400);
    $hours = floor(($seconds % 86400) / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    
    return "{$days}d {$hours}h {$minutes}m";
}

function getFailedLogins($db) {
    try {
        return $db->query("
            SELECT COUNT(*) 
            FROM audit_logs 
            WHERE action = 'failed_login' 
            AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ")->fetchColumn();
    } catch (Exception $e) {
        return 0;
    }
}

function getSuspiciousActivities($db) {
    try {
        return $db->query("
            SELECT COUNT(*) 
            FROM audit_logs 
            WHERE action IN ('multiple_failed_login', 'suspicious_access') 
            AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ")->fetchColumn();
    } catch (Exception $e) {
        return 0;
    }
}

function getSecurityAlerts($db) {
    try {
        return $db->query("
            SELECT COUNT(*) 
            FROM audit_logs 
            WHERE action LIKE '%security%' 
            AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ")->fetchColumn();
    } catch (Exception $e) {
        return 0;
    }
}

function checkSSLStatus() {
    return isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
}

function checkFirewallStatus() {
    // This would need actual firewall integration
    return true;
}

function clearSystemLogs($db) {
    try {
        $db->exec("DELETE FROM audit_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
        return true;
    } catch (Exception $e) {
        return false;
    }
}

function restartService($service) {
    // This would need actual service management integration
    // For demo purposes, always return true
    return true;
}

// Get current system overview
$system_overview = [
    'total_users' => $db->query("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL")->fetchColumn(),
    'active_transfers' => $db->query("SELECT COUNT(*) FROM transfers WHERE status IN ('pending', 'processing') AND deleted_at IS NULL")->fetchColumn(),
    'system_load' => 'Normal',
    'last_backup' => date('Y-m-d H:i:s', strtotime('-2 hours'))
];

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراقبة النظام - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
        }
        
        .stats-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stats-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .monitor-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .monitor-card-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .monitor-card-body {
            padding: 20px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-healthy { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .progress-custom {
            height: 8px;
            border-radius: 4px;
        }
        
        .log-entry {
            padding: 8px 12px;
            margin-bottom: 5px;
            border-radius: 5px;
            background: #f8f9fa;
            font-size: 0.9em;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-1">
                        <i class="bi bi-activity me-2"></i>
                        مراقبة النظام
                    </h1>
                    <p class="mb-0 opacity-75">مراقبة شاملة لأداء وصحة النظام</p>
                    <small class="opacity-50">مرحباً <?= htmlspecialchars($userName) ?></small>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light me-2" onclick="refreshAllData()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        تحديث شامل
                    </button>
                    <button class="btn btn-warning me-2" onclick="clearLogs()">
                        <i class="bi bi-trash me-1"></i>
                        مسح السجلات
                    </button>
                    <div class="btn-group">
                        <button class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="bi bi-tools me-1"></i>
                            أدوات النظام
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="restartService('database')">إعادة تشغيل قاعدة البيانات</a></li>
                            <li><a class="dropdown-item" href="#" onclick="restartService('web')">إعادة تشغيل الخادم</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="exportSystemReport()">تصدير تقرير النظام</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- System Overview -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($system_overview['total_users']) ?></div>
                        <div class="stats-label">إجمالي المستخدمين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($system_overview['active_transfers']) ?></div>
                        <div class="stats-label">التحويلات النشطة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= $system_overview['system_load'] ?></div>
                        <div class="stats-label">حمولة النظام</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number" id="systemUptime">جاري التحميل...</div>
                        <div class="stats-label">وقت التشغيل</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- System Status -->
            <div class="row">
                <div class="col-md-6">
                    <div class="monitor-card">
                        <div class="monitor-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-server me-2"></i>
                                حالة النظام
                                <span class="status-indicator status-healthy" id="systemStatusIndicator"></span>
                            </h5>
                        </div>
                        <div class="monitor-card-body">
                            <div id="systemStatusContent">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p class="mt-2">جاري تحميل حالة النظام...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="monitor-card">
                        <div class="monitor-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-speedometer2 me-2"></i>
                                مقاييس الأداء
                                <span class="status-indicator status-healthy" id="performanceStatusIndicator"></span>
                            </h5>
                        </div>
                        <div class="monitor-card-body">
                            <div id="performanceContent">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p class="mt-2">جاري تحميل مقاييس الأداء...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security and Logs -->
            <div class="row">
                <div class="col-md-6">
                    <div class="monitor-card">
                        <div class="monitor-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-shield-check me-2"></i>
                                حالة الأمان
                                <span class="status-indicator status-healthy" id="securityStatusIndicator"></span>
                            </h5>
                        </div>
                        <div class="monitor-card-body">
                            <div id="securityContent">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p class="mt-2">جاري تحميل حالة الأمان...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="monitor-card">
                        <div class="monitor-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-journal-text me-2"></i>
                                السجلات الحديثة
                            </h5>
                        </div>
                        <div class="monitor-card-body">
                            <div id="logsContent" style="max-height: 300px; overflow-y: auto;">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p class="mt-2">جاري تحميل السجلات...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resource Usage -->
            <div class="row">
                <div class="col-md-12">
                    <div class="monitor-card">
                        <div class="monitor-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-cpu me-2"></i>
                                استخدام الموارد
                            </h5>
                        </div>
                        <div class="monitor-card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>استخدام القرص الصلب</h6>
                                    <div class="progress progress-custom mb-2">
                                        <div class="progress-bar" id="diskUsageBar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="diskUsageText">جاري التحميل...</small>
                                </div>
                                <div class="col-md-4">
                                    <h6>استخدام الذاكرة</h6>
                                    <div class="progress progress-custom mb-2">
                                        <div class="progress-bar bg-warning" id="memoryUsageBar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="memoryUsageText">جاري التحميل...</small>
                                </div>
                                <div class="col-md-4">
                                    <h6>استجابة قاعدة البيانات</h6>
                                    <div class="progress progress-custom mb-2">
                                        <div class="progress-bar bg-info" id="dbResponseBar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="dbResponseText">جاري التحميل...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Refresh Button -->
    <button class="btn btn-primary btn-lg refresh-btn" onclick="refreshAllData()" title="تحديث البيانات">
        <i class="bi bi-arrow-clockwise"></i>
    </button>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        let refreshInterval;

        $(document).ready(function() {
            refreshAllData();

            // Auto refresh every 30 seconds
            refreshInterval = setInterval(refreshAllData, 30000);
        });

        function refreshAllData() {
            updateSystemStatus();
            updatePerformanceMetrics();
            updateSecurityStatus();
            updateResourceUsage();
        }

        function updateSystemStatus() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_system_status' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const status = response.status;
                        displaySystemStatus(status);
                        updateSystemIndicator(status.database.status);
                        displayRecentLogs(status.logs);
                        updateResourceBars(status);
                    }
                },
                error: function() {
                    $('#systemStatusContent').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            خطأ في تحميل حالة النظام
                        </div>
                    `);
                    updateSystemIndicator('error');
                }
            });
        }

        function displaySystemStatus(status) {
            const content = `
                <div class="metric-item">
                    <span><i class="bi bi-database me-2"></i>قاعدة البيانات</span>
                    <span class="badge bg-${status.database.status === 'healthy' ? 'success' : 'danger'}">
                        ${status.database.status === 'healthy' ? 'صحية' : 'خطأ'}
                    </span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-clock me-2"></i>زمن الاستجابة</span>
                    <span class="text-muted">${status.database.response_time || 0}ms</span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-table me-2"></i>عدد الجداول</span>
                    <span class="text-muted">${status.database.table_count || 0}</span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-hdd me-2"></i>مساحة القرص</span>
                    <span class="text-muted">${formatBytes(status.disk_space.free)} متاح</span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-memory me-2"></i>الذاكرة المستخدمة</span>
                    <span class="text-muted">${formatBytes(status.memory.current)}</span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-code me-2"></i>إصدار PHP</span>
                    <span class="text-muted">${status.php.version}</span>
                </div>
            `;
            $('#systemStatusContent').html(content);
        }

        function updatePerformanceMetrics() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_performance_metrics' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const metrics = response.metrics;
                        displayPerformanceMetrics(metrics);
                        updatePerformanceIndicator(metrics);
                        $('#systemUptime').text(metrics.uptime || 'غير متاح');
                    }
                },
                error: function() {
                    $('#performanceContent').html(`
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            خطأ في تحميل مقاييس الأداء
                        </div>
                    `);
                    updatePerformanceIndicator({ error_rate: 100 });
                }
            });
        }

        function displayPerformanceMetrics(metrics) {
            const content = `
                <div class="metric-item">
                    <span><i class="bi bi-speedometer me-2"></i>زمن الاستجابة</span>
                    <span class="text-muted">${metrics.response_time}ms</span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-search me-2"></i>إجمالي الاستعلامات</span>
                    <span class="text-muted">${formatNumber(metrics.database_queries.total_queries)}</span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-people me-2"></i>الجلسات النشطة</span>
                    <span class="text-muted">${metrics.active_sessions}</span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-exclamation-triangle me-2"></i>معدل الأخطاء</span>
                    <span class="badge bg-${metrics.error_rate > 5 ? 'danger' : 'success'}">${metrics.error_rate}%</span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-clock-history me-2"></i>وقت التشغيل</span>
                    <span class="text-muted">${metrics.uptime}</span>
                </div>
            `;
            $('#performanceContent').html(content);
        }

        function updateSecurityStatus() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_security_status' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const security = response.security;
                        displaySecurityStatus(security);
                        updateSecurityIndicator(security);
                    }
                },
                error: function() {
                    $('#securityContent').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-shield-x me-2"></i>
                            خطأ في تحميل حالة الأمان
                        </div>
                    `);
                    updateSecurityIndicator({ failed_logins: 999 });
                }
            });
        }

        function displaySecurityStatus(security) {
            const content = `
                <div class="metric-item">
                    <span><i class="bi bi-shield-x me-2"></i>محاولات دخول فاشلة (24س)</span>
                    <span class="badge bg-${security.failed_logins > 10 ? 'danger' : 'success'}">${security.failed_logins}</span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-exclamation-triangle me-2"></i>أنشطة مشبوهة</span>
                    <span class="badge bg-${security.suspicious_activities > 0 ? 'warning' : 'success'}">${security.suspicious_activities}</span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-bell me-2"></i>تنبيهات أمنية</span>
                    <span class="badge bg-${security.security_alerts > 0 ? 'danger' : 'success'}">${security.security_alerts}</span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-lock me-2"></i>حالة SSL</span>
                    <span class="badge bg-${security.ssl_status ? 'success' : 'danger'}">
                        ${security.ssl_status ? 'مفعل' : 'غير مفعل'}
                    </span>
                </div>
                <div class="metric-item">
                    <span><i class="bi bi-shield-check me-2"></i>جدار الحماية</span>
                    <span class="badge bg-${security.firewall_status ? 'success' : 'danger'}">
                        ${security.firewall_status ? 'نشط' : 'غير نشط'}
                    </span>
                </div>
            `;
            $('#securityContent').html(content);
        }

        function displayRecentLogs(logs) {
            if (!logs || logs.length === 0) {
                $('#logsContent').html(`
                    <div class="text-center py-4 text-muted">
                        <i class="bi bi-journal-x display-6"></i>
                        <p class="mt-2">لا توجد سجلات حديثة</p>
                    </div>
                `);
                return;
            }

            let content = '';
            logs.forEach(log => {
                content += `
                    <div class="log-entry">
                        <div class="d-flex justify-content-between">
                            <span><i class="bi bi-dot me-1"></i>${log.action}</span>
                            <small class="text-muted">${formatDateTime(log.created_at)}</small>
                        </div>
                        ${log.ip_address ? `<small class="text-muted">IP: ${log.ip_address}</small>` : ''}
                    </div>
                `;
            });
            $('#logsContent').html(content);
        }

        function updateResourceBars(status) {
            if (status.disk_space) {
                const diskPercent = status.disk_space.usage_percent;
                $('#diskUsageBar').css('width', diskPercent + '%');
                $('#diskUsageText').text(`${diskPercent}% مستخدم (${formatBytes(status.disk_space.used)} من ${formatBytes(status.disk_space.total)})`);

                if (diskPercent > 90) {
                    $('#diskUsageBar').removeClass('bg-primary bg-warning').addClass('bg-danger');
                } else if (diskPercent > 70) {
                    $('#diskUsageBar').removeClass('bg-primary bg-danger').addClass('bg-warning');
                } else {
                    $('#diskUsageBar').removeClass('bg-warning bg-danger').addClass('bg-primary');
                }
            }

            if (status.memory) {
                const memoryPercent = Math.min((status.memory.current / parseBytes(status.memory.limit)) * 100, 100);
                $('#memoryUsageBar').css('width', memoryPercent + '%');
                $('#memoryUsageText').text(`${memoryPercent.toFixed(1)}% (${formatBytes(status.memory.current)} من ${status.memory.limit})`);
            }

            if (status.database) {
                const responseTime = status.database.response_time || 0;
                const responsePercent = Math.min((responseTime / 100) * 100, 100); // 100ms = 100%
                $('#dbResponseBar').css('width', responsePercent + '%');
                $('#dbResponseText').text(`${responseTime}ms زمن الاستجابة`);
            }
        }

        function updateSystemIndicator(status) {
            const indicator = $('#systemStatusIndicator');
            indicator.removeClass('status-healthy status-warning status-error');

            if (status === 'healthy') {
                indicator.addClass('status-healthy');
            } else if (status === 'warning') {
                indicator.addClass('status-warning');
            } else {
                indicator.addClass('status-error');
            }
        }

        function updatePerformanceIndicator(metrics) {
            const indicator = $('#performanceStatusIndicator');
            indicator.removeClass('status-healthy status-warning status-error');

            if (metrics.error_rate <= 1) {
                indicator.addClass('status-healthy');
            } else if (metrics.error_rate <= 5) {
                indicator.addClass('status-warning');
            } else {
                indicator.addClass('status-error');
            }
        }

        function updateSecurityIndicator(security) {
            const indicator = $('#securityStatusIndicator');
            indicator.removeClass('status-healthy status-warning status-error');

            if (security.failed_logins <= 5 && security.suspicious_activities === 0) {
                indicator.addClass('status-healthy');
            } else if (security.failed_logins <= 10) {
                indicator.addClass('status-warning');
            } else {
                indicator.addClass('status-error');
            }
        }

        function clearLogs() {
            if (confirm('هل أنت متأكد من مسح السجلات القديمة؟')) {
                $.ajax({
                    url: '',
                    method: 'POST',
                    data: { action: 'clear_logs' },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.message, 'success');
                            refreshAllData();
                        } else {
                            showAlert(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showAlert('خطأ في مسح السجلات', 'danger');
                    }
                });
            }
        }

        function restartService(service) {
            if (confirm(`هل أنت متأكد من إعادة تشغيل ${service}؟`)) {
                $.ajax({
                    url: '',
                    method: 'POST',
                    data: {
                        action: 'restart_service',
                        service: service
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.message, 'success');
                            setTimeout(refreshAllData, 2000);
                        } else {
                            showAlert(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showAlert('خطأ في إعادة تشغيل الخدمة', 'danger');
                    }
                });
            }
        }

        function exportSystemReport() {
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const reportData = {
                timestamp: timestamp,
                system_status: 'تم تصدير التقرير بنجاح',
                note: 'هذه ميزة تجريبية'
            };

            const dataStr = JSON.stringify(reportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `system_report_${timestamp}.json`;
            link.click();

            showAlert('تم تصدير تقرير النظام بنجاح', 'success');
        }

        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);

            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function parseBytes(str) {
            const units = { 'K': 1024, 'M': 1024*1024, 'G': 1024*1024*1024 };
            const match = str.match(/^(\d+)([KMG]?)$/i);
            if (match) {
                return parseInt(match[1]) * (units[match[2].toUpperCase()] || 1);
            }
            return parseInt(str) || 0;
        }

        function formatNumber(num) {
            return parseFloat(num).toLocaleString('en-US');
        }

        function formatDateTime(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString('ar-SA', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Cleanup on page unload
        $(window).on('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
