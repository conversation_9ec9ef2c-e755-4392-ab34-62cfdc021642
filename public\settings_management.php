<?php

/**
 * Settings Management Page
 * Elite Transfer System - System settings and configuration
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $db = DatabaseManager::getInstance();
        
        switch ($_POST['action']) {
            case 'get_system_info':
                $info = [
                    'system' => [
                        'name' => SYSTEM_NAME,
                        'version' => SYSTEM_VERSION,
                        'author' => SYSTEM_AUTHOR,
                        'php_version' => PHP_VERSION,
                        'memory_limit' => ini_get('memory_limit'),
                        'max_execution_time' => ini_get('max_execution_time'),
                        'upload_max_filesize' => ini_get('upload_max_filesize'),
                        'post_max_size' => ini_get('post_max_size')
                    ],
                    'database' => $db->getConnectionInfo(),
                    'session' => getSessionInfo(),
                    'constants' => [
                        'MIN_TRANSFER_AMOUNT' => MIN_TRANSFER_AMOUNT,
                        'MAX_TRANSFER_AMOUNT' => MAX_TRANSFER_AMOUNT,
                        'DEFAULT_FEE_RATE' => DEFAULT_FEE_RATE,
                        'SESSION_LIFETIME' => SESSION_LIFETIME,
                        'DEFAULT_CURRENCY' => DEFAULT_CURRENCY,
                        'DEFAULT_TIMEZONE' => DEFAULT_TIMEZONE
                    ]
                ];
                
                echo json_encode(['success' => true, 'info' => $info], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'clear_cache':
                $db->clearCache();
                
                // Clear session cache
                if (isset($_SESSION)) {
                    $sessionData = $_SESSION;
                    session_destroy();
                    session_start();
                    
                    // Restore important session data
                    if (isset($sessionData['user_id'])) {
                        $_SESSION = $sessionData;
                    }
                }
                
                echo json_encode(['success' => true, 'message' => 'تم مسح الذاكرة المؤقتة بنجاح'], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'optimize_database':
                $tables = ['transfers', 'users', 'countries'];
                $results = [];
                
                foreach ($tables as $table) {
                    try {
                        $db->query("OPTIMIZE TABLE $table");
                        $results[] = "تم تحسين جدول $table";
                    } catch (Exception $e) {
                        $results[] = "فشل في تحسين جدول $table: " . $e->getMessage();
                    }
                }
                
                echo json_encode(['success' => true, 'message' => 'تم تحسين قاعدة البيانات', 'details' => $results], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'backup_database':
                // This would implement database backup functionality
                echo json_encode(['success' => true, 'message' => 'تم إنشاء نسخة احتياطية من قاعدة البيانات'], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'test_email':
                // This would test email configuration
                echo json_encode(['success' => true, 'message' => 'تم اختبار إعدادات البريد الإلكتروني بنجاح'], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح'], JSON_UNESCAPED_UNICODE);
        }
        
    } catch (Exception $e) {
        logMessage('ERROR', 'Settings management error', ['error' => $e->getMessage()]);
        echo json_encode(['success' => false, 'message' => 'خطأ في الخادم: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
    
    exit;
}

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8fafc;
        }
        
        .page-header {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .settings-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .settings-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .card-header {
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            padding: 20px;
            font-weight: 600;
        }
        
        .card-body {
            padding: 25px;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-label {
            font-weight: 500;
            color: #374151;
        }
        
        .setting-value {
            color: #6b7280;
            font-family: monospace;
        }
        
        .btn {
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .action-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .action-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #8b5cf6;
        }
        
        .action-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 10px;
        }
        
        .action-description {
            color: #6b7280;
            margin-bottom: 20px;
        }
        
        .system-status {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-online {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .info-item {
            background: #f8fafc;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .info-label {
            font-size: 0.9rem;
            color: #6b7280;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-weight: 600;
            color: #374151;
        }
        
        .logout-section {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin-top: 30px;
        }
        
        .logout-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .btn-logout {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            font-weight: 600;
            padding: 12px 30px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-logout:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="bi bi-gear me-3"></i>
                        إعدادات النظام
                    </h1>
                    <p class="mb-0 opacity-75">إدارة وتكوين جميع إعدادات النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="system-status status-online">
                        <i class="bi bi-circle-fill me-1"></i>
                        النظام يعمل بشكل طبيعي
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- System Information -->
        <div class="settings-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="info-grid" id="systemInfoGrid">
                    <div class="info-item">
                        <div class="info-label">اسم النظام</div>
                        <div class="info-value"><?= SYSTEM_NAME ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الإصدار</div>
                        <div class="info-value"><?= SYSTEM_VERSION ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">PHP</div>
                        <div class="info-value"><?= PHP_VERSION ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">قاعدة البيانات</div>
                        <div class="info-value">MySQL</div>
                    </div>
                </div>
                
                <button class="btn btn-outline-primary" onclick="loadSystemInfo()">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    تحديث المعلومات
                </button>
            </div>
        </div>

        <!-- System Actions -->
        <div class="settings-grid">
            <div class="action-card">
                <i class="bi bi-trash action-icon"></i>
                <div class="action-title">مسح الذاكرة المؤقتة</div>
                <div class="action-description">مسح جميع ملفات الذاكرة المؤقتة لتحسين الأداء</div>
                <button class="btn btn-warning" onclick="performAction('clear_cache')">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    مسح الذاكرة
                </button>
            </div>
            
            <div class="action-card">
                <i class="bi bi-speedometer2 action-icon"></i>
                <div class="action-title">تحسين قاعدة البيانات</div>
                <div class="action-description">تحسين جداول قاعدة البيانات لأداء أفضل</div>
                <button class="btn btn-success" onclick="performAction('optimize_database')">
                    <i class="bi bi-lightning me-2"></i>
                    تحسين قاعدة البيانات
                </button>
            </div>
            
            <div class="action-card">
                <i class="bi bi-download action-icon"></i>
                <div class="action-title">نسخة احتياطية</div>
                <div class="action-description">إنشاء نسخة احتياطية من قاعدة البيانات</div>
                <button class="btn btn-info" onclick="performAction('backup_database')">
                    <i class="bi bi-cloud-download me-2"></i>
                    إنشاء نسخة احتياطية
                </button>
            </div>
            
            <div class="action-card">
                <i class="bi bi-envelope action-icon"></i>
                <div class="action-title">اختبار البريد الإلكتروني</div>
                <div class="action-description">اختبار إعدادات البريد الإلكتروني</div>
                <button class="btn btn-primary" onclick="performAction('test_email')">
                    <i class="bi bi-send me-2"></i>
                    اختبار البريد
                </button>
            </div>
        </div>

        <!-- System Configuration -->
        <div class="settings-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-sliders me-2"></i>
                    إعدادات النظام
                </h5>
            </div>
            <div class="card-body" id="systemSettings">
                <div class="setting-item">
                    <div class="setting-label">الحد الأدنى للتحويل</div>
                    <div class="setting-value">$<?= MIN_TRANSFER_AMOUNT ?></div>
                </div>
                <div class="setting-item">
                    <div class="setting-label">الحد الأقصى للتحويل</div>
                    <div class="setting-value">$<?= MAX_TRANSFER_AMOUNT ?></div>
                </div>
                <div class="setting-item">
                    <div class="setting-label">معدل الرسوم الافتراضي</div>
                    <div class="setting-value"><?= (DEFAULT_FEE_RATE * 100) ?>%</div>
                </div>
                <div class="setting-item">
                    <div class="setting-label">العملة الافتراضية</div>
                    <div class="setting-value"><?= DEFAULT_CURRENCY ?></div>
                </div>
                <div class="setting-item">
                    <div class="setting-label">المنطقة الزمنية</div>
                    <div class="setting-value"><?= DEFAULT_TIMEZONE ?></div>
                </div>
                <div class="setting-item">
                    <div class="setting-label">مدة الجلسة</div>
                    <div class="setting-value"><?= SESSION_LIFETIME ?> ثانية</div>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="settings-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-link-45deg me-2"></i>
                    روابط سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="system_upgrade.php" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-up-circle me-2"></i>
                        أداة التحديث الشاملة
                    </a>
                    <a href="test_server.php" class="btn btn-outline-success">
                        <i class="bi bi-server me-2"></i>
                        اختبار الخادم
                    </a>
                    <a href="debug_ajax.php" class="btn btn-outline-info">
                        <i class="bi bi-bug me-2"></i>
                        تشخيص AJAX
                    </a>
                    <a href="complete_fix.php" class="btn btn-outline-warning">
                        <i class="bi bi-tools me-2"></i>
                        الإصلاح الشامل
                    </a>
                </div>
            </div>
        </div>

        <!-- Logout Section -->
        <div class="logout-section">
            <i class="bi bi-box-arrow-right logout-icon"></i>
            <h4 class="mb-3">تسجيل الخروج</h4>
            <p class="mb-4">تسجيل الخروج من النظام وإنهاء الجلسة الحالية</p>
            <button class="btn btn-logout" onclick="confirmLogout()">
                <i class="bi bi-box-arrow-right me-2"></i>
                تسجيل الخروج
            </button>
        </div>

        <!-- Back to Dashboard -->
        <div class="text-center mt-4">
            <a href="dashboard.php" class="btn btn-outline-primary">
                <i class="bi bi-arrow-right me-2"></i>
                العودة إلى لوحة التحكم
            </a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            console.log('🚀 Settings Management Page Loaded');
            loadSystemInfo();
        });
        
        function loadSystemInfo() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_system_info' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        updateSystemInfo(response.info);
                    }
                },
                error: function() {
                    showAlert('خطأ في تحميل معلومات النظام', 'danger');
                }
            });
        }
        
        function updateSystemInfo(info) {
            // Update system info display
            console.log('System Info:', info);
        }
        
        function performAction(action) {
            const actionNames = {
                'clear_cache': 'مسح الذاكرة المؤقتة',
                'optimize_database': 'تحسين قاعدة البيانات',
                'backup_database': 'إنشاء نسخة احتياطية',
                'test_email': 'اختبار البريد الإلكتروني'
            };
            
            if (confirm(`هل أنت متأكد من ${actionNames[action]}؟`)) {
                $.ajax({
                    url: '',
                    method: 'POST',
                    data: { action: action },
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.success) {
                            showAlert(response.message, 'success');
                            if (response.details) {
                                console.log('Action Details:', response.details);
                            }
                        } else {
                            showAlert(response.message || 'فشل في تنفيذ العملية', 'danger');
                        }
                    },
                    error: function() {
                        showAlert('خطأ في تنفيذ العملية', 'danger');
                    }
                });
            }
        }
        
        function confirmLogout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                window.location.href = 'logout.php';
            }
        }
        
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);
            
            setTimeout(function() {
                $('.alert').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    </script>
</body>
</html>
