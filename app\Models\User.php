<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'role',
        'branch_id',
        'country_id',
        'national_id',
        'address',
        'date_of_birth',
        'gender',
        'profile_photo',
        'is_active',
        'email_verified_at',
        'phone_verified_at',
        'two_factor_enabled',
        'two_factor_secret',
        'last_login_at',
        'login_attempts',
        'locked_until',
        'preferred_language',
        'preferred_currency',
        'kyc_status',
        'kyc_documents',
        'risk_score',
        'agent_code',
        'commission_rate',
        'daily_limit',
        'monthly_limit',
        'total_transfers_sent',
        'total_transfers_received',
        'total_amount_sent',
        'total_amount_received',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'password' => 'hashed',
        'date_of_birth' => 'date',
        'is_active' => 'boolean',
        'two_factor_enabled' => 'boolean',
        'last_login_at' => 'datetime',
        'locked_until' => 'datetime',
        'kyc_documents' => 'array',
        'commission_rate' => 'decimal:2',
        'daily_limit' => 'decimal:2',
        'monthly_limit' => 'decimal:2',
        'total_amount_sent' => 'decimal:2',
        'total_amount_received' => 'decimal:2',
        'risk_score' => 'integer',
    ];

    /**
     * User roles constants
     */
    const ROLE_ADMIN = 'admin';
    const ROLE_AGENT = 'agent';
    const ROLE_CUSTOMER = 'customer';
    const ROLE_SUPER_ADMIN = 'super_admin';

    /**
     * KYC Status constants
     */
    const KYC_PENDING = 'pending';
    const KYC_APPROVED = 'approved';
    const KYC_REJECTED = 'rejected';
    const KYC_UNDER_REVIEW = 'under_review';

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return in_array($this->role, [self::ROLE_ADMIN, self::ROLE_SUPER_ADMIN]);
    }

    /**
     * Check if user is agent
     */
    public function isAgent(): bool
    {
        return $this->role === self::ROLE_AGENT;
    }

    /**
     * Check if user is customer
     */
    public function isCustomer(): bool
    {
        return $this->role === self::ROLE_CUSTOMER;
    }

    /**
     * Get the branch that the user belongs to
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the country that the user belongs to
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get transfers sent by this user
     */
    public function transfersSent(): HasMany
    {
        return $this->hasMany(Transfer::class, 'sender_id');
    }

    /**
     * Get transfers received by this user
     */
    public function transfersReceived(): HasMany
    {
        return $this->hasMany(Transfer::class, 'receiver_id');
    }

    /**
     * Get transfers handled by this agent
     */
    public function transfersHandled(): HasMany
    {
        return $this->hasMany(Transfer::class, 'agent_id');
    }

    /**
     * Get audit logs for this user
     */
    public function auditLogs(): HasMany
    {
        return $this->hasMany(AuditLog::class);
    }

    /**
     * Get OTP codes for this user
     */
    public function otpCodes(): HasMany
    {
        return $this->hasMany(OtpCode::class);
    }

    /**
     * Generate agent code
     */
    public function generateAgentCode(): string
    {
        $code = 'AG' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
        $this->update(['agent_code' => $code]);
        return $code;
    }

    /**
     * Check if user can send transfer
     */
    public function canSendTransfer(float $amount): bool
    {
        if (!$this->is_active || $this->kyc_status !== self::KYC_APPROVED) {
            return false;
        }

        // Check daily limit
        $todayTransfers = $this->transfersSent()
            ->whereDate('created_at', today())
            ->sum('amount');

        if ($todayTransfers + $amount > $this->daily_limit) {
            return false;
        }

        // Check monthly limit
        $monthlyTransfers = $this->transfersSent()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');

        if ($monthlyTransfers + $amount > $this->monthly_limit) {
            return false;
        }

        return true;
    }

    /**
     * Update risk score based on activity
     */
    public function updateRiskScore(): void
    {
        $score = 0;

        // Base score based on verification status
        if ($this->kyc_status === self::KYC_APPROVED) {
            $score += 20;
        }

        if ($this->email_verified_at) {
            $score += 10;
        }

        if ($this->phone_verified_at) {
            $score += 10;
        }

        if ($this->two_factor_enabled) {
            $score += 15;
        }

        // Score based on transfer history
        $transferCount = $this->transfersSent()->count();
        $score += min($transferCount * 2, 30);

        // Account age bonus
        $accountAge = $this->created_at->diffInDays(now());
        $score += min($accountAge / 10, 15);

        $this->update(['risk_score' => min($score, 100)]);
    }
}
