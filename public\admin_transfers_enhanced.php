<?php

// Elite Transfer System - Enhanced Admin Transfers Management
// Complete functionality with all features

// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

// Load database manager
require_once __DIR__ . '/includes/database_manager.php';

try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    // Ensure we always return JSON
    header('Content-Type: application/json');

    // Capture any output that might interfere
    ob_start();

    try {
        switch ($_POST['action']) {
        case 'get_transfers':
            $page = intval($_POST['page'] ?? 1);
            $limit = intval($_POST['limit'] ?? 10);
            $search = $_POST['search'] ?? '';
            $status_filter = $_POST['status_filter'] ?? '';
            $date_from = $_POST['date_from'] ?? '';
            $date_to = $_POST['date_to'] ?? '';
            
            $offset = ($page - 1) * $limit;
            
            // Build WHERE clause
            $where = ["t.deleted_at IS NULL"];
            $params = [];
            
            if ($search) {
                $where[] = "(t.transfer_code LIKE ? OR t.pickup_code LIKE ? OR t.sender_name LIKE ? OR t.recipient_name LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }
            
            if ($status_filter) {
                $where[] = "t.status = ?";
                $params[] = $status_filter;
            }
            
            if ($date_from) {
                $where[] = "DATE(t.created_at) >= ?";
                $params[] = $date_from;
            }
            
            if ($date_to) {
                $where[] = "DATE(t.created_at) <= ?";
                $params[] = $date_to;
            }
            
            $whereClause = implode(' AND ', $where);
            
            // Get total count
            $countQuery = "SELECT COUNT(*) FROM transfers t WHERE $whereClause";
            $stmt = $db->prepare($countQuery);
            $stmt->execute($params);
            $total = $stmt->fetchColumn();
            
            // Get transfers
            $query = "
                SELECT t.*,
                       COALESCE(u.name, 'غير محدد') as user_name,
                       COALESCE(sc.name, 'غير محدد') as sender_country,
                       COALESCE(rc.name, 'غير محدد') as recipient_country
                FROM transfers t
                LEFT JOIN users u ON t.user_id = u.id
                LEFT JOIN countries sc ON t.sender_country_id = sc.id
                LEFT JOIN countries rc ON t.recipient_country_id = rc.id
                WHERE $whereClause
                ORDER BY t.created_at DESC
                LIMIT $limit OFFSET $offset
            ";
            
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            ob_clean(); // Clear any output
            echo json_encode([
                'success' => true,
                'transfers' => $transfers,
                'total' => $total,
                'page' => $page,
                'pages' => ceil($total / $limit)
            ]);
            exit;
            
        case 'update_transfer_status':
            $id = intval($_POST['id']);
            $status = $_POST['status'] ?? '';
            $notes = $_POST['notes'] ?? '';
            
            // Update transfer status
            $stmt = $db->prepare("
                UPDATE transfers 
                SET status = ?, updated_at = NOW()
                WHERE id = ? AND deleted_at IS NULL
            ");
            
            $result = $stmt->execute([$status, $id]);
            
            if ($result) {
                // Add to status history if table exists
                try {
                    $stmt = $db->prepare("
                        INSERT INTO transfer_status_histories (transfer_id, status, notes, changed_by, created_at)
                        VALUES (?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([$id, $status, $notes, $_SESSION['user_id']]);
                } catch (Exception $e) {
                    // Table might not exist, continue
                }

                ob_clean(); // Clear any output
                echo json_encode(['success' => true, 'message' => 'تم تحديث حالة التحويل بنجاح']);
            } else {
                ob_clean(); // Clear any output
                echo json_encode(['success' => false, 'message' => 'فشل في تحديث حالة التحويل']);
            }
            exit;
            
        case 'get_transfer':
            $id = intval($_POST['id']);
            $stmt = $db->prepare("
                SELECT t.*, 
                       u.name as user_name, u.email as user_email,
                       sc.name as sender_country,
                       rc.name as recipient_country
                FROM transfers t
                LEFT JOIN users u ON t.user_id = u.id
                LEFT JOIN countries sc ON t.sender_country_id = sc.id
                LEFT JOIN countries rc ON t.recipient_country_id = rc.id
                WHERE t.id = ? AND t.deleted_at IS NULL
            ");
            $stmt->execute([$id]);
            $transfer = $stmt->fetch(PDO::FETCH_ASSOC);
            
            ob_clean(); // Clear any output
            if ($transfer) {
                echo json_encode(['success' => true, 'transfer' => $transfer]);
            } else {
                echo json_encode(['success' => false, 'message' => 'التحويل غير موجود']);
            }
            exit;

        default:
            ob_clean();
            echo json_encode(['success' => false, 'message' => 'إجراء غير معروف: ' . $_POST['action']]);
            exit;
        }

    } catch (Exception $e) {
        ob_clean();
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في الخادم: ' . $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        exit;
    }
}

// Get statistics
$stats = [
    'total_transfers' => $db->query("SELECT COUNT(*) FROM transfers WHERE deleted_at IS NULL")->fetchColumn(),
    'pending_transfers' => $db->query("SELECT COUNT(*) FROM transfers WHERE status = 'pending' AND deleted_at IS NULL")->fetchColumn(),
    'completed_transfers' => $db->query("SELECT COUNT(*) FROM transfers WHERE status = 'completed' AND deleted_at IS NULL")->fetchColumn(),
    'total_amount' => $db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE status = 'completed' AND deleted_at IS NULL")->fetchColumn(),
    'today_transfers' => $db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = CURDATE() AND deleted_at IS NULL")->fetchColumn(),
    'today_amount' => $db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND deleted_at IS NULL")->fetchColumn()
];

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التحويلات - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
        }
        
        .stats-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stats-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-pending { background: #fff3cd; color: #664d03; }
        .status-processing { background: #cff4fc; color: #055160; }
        .status-completed { background: #d1e7dd; color: #0f5132; }
        .status-cancelled { background: #f8d7da; color: #842029; }
        .status-failed { background: #f8d7da; color: #842029; }
        
        .transfer-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #0066cc;
        }
        
        .amount-display {
            font-weight: bold;
            color: #28a745;
        }
        
        .btn-action {
            margin: 2px;
            padding: 5px 10px;
            font-size: 0.8em;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box .bi-search {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        
        .search-box input {
            padding-left: 40px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-1">
                        <i class="bi bi-arrow-left-right me-2"></i>
                        إدارة التحويلات
                    </h1>
                    <p class="mb-0 opacity-75">إدارة شاملة لجميع التحويلات المالية</p>
                    <small class="opacity-50">مرحباً <?= htmlspecialchars($userName) ?></small>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light me-2" onclick="loadTransfers()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        تحديث
                    </button>
                    <a href="transfers-create.php" class="btn btn-success">
                        <i class="bi bi-plus-circle me-1"></i>
                        تحويل جديد
                    </a>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mt-4">
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['total_transfers']) ?></div>
                        <div class="stats-label">إجمالي التحويلات</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['pending_transfers']) ?></div>
                        <div class="stats-label">في الانتظار</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['completed_transfers']) ?></div>
                        <div class="stats-label">مكتملة</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number">$<?= number_format($stats['total_amount'], 0) ?></div>
                        <div class="stats-label">إجمالي المبلغ</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['today_transfers']) ?></div>
                        <div class="stats-label">تحويلات اليوم</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number">$<?= number_format($stats['today_amount'], 0) ?></div>
                        <div class="stats-label">مبلغ اليوم</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Filters -->
            <div class="filter-section">
                <div class="row">
                    <div class="col-md-3">
                        <div class="search-box">
                            <i class="bi bi-search"></i>
                            <input type="text" class="form-control" id="searchInput" placeholder="البحث برمز التحويل أو الاسم...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="pending">في الانتظار</option>
                            <option value="processing">قيد المعالجة</option>
                            <option value="completed">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                            <option value="failed">فاشل</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="dateFrom" placeholder="من تاريخ">
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="dateTo" placeholder="إلى تاريخ">
                    </div>
                    <div class="col-md-1">
                        <select class="form-select" id="limitSelect">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary w-100" onclick="exportTransfers()">
                            <i class="bi bi-download me-1"></i>
                            تصدير
                        </button>
                    </div>
                </div>
            </div>

            <!-- Transfers Table -->
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>رمز التحويل</th>
                                <th>المرسل</th>
                                <th>المستلم</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>طريقة الدفع</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transfersTableBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center p-3">
                    <div id="tableInfo" class="text-muted"></div>
                    <nav>
                        <ul class="pagination mb-0" id="pagination"></ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Details Modal -->
    <div class="modal fade" id="transferDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-info-circle me-2"></i>
                        تفاصيل التحويل
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="transferDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal fade" id="updateStatusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-arrow-repeat me-2"></i>
                        تحديث حالة التحويل
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="updateStatusForm">
                        <input type="hidden" id="updateTransferId" name="id">
                        <div class="form-floating mb-3">
                            <select class="form-select" id="newStatus" name="status" required>
                                <option value="pending">في الانتظار</option>
                                <option value="processing">قيد المعالجة</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                                <option value="failed">فاشل</option>
                            </select>
                            <label for="newStatus">الحالة الجديدة</label>
                        </div>
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="statusNotes" name="notes" style="height: 100px"></textarea>
                            <label for="statusNotes">ملاحظات (اختياري)</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="updateTransferStatus()">
                        <i class="bi bi-check-lg me-1"></i>
                        تحديث الحالة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        let currentPage = 1;
        let currentLimit = 10;
        let currentSearch = '';
        let currentStatusFilter = '';
        let currentDateFrom = '';
        let currentDateTo = '';

        // Load transfers on page load
        $(document).ready(function() {
            loadTransfers();

            // Search functionality
            $('#searchInput').on('input', function() {
                currentSearch = $(this).val();
                currentPage = 1;
                loadTransfers();
            });

            // Filter functionality
            $('#statusFilter, #dateFrom, #dateTo').on('change', function() {
                currentStatusFilter = $('#statusFilter').val();
                currentDateFrom = $('#dateFrom').val();
                currentDateTo = $('#dateTo').val();
                currentPage = 1;
                loadTransfers();
            });

            // Limit functionality
            $('#limitSelect').on('change', function() {
                currentLimit = parseInt($(this).val());
                currentPage = 1;
                loadTransfers();
            });
        });

        function loadTransfers() {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_transfers',
                    page: currentPage,
                    limit: currentLimit,
                    search: currentSearch,
                    status_filter: currentStatusFilter,
                    date_from: currentDateFrom,
                    date_to: currentDateTo
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        displayTransfers(response.transfers);
                        updatePagination(response.page, response.pages, response.total);
                    } else {
                        showAlert('خطأ في تحميل البيانات', 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    console.error('Status:', status);
                    console.error('Response:', xhr.responseText);

                    let errorMessage = 'خطأ في الاتصال بالخادم';
                    if (xhr.responseText) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMessage = response.message || errorMessage;
                        } catch (e) {
                            errorMessage += ': ' + error;
                        }
                    }

                    showAlert(errorMessage, 'danger');
                    $('#transfersTableBody').html(`
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="alert alert-danger">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    ${errorMessage}
                                    <br>
                                    <button class="btn btn-sm btn-outline-danger mt-2" onclick="loadTransfers()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        إعادة المحاولة
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `);
                }
            });
        }

        function displayTransfers(transfers) {
            const tbody = $('#transfersTableBody');
            tbody.empty();

            if (transfers.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد تحويلات للعرض</p>
                        </td>
                    </tr>
                `);
                return;
            }

            transfers.forEach(transfer => {
                const row = `
                    <tr>
                        <td>
                            <div>
                                <span class="transfer-code">${transfer.transfer_code || ''}</span>
                                <br>
                                <small class="text-muted">رمز الاستلام: ${transfer.pickup_code || ''}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>${transfer.sender_name || ''}</strong>
                                <br>
                                <small class="text-muted">${transfer.sender_country || ''}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>${transfer.recipient_name || ''}</strong>
                                <br>
                                <small class="text-muted">${transfer.recipient_country || ''}</small>
                            </div>
                        </td>
                        <td>
                            <div class="amount-display">
                                $${parseFloat(transfer.amount || 0).toFixed(2)}
                            </div>
                            <small class="text-muted">رسوم: $${parseFloat(transfer.fee || 0).toFixed(2)}</small>
                        </td>
                        <td>
                            <span class="status-badge status-${transfer.status}">
                                ${getStatusText(transfer.status)}
                            </span>
                        </td>
                        <td>${transfer.payment_method || '-'}</td>
                        <td>${formatDate(transfer.created_at)}</td>
                        <td>
                            <button class="btn btn-sm btn-info btn-action" onclick="viewTransfer(${transfer.id})" title="عرض التفاصيل">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-warning btn-action" onclick="updateStatus(${transfer.id})" title="تحديث الحالة">
                                <i class="bi bi-arrow-repeat"></i>
                            </button>
                            <button class="btn btn-sm btn-success btn-action" onclick="printTransfer(${transfer.id})" title="طباعة">
                                <i class="bi bi-printer"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        function updatePagination(page, pages, total) {
            const pagination = $('#pagination');
            pagination.empty();

            const start = ((page - 1) * currentLimit) + 1;
            const end = Math.min(page * currentLimit, total);

            $('#tableInfo').text(`عرض ${start} إلى ${end} من ${total} تحويل`);

            if (pages <= 1) return;

            // Previous button
            if (page > 1) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage(${page - 1})">السابق</a>
                    </li>
                `);
            }

            // Page numbers
            const startPage = Math.max(1, page - 2);
            const endPage = Math.min(pages, page + 2);

            for (let i = startPage; i <= endPage; i++) {
                pagination.append(`
                    <li class="page-item ${i === page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `);
            }

            // Next button
            if (page < pages) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage(${page + 1})">التالي</a>
                    </li>
                `);
            }
        }

        function changePage(page) {
            currentPage = page;
            loadTransfers();
        }

        function viewTransfer(id) {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_transfer',
                    id: id
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const transfer = response.transfer;
                        const detailsHtml = `
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">معلومات التحويل</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>رمز التحويل:</strong></td><td>${transfer.transfer_code || ''}</td></tr>
                                        <tr><td><strong>رمز الاستلام:</strong></td><td>${transfer.pickup_code || ''}</td></tr>
                                        <tr><td><strong>المبلغ:</strong></td><td>$${parseFloat(transfer.amount || 0).toFixed(2)}</td></tr>
                                        <tr><td><strong>الرسوم:</strong></td><td>$${parseFloat(transfer.fee || 0).toFixed(2)}</td></tr>
                                        <tr><td><strong>الحالة:</strong></td><td><span class="status-badge status-${transfer.status}">${getStatusText(transfer.status)}</span></td></tr>
                                        <tr><td><strong>طريقة الدفع:</strong></td><td>${transfer.payment_method || '-'}</td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">معلومات المرسل</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>الاسم:</strong></td><td>${transfer.sender_name || ''}</td></tr>
                                        <tr><td><strong>الهاتف:</strong></td><td>${transfer.sender_phone || ''}</td></tr>
                                        <tr><td><strong>الدولة:</strong></td><td>${transfer.sender_country || ''}</td></tr>
                                        <tr><td><strong>العنوان:</strong></td><td>${transfer.sender_address || ''}</td></tr>
                                    </table>

                                    <h6 class="text-primary mt-3">معلومات المستلم</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>الاسم:</strong></td><td>${transfer.recipient_name || ''}</td></tr>
                                        <tr><td><strong>الهاتف:</strong></td><td>${transfer.recipient_phone || ''}</td></tr>
                                        <tr><td><strong>الدولة:</strong></td><td>${transfer.recipient_country || ''}</td></tr>
                                        <tr><td><strong>العنوان:</strong></td><td>${transfer.recipient_address || ''}</td></tr>
                                    </table>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="text-primary">معلومات إضافية</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>تاريخ الإنشاء:</strong></td><td>${formatDateTime(transfer.created_at)}</td></tr>
                                        <tr><td><strong>آخر تحديث:</strong></td><td>${formatDateTime(transfer.updated_at)}</td></tr>
                                        <tr><td><strong>المستخدم:</strong></td><td>${transfer.user_name || ''} (${transfer.user_email || ''})</td></tr>
                                        <tr><td><strong>الغرض:</strong></td><td>${transfer.purpose || ''}</td></tr>
                                        <tr><td><strong>ملاحظات:</strong></td><td>${transfer.notes || ''}</td></tr>
                                    </table>
                                </div>
                            </div>
                        `;
                        $('#transferDetailsContent').html(detailsHtml);
                        $('#transferDetailsModal').modal('show');
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في الاتصال بالخادم', 'danger');
                }
            });
        }

        function updateStatus(id) {
            $('#updateTransferId').val(id);
            $('#updateStatusModal').modal('show');
        }

        function updateTransferStatus() {
            const formData = new FormData($('#updateStatusForm')[0]);
            formData.append('action', 'update_transfer_status');

            $.ajax({
                url: '',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showAlert(response.message, 'success');
                        $('#updateStatusModal').modal('hide');
                        $('#updateStatusForm')[0].reset();
                        loadTransfers();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في الاتصال بالخادم', 'danger');
                }
            });
        }

        function printTransfer(id) {
            // Open print window
            window.open(`transfer_receipt.php?id=${id}`, '_blank');
        }

        function exportTransfers() {
            showAlert('ميزة التصدير قيد التطوير', 'info');
        }

        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);

            // Auto dismiss after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        }

        function getStatusText(status) {
            const statuses = {
                'pending': 'في الانتظار',
                'processing': 'قيد المعالجة',
                'completed': 'مكتمل',
                'cancelled': 'ملغي',
                'failed': 'فاشل'
            };
            return statuses[status] || status;
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('ar-SA', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    </script>
</body>
</html>
