<?php

/**
 * Professional Login Page
 * Elite Transfer System - Ultra Professional Login Interface
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// If already logged in, redirect to dashboard
if (isLoggedIn()) {
    header('Location: dashboard_advanced.php');
    exit;
}

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    } else {
        try {
            $db = DatabaseManager::getInstance();
            
            // Find user
            $user = $db->selectOne(
                "SELECT * FROM users WHERE email = :email AND status = 'active' AND (deleted_at IS NULL OR deleted_at = '')",
                ['email' => $email]
            );
            
            if ($user && password_verify($password, $user['password_hash'])) {
                // Login successful
                if (loginUser($user)) {
                    $success = 'تم تسجيل الدخول بنجاح';
                    
                    // Log successful login
                    logMessage('INFO', 'User logged in successfully', [
                        'user_id' => $user['id'],
                        'email' => $user['email'],
                        'role' => $user['role'],
                        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                    ]);
                    
                    // Redirect after 2 seconds
                    header("refresh:2;url=dashboard_advanced.php");
                } else {
                    $error = 'فشل في تسجيل الدخول';
                }
            } else {
                $error = 'بيانات الدخول غير صحيحة';
                
                // Log failed login attempt
                logMessage('WARNING', 'Failed login attempt', [
                    'email' => $email,
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
            }
        } catch (Exception $e) {
            logMessage('ERROR', 'Login error', ['error' => $e->getMessage(), 'email' => $email]);
            $error = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى.';
        }
    }
}

// Get flash messages
$flashMessages = getFlashMessages();

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }
        
        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"><animate attributeName="cx" values="200;800;200" dur="20s" repeatCount="indefinite"/></circle><circle cx="800" cy="300" r="150" fill="url(%23a)"><animate attributeName="cy" values="300;700;300" dur="25s" repeatCount="indefinite"/></circle><circle cx="400" cy="600" r="80" fill="url(%23a)"><animate attributeName="r" values="80;120;80" dur="15s" repeatCount="indefinite"/></circle></svg>');
            z-index: -1;
            animation: float 30s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 25px;
            box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
            padding: 50px;
            width: 100%;
            max-width: 450px;
            position: relative;
            overflow: hidden;
        }
        
        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--success-gradient);
            border-radius: 25px 25px 0 0;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .login-logo {
            width: 80px;
            height: 80px;
            background: var(--success-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
            color: white;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
        }
        
        .login-title {
            font-size: 2.2rem;
            font-weight: 800;
            color: white;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .login-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            font-weight: 400;
        }
        
        .form-floating {
            margin-bottom: 25px;
        }
        
        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .form-control:focus {
            background: rgba(255, 255, 255, 0.95);
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
            transform: translateY(-2px);
        }
        
        .form-floating > label {
            color: #666;
            font-weight: 500;
        }
        
        .btn-login {
            background: var(--success-gradient);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-login:hover::before {
            left: 100%;
        }
        
        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(79, 172, 254, 0.4);
        }
        
        .btn-auto-login {
            background: var(--secondary-gradient);
            border: none;
            border-radius: 15px;
            padding: 12px 25px;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            margin-top: 15px;
            transition: all 0.3s ease;
        }
        
        .btn-auto-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(240, 147, 251, 0.4);
        }
        
        .alert {
            border: none;
            border-radius: 15px;
            padding: 15px 20px;
            margin-bottom: 25px;
            backdrop-filter: blur(10px);
            font-weight: 500;
        }
        
        .alert-success {
            background: rgba(72, 187, 120, 0.2);
            color: #2f855a;
            border: 1px solid rgba(72, 187, 120, 0.3);
        }
        
        .alert-danger {
            background: rgba(245, 101, 101, 0.2);
            color: #c53030;
            border: 1px solid rgba(245, 101, 101, 0.3);
        }
        
        .system-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .system-info h6 {
            color: white;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .demo-accounts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .demo-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 8px 12px;
            color: white;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .demo-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .footer-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .footer-links a:hover {
            color: white;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .login-card {
                padding: 30px 25px;
                margin: 10px;
            }
            
            .login-title {
                font-size: 1.8rem;
            }
            
            .demo-accounts {
                grid-template-columns: 1fr;
            }
        }
        
        /* Animations */
        .animate-fade-in {
            animation: fadeIn 0.8s ease-out;
        }
        
        .animate-slide-up {
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(30px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center text-white">
            <div class="loading-spinner mb-3"></div>
            <h5>جاري تسجيل الدخول...</h5>
        </div>
    </div>

    <div class="login-container">
        <div class="login-card animate-fade-in">
            <!-- Header -->
            <div class="login-header animate-slide-up">
                <div class="login-logo">
                    <i class="bi bi-shield-lock"></i>
                </div>
                <h1 class="login-title"><?= SYSTEM_NAME ?></h1>
                <p class="login-subtitle">نظام التحويلات المالية المتقدم</p>
            </div>

            <!-- Flash Messages -->
            <?php if (!empty($flashMessages)): ?>
                <?php foreach ($flashMessages as $flashMessage): ?>
                    <div class="alert alert-<?= $flashMessage['type'] === 'success' ? 'success' : 'danger' ?> animate__animated animate__fadeInDown">
                        <i class="bi bi-<?= $flashMessage['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> me-2"></i>
                        <?= htmlspecialchars($flashMessage['message']) ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- Error Messages -->
            <?php if ($error): ?>
                <div class="alert alert-danger animate__animated animate__shakeX">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <!-- Success Messages -->
            <?php if ($success): ?>
                <div class="alert alert-success animate__animated animate__bounceIn">
                    <i class="bi bi-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                    <div class="mt-2">
                        <small>سيتم توجيهك إلى لوحة التحكم خلال ثوانٍ...</small>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Login Form -->
            <form id="loginForm" method="POST" action="login_professional.php" class="animate-slide-up">
                <div class="form-floating">
                    <input type="email" 
                           class="form-control" 
                           id="email" 
                           name="email" 
                           placeholder="البريد الإلكتروني"
                           required 
                           autofocus>
                    <label for="email">
                        <i class="bi bi-envelope me-2"></i>
                        البريد الإلكتروني
                    </label>
                </div>

                <div class="form-floating">
                    <input type="password" 
                           class="form-control" 
                           id="password" 
                           name="password" 
                           placeholder="كلمة المرور"
                           required>
                    <label for="password">
                        <i class="bi bi-lock me-2"></i>
                        كلمة المرور
                    </label>
                </div>

                <button type="submit" class="btn btn-login">
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    تسجيل الدخول
                </button>

                <button type="button" class="btn btn-auto-login" onclick="autoLogin()">
                    <i class="bi bi-lightning me-2"></i>
                    دخول سريع كمدير
                </button>
            </form>

            <!-- System Info -->
            <div class="system-info animate-slide-up">
                <h6>
                    <i class="bi bi-info-circle me-2"></i>
                    حسابات تجريبية
                </h6>
                <div class="demo-accounts">
                    <button class="demo-btn" onclick="fillDemo('admin')">
                        <i class="bi bi-person-gear me-1"></i>
                        مدير
                    </button>
                    <button class="demo-btn" onclick="fillDemo('manager')">
                        <i class="bi bi-person-badge me-1"></i>
                        مدير فرع
                    </button>
                    <button class="demo-btn" onclick="fillDemo('agent')">
                        <i class="bi bi-person-check me-1"></i>
                        وكيل
                    </button>
                </div>
                <div class="mt-3">
                    <small>
                        <i class="bi bi-shield-check me-1"></i>
                        نظام آمن ومشفر | الإصدار <?= SYSTEM_VERSION ?>
                    </small>
                </div>
            </div>

            <!-- Footer Links -->
            <div class="footer-links animate-slide-up">
                <a href="index.php">
                    <i class="bi bi-house me-1"></i>
                    الرئيسية
                </a>
                <a href="track_transfer_fixed.php">
                    <i class="bi bi-search me-1"></i>
                    تتبع التحويل
                </a>
                <a href="test_server.php">
                    <i class="bi bi-server me-1"></i>
                    حالة النظام
                </a>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto login function
        function autoLogin() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'admin123';
            document.getElementById('loginForm').submit();
        }
        
        // Fill demo credentials
        function fillDemo(type) {
            const emailField = document.getElementById('email');
            const passwordField = document.getElementById('password');
            
            switch(type) {
                case 'admin':
                    emailField.value = '<EMAIL>';
                    passwordField.value = 'admin123';
                    break;
                case 'manager':
                    emailField.value = '<EMAIL>';
                    passwordField.value = 'manager123';
                    break;
                case 'agent':
                    emailField.value = '<EMAIL>';
                    passwordField.value = 'agent123';
                    break;
            }
            
            // Add visual feedback
            emailField.style.transform = 'scale(1.02)';
            passwordField.style.transform = 'scale(1.02)';
            
            setTimeout(() => {
                emailField.style.transform = 'scale(1)';
                passwordField.style.transform = 'scale(1)';
            }, 200);
        }
        
        // Form submission with loading
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            if (!document.getElementById('email').value || !document.getElementById('password').value) {
                return;
            }
            
            // Show loading overlay
            document.getElementById('loadingOverlay').style.display = 'flex';
            
            // Add small delay for better UX
            setTimeout(() => {
                // Form will submit normally
            }, 500);
        });
        
        // Enhanced form interactions
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
            });
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl + Enter for quick admin login
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                autoLogin();
            }
        });
        
        // Page load animations
        window.addEventListener('load', function() {
            // Add staggered animations to form elements
            const elements = document.querySelectorAll('.form-floating, .btn');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
                el.classList.add('animate__animated', 'animate__fadeInUp');
            });
        });
        
        console.log('🚀 Professional Login System Loaded - Elite Transfer v<?= SYSTEM_VERSION ?>');
    </script>
</body>
</html>
