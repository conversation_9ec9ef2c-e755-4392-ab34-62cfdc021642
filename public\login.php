<?php

/**
 * Enhanced Login Page
 * Elite Transfer System - Secure login with auto-admin feature
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// If already logged in, redirect to dashboard
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

// Handle login form submission
if ($_POST) {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';

    if ($email && $password) {
        try {
            // Connect to database
            $db = DatabaseManager::getInstance();

            // Find user
            $user = $db->selectOne(
                "SELECT * FROM users WHERE email = :email AND status = 'active' AND (deleted_at IS NULL OR deleted_at = '')",
                ['email' => $email]
            );

            if ($user && password_verify($password, $user['password_hash'])) {
                // Login successful - use the enhanced login function
                if (loginUser($user)) {
                    $success = 'مرحباً بك، ' . $user['name'];

                    // Redirect to dashboard
                    header('Location: dashboard.php');
                    exit;
                } else {
                    $error = 'فشل في تسجيل الدخول';
                }
            } else {
                $error = 'بيانات الدخول غير صحيحة';
            }
        } catch (Exception $e) {
            logMessage('ERROR', 'Login error', ['error' => $e->getMessage(), 'email' => $email]);
            $error = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى.';
        }
    } else {
        $error = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    }
}

// Get flash messages
$flashMessages = getFlashMessages();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #2563eb, #3b82f6);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand text-white" href="index.php">
                <i class="bi bi-bank2 me-2"></i>
                <?= SYSTEM_NAME ?> v<?= SYSTEM_VERSION ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="index.php">
                    <i class="bi bi-house me-1"></i>
                    الرئيسية
                </a>
                <a class="nav-link text-white" href="create_transfer_fixed.php">
                    <i class="bi bi-plus-circle me-1"></i>
                    تحويل جديد
                </a>
                <a class="nav-link text-white" href="track_transfer_fixed.php">
                    <i class="bi bi-search me-1"></i>
                    تتبع التحويل
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-5">
                <div class="card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="bi bi-bank2 text-primary" style="font-size: 3rem;"></i>
                            <h2 class="mt-3 mb-1">أهلاً بعودتك</h2>
                            <p class="text-muted">سجل دخولك إلى حساب Elite Transfer</p>
                        </div>

                        <!-- Flash Messages -->
                        <?php if (!empty($flashMessages)): ?>
                            <?php foreach ($flashMessages as $flashMessage): ?>
                                <div class="alert alert-<?= $flashMessage['type'] === 'success' ? 'success' : ($flashMessage['type'] === 'error' ? 'danger' : 'info') ?> alert-dismissible fade show" role="alert">
                                    <i class="bi bi-<?= $flashMessage['type'] === 'success' ? 'check-circle' : ($flashMessage['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
                                    <?= htmlspecialchars($flashMessage['message']) ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>

                        <!-- Error Messages -->
                        <?php if ($error): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <?= htmlspecialchars($error) ?>
                            </div>
                        <?php endif; ?>

                        <!-- Success Messages -->
                        <?php if ($success): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="bi bi-check-circle me-2"></i>
                                <?= htmlspecialchars($success) ?>
                            </div>
                        <?php endif; ?>

                        <!-- Demo Accounts Info -->
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>حسابات تجريبية متاحة:</h6>
                            <small>
                                <strong>مدير:</strong> <EMAIL> / password<br>
                                <strong>عميل:</strong> <EMAIL> / customer123
                            </small>
                        </div>

                        <!-- Login Form -->
                        <form id="loginForm" method="POST" action="login.php">
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="bi bi-envelope me-1"></i>
                                    البريد الإلكتروني
                                </label>
                                <input type="email" 
                                       class="form-control" 
                                       id="email" 
                                       name="email" 
                                       required 
                                       autofocus
                                       placeholder="أدخل بريدك الإلكتروني">
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock me-1"></i>
                                    كلمة المرور
                                </label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password" 
                                           required
                                           placeholder="أدخل كلمة المرور">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    تذكرني
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </div>

                            <div class="text-center">
                                <a href="#" class="text-decoration-none">
                                    نسيت كلمة المرور؟
                                </a>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="text-center">
                            <p class="mb-0">تسجيل دخول سريع للاختبار:</p>
                            <button type="button" class="btn btn-outline-success mt-2" onclick="autoLogin()">
                                <i class="bi bi-lightning me-2"></i>
                                دخول تلقائي كمدير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });

        // Auto login function
        function autoLogin() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'admin123';
            document.getElementById('loginForm').submit();
        }

        console.log('🔐 Enhanced Login Page Loaded - Elite Transfer System v<?= SYSTEM_VERSION ?>');
    </script>
</body>
</html>
