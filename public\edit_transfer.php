<?php

/**
 * Edit Transfer
 * Elite Transfer System - Professional Transfer Editing
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

$db = DatabaseManager::getInstance();
$transferId = intval($_GET['id'] ?? 0);
$message = '';
$messageType = '';

if (!$transferId) {
    header('Location: transfers_safe.php');
    exit;
}

// Get transfer details
$transfer = $db->selectOne("
    SELECT * FROM transfers 
    WHERE id = :id AND (deleted_at IS NULL OR deleted_at = '')
", ['id' => $transferId]);

if (!$transfer) {
    header('Location: transfers_safe.php');
    exit;
}

// Get countries for dropdowns
$countries = $db->getCountries();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_transfer') {
    try {
        // Get form data
        $senderName = trim($_POST['sender_name'] ?? '');
        $senderPhone = trim($_POST['sender_phone'] ?? '');
        $senderEmail = trim($_POST['sender_email'] ?? '');
        $senderCountry = intval($_POST['sender_country'] ?? 0);
        $senderAddress = trim($_POST['sender_address'] ?? '');
        
        $recipientName = trim($_POST['recipient_name'] ?? '');
        $recipientPhone = trim($_POST['recipient_phone'] ?? '');
        $recipientEmail = trim($_POST['recipient_email'] ?? '');
        $recipientCountry = intval($_POST['recipient_country'] ?? 0);
        $recipientAddress = trim($_POST['recipient_address'] ?? '');
        
        $amount = floatval($_POST['amount'] ?? 0);
        $fee = floatval($_POST['fee'] ?? 0);
        $exchangeRate = floatval($_POST['exchange_rate'] ?? 1);
        $totalAmount = $amount + $fee;
        
        $purpose = trim($_POST['purpose'] ?? '');
        $paymentMethod = trim($_POST['payment_method'] ?? '');
        $notes = trim($_POST['notes'] ?? '');
        
        // Validation
        if (empty($senderName) || empty($recipientName) || $amount <= 0) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        // Update transfer
        $updateData = [
            'sender_name' => $senderName,
            'sender_phone' => $senderPhone,
            'sender_email' => $senderEmail,
            'sender_country_id' => $senderCountry,
            'sender_address' => $senderAddress,
            'recipient_name' => $recipientName,
            'recipient_phone' => $recipientPhone,
            'recipient_email' => $recipientEmail,
            'recipient_country_id' => $recipientCountry,
            'recipient_address' => $recipientAddress,
            'amount' => $amount,
            'fee' => $fee,
            'exchange_rate' => $exchangeRate,
            'total_amount' => $totalAmount,
            'purpose' => $purpose,
            'payment_method' => $paymentMethod,
            'notes' => $notes . "\n\nتم التعديل بواسطة: " . getUserName() . " في " . date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $updated = $db->update('transfers', $updateData, 'id = :id', ['id' => $transferId]);
        
        if ($updated) {
            // Log the update
            logMessage('INFO', 'Transfer updated', [
                'transfer_id' => $transferId,
                'transfer_code' => $transfer['transfer_code'],
                'user_id' => getUserId()
            ]);
            
            $message = 'تم تحديث التحويل بنجاح';
            $messageType = 'success';
            
            // Refresh transfer data
            $transfer = $db->selectOne("
                SELECT * FROM transfers 
                WHERE id = :id AND (deleted_at IS NULL OR deleted_at = '')
            ", ['id' => $transferId]);
        } else {
            throw new Exception('فشل في تحديث التحويل');
        }
        
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $messageType = 'error';
        logMessage('ERROR', 'Transfer update failed', [
            'transfer_id' => $transferId,
            'error' => $e->getMessage(),
            'user_id' => getUserId()
        ]);
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل التحويل - <?= $transfer['transfer_code'] ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .edit-container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }
        
        .page-title {
            font-size: 2.2rem;
            font-weight: 800;
            color: #495057;
            margin-bottom: 10px;
        }
        
        .transfer-code {
            font-size: 1.2rem;
            color: #007bff;
            font-weight: 600;
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #007bff;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .btn-update {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-update:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }
        
        .btn-back {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
        }
        
        .amount-display {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
        }
        
        .amount-value {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 5px;
        }
        
        .required {
            color: #dc3545;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 700;
            font-size: 0.9rem;
        }
        
        .status-pending { background: rgba(255, 193, 7, 0.2); color: #ff8f00; border: 2px solid #ffc107; }
        .status-processing { background: rgba(13, 110, 253, 0.2); color: #0d6efd; border: 2px solid #007bff; }
        .status-completed { background: rgba(25, 135, 84, 0.2); color: #198754; border: 2px solid #28a745; }
        .status-cancelled { background: rgba(220, 53, 69, 0.2); color: #dc3545; border: 2px solid #dc3545; }
        
        @media (max-width: 768px) {
            .edit-container {
                margin: 10px;
                padding: 20px;
            }
            
            .page-title {
                font-size: 1.8rem;
            }
            
            .form-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="edit-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="bi bi-pencil-square me-3"></i>
                تعديل التحويل
            </h1>
            <div class="transfer-code">
                رمز التحويل: <?= $transfer['transfer_code'] ?>
            </div>
            <div class="mt-2">
                <span class="status-badge status-<?= $transfer['status'] ?>">
                    <?= getStatusLabel($transfer['status']) ?>
                </span>
            </div>
        </div>
        
        <!-- Messages -->
        <?php if ($message): ?>
        <div class="alert alert-<?= $messageType === 'success' ? 'success' : 'danger' ?> alert-dismissible fade show" role="alert">
            <i class="bi bi-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?> me-2"></i>
            <?= htmlspecialchars($message) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <!-- Edit Form -->
        <form method="POST" id="editForm">
            <input type="hidden" name="action" value="update_transfer">
            
            <!-- Sender Information -->
            <div class="form-section">
                <div class="section-title">
                    <i class="bi bi-person-fill"></i>
                    معلومات المرسل
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم المرسل <span class="required">*</span></label>
                        <input type="text" class="form-control" name="sender_name" value="<?= htmlspecialchars($transfer['sender_name']) ?>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">هاتف المرسل <span class="required">*</span></label>
                        <input type="tel" class="form-control" name="sender_phone" value="<?= htmlspecialchars($transfer['sender_phone']) ?>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">بريد المرسل</label>
                        <input type="email" class="form-control" name="sender_email" value="<?= htmlspecialchars($transfer['sender_email'] ?? '') ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">بلد المرسل</label>
                        <select class="form-select" name="sender_country">
                            <option value="0">اختر البلد</option>
                            <?php foreach ($countries as $country): ?>
                            <option value="<?= $country['id'] ?>" <?= $country['id'] == $transfer['sender_country_id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($country['name']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">عنوان المرسل</label>
                        <textarea class="form-control" name="sender_address" rows="2"><?= htmlspecialchars($transfer['sender_address'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>
            
            <!-- Recipient Information -->
            <div class="form-section">
                <div class="section-title">
                    <i class="bi bi-person-check-fill"></i>
                    معلومات المستلم
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم المستلم <span class="required">*</span></label>
                        <input type="text" class="form-control" name="recipient_name" value="<?= htmlspecialchars($transfer['recipient_name']) ?>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">هاتف المستلم <span class="required">*</span></label>
                        <input type="tel" class="form-control" name="recipient_phone" value="<?= htmlspecialchars($transfer['recipient_phone']) ?>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">بريد المستلم</label>
                        <input type="email" class="form-control" name="recipient_email" value="<?= htmlspecialchars($transfer['recipient_email'] ?? '') ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">بلد المستلم</label>
                        <select class="form-select" name="recipient_country">
                            <option value="0">اختر البلد</option>
                            <?php foreach ($countries as $country): ?>
                            <option value="<?= $country['id'] ?>" <?= $country['id'] == $transfer['recipient_country_id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($country['name']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">عنوان المستلم</label>
                        <textarea class="form-control" name="recipient_address" rows="2"><?= htmlspecialchars($transfer['recipient_address'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>
            
            <!-- Amount Information -->
            <div class="form-section">
                <div class="section-title">
                    <i class="bi bi-currency-dollar"></i>
                    معلومات المبلغ
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">المبلغ الأساسي <span class="required">*</span></label>
                        <input type="number" class="form-control" name="amount" value="<?= $transfer['amount'] ?>" step="0.01" min="0" required id="amount">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">الرسوم</label>
                        <input type="number" class="form-control" name="fee" value="<?= $transfer['fee'] ?>" step="0.01" min="0" id="fee">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">سعر الصرف</label>
                        <input type="number" class="form-control" name="exchange_rate" value="<?= $transfer['exchange_rate'] ?>" step="0.0001" min="0" id="exchangeRate">
                    </div>
                </div>
                
                <!-- Total Amount Display -->
                <div class="amount-display">
                    <div class="amount-value" id="totalAmount">
                        $<?= number_format($transfer['total_amount'], 2) ?>
                    </div>
                    <div>إجمالي المبلغ</div>
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="form-section">
                <div class="section-title">
                    <i class="bi bi-info-circle-fill"></i>
                    معلومات إضافية
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الغرض من التحويل</label>
                        <input type="text" class="form-control" name="purpose" value="<?= htmlspecialchars($transfer['purpose'] ?? '') ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">طريقة الدفع</label>
                        <select class="form-select" name="payment_method">
                            <option value="">اختر طريقة الدفع</option>
                            <option value="cash" <?= $transfer['payment_method'] === 'cash' ? 'selected' : '' ?>>نقداً</option>
                            <option value="bank_transfer" <?= $transfer['payment_method'] === 'bank_transfer' ? 'selected' : '' ?>>تحويل بنكي</option>
                            <option value="credit_card" <?= $transfer['payment_method'] === 'credit_card' ? 'selected' : '' ?>>بطاقة ائتمان</option>
                            <option value="debit_card" <?= $transfer['payment_method'] === 'debit_card' ? 'selected' : '' ?>>بطاقة خصم</option>
                        </select>
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="3"><?= htmlspecialchars($transfer['notes'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-update me-3">
                    <i class="bi bi-check-lg me-2"></i>
                    حفظ التعديلات
                </button>
                <a href="transfers_safe.php" class="btn btn-back">
                    <i class="bi bi-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </form>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Calculate total amount
        function calculateTotal() {
            const amount = parseFloat(document.getElementById('amount').value) || 0;
            const fee = parseFloat(document.getElementById('fee').value) || 0;
            const total = amount + fee;
            
            document.getElementById('totalAmount').textContent = '$' + total.toFixed(2);
        }
        
        // Add event listeners
        document.getElementById('amount').addEventListener('input', calculateTotal);
        document.getElementById('fee').addEventListener('input', calculateTotal);
        
        // Form validation and submission
        document.getElementById('editForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            Swal.fire({
                title: 'تأكيد التعديل',
                text: 'هل أنت متأكد من حفظ التعديلات؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، احفظ',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#28a745'
            }).then((result) => {
                if (result.isConfirmed) {
                    this.submit();
                }
            });
        });
        
        <?php if ($message && $messageType === 'success'): ?>
        // Show success message
        Swal.fire({
            icon: 'success',
            title: 'تم بنجاح!',
            text: '<?= $message ?>',
            timer: 3000,
            showConfirmButton: false
        });
        <?php endif; ?>
    </script>
</body>
</html>
