<?php

/**
 * System Upgrade Tool
 * Elite Transfer System - Comprehensive system upgrade and optimization
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

// Handle upgrade actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $db = DatabaseManager::getInstance();
        
        switch ($_POST['action']) {
            case 'upgrade_database':
                $result = upgradeDatabaseStructure($db);
                echo json_encode(['success' => true, 'message' => 'تم تحديث قاعدة البيانات بنجاح', 'details' => $result], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'optimize_database':
                $result = optimizeDatabase($db);
                echo json_encode(['success' => true, 'message' => 'تم تحسين قاعدة البيانات بنجاح', 'details' => $result], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'update_files':
                $result = updateSystemFiles();
                echo json_encode(['success' => true, 'message' => 'تم تحديث ملفات النظام بنجاح', 'details' => $result], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'clear_cache':
                $result = clearSystemCache($db);
                echo json_encode(['success' => true, 'message' => 'تم مسح الذاكرة المؤقتة بنجاح', 'details' => $result], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'system_check':
                $result = performSystemCheck($db);
                echo json_encode(['success' => true, 'message' => 'تم فحص النظام بنجاح', 'details' => $result], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح'], JSON_UNESCAPED_UNICODE);
        }
        
    } catch (Exception $e) {
        logMessage('ERROR', 'System upgrade error', ['error' => $e->getMessage()]);
        echo json_encode(['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
    
    exit;
}

function upgradeDatabaseStructure($db) {
    $results = [];
    
    // Check and add missing columns
    $requiredColumns = [
        'transfers' => [
            'user_id' => 'INT',
            'sender_country_id' => 'INT',
            'recipient_country_id' => 'INT',
            'sender_address' => 'TEXT',
            'recipient_address' => 'TEXT',
            'fee' => 'DECIMAL(15,2)',
            'exchange_rate' => 'DECIMAL(10,4)',
            'total_amount' => 'DECIMAL(15,2)',
            'currency_from' => 'VARCHAR(3) DEFAULT "USD"',
            'currency_to' => 'VARCHAR(3) DEFAULT "USD"',
            'payment_method' => 'VARCHAR(50)',
            'purpose' => 'TEXT',
            'notes' => 'TEXT',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'deleted_at' => 'TIMESTAMP NULL DEFAULT NULL'
        ]
    ];
    
    foreach ($requiredColumns as $table => $columns) {
        $existingColumns = $db->select("DESCRIBE $table");
        $existingColumnNames = array_column($existingColumns, 'Field');
        
        foreach ($columns as $column => $definition) {
            if (!in_array($column, $existingColumnNames)) {
                try {
                    $db->query("ALTER TABLE $table ADD COLUMN $column $definition");
                    $results[] = "تم إضافة العمود $column إلى جدول $table";
                } catch (Exception $e) {
                    $results[] = "فشل في إضافة العمود $column: " . $e->getMessage();
                }
            }
        }
    }
    
    // Create indexes for performance
    $indexes = [
        'transfers' => [
            'idx_user_id' => 'user_id',
            'idx_status' => 'status',
            'idx_created_at' => 'created_at',
            'idx_transfer_code' => 'transfer_code',
            'idx_pickup_code' => 'pickup_code',
            'idx_sender_country' => 'sender_country_id',
            'idx_recipient_country' => 'recipient_country_id'
        ]
    ];
    
    foreach ($indexes as $table => $tableIndexes) {
        foreach ($tableIndexes as $indexName => $column) {
            try {
                $db->query("CREATE INDEX $indexName ON $table ($column)");
                $results[] = "تم إنشاء الفهرس $indexName";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    $results[] = "تحذير في إنشاء الفهرس $indexName: " . $e->getMessage();
                }
            }
        }
    }
    
    return $results;
}

function optimizeDatabase($db) {
    $results = [];
    
    // Optimize tables
    $tables = ['transfers', 'users', 'countries'];
    
    foreach ($tables as $table) {
        try {
            $db->query("OPTIMIZE TABLE $table");
            $results[] = "تم تحسين جدول $table";
        } catch (Exception $e) {
            $results[] = "فشل في تحسين جدول $table: " . $e->getMessage();
        }
    }
    
    // Analyze tables
    foreach ($tables as $table) {
        try {
            $db->query("ANALYZE TABLE $table");
            $results[] = "تم تحليل جدول $table";
        } catch (Exception $e) {
            $results[] = "فشل في تحليل جدول $table: " . $e->getMessage();
        }
    }
    
    return $results;
}

function updateSystemFiles() {
    $results = [];
    
    // List of files to update with their new versions
    $filesToUpdate = [
        'index.php' => 'index_v2.php',
        'dashboard.php' => 'dashboard_v2.php',
        'create-transfer.php' => 'create_transfer_fixed.php',
        'track-transfer.php' => 'track_transfer_fixed.php',
        'includes/database_manager.php' => 'includes/database_manager_v2.php',
        'includes/session_helper.php' => 'includes/session_helper_v2.php'
    ];
    
    foreach ($filesToUpdate as $oldFile => $newFile) {
        $oldPath = __DIR__ . '/' . $oldFile;
        $newPath = __DIR__ . '/' . $newFile;
        
        if (file_exists($newPath)) {
            if (file_exists($oldPath)) {
                // Backup old file
                $backupPath = $oldPath . '.backup.' . date('Y-m-d-H-i-s');
                if (copy($oldPath, $backupPath)) {
                    $results[] = "تم إنشاء نسخة احتياطية: " . basename($backupPath);
                }
            }
            
            // Copy new file
            if (copy($newPath, $oldPath)) {
                $results[] = "تم تحديث الملف: $oldFile";
            } else {
                $results[] = "فشل في تحديث الملف: $oldFile";
            }
        }
    }
    
    return $results;
}

function clearSystemCache($db) {
    $results = [];
    
    // Clear database cache
    $db->clearCache();
    $results[] = "تم مسح ذاكرة قاعدة البيانات المؤقتة";
    
    // Clear session cache
    if (isset($_SESSION)) {
        $sessionData = $_SESSION;
        session_destroy();
        session_start();
        
        // Restore important session data
        if (isset($sessionData['user_id'])) {
            $_SESSION = $sessionData;
        }
        
        $results[] = "تم تحديث بيانات الجلسة";
    }
    
    // Clear file cache if exists
    $cacheDir = __DIR__ . '/cache';
    if (is_dir($cacheDir)) {
        $files = glob($cacheDir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        $results[] = "تم مسح ملفات الذاكرة المؤقتة";
    }
    
    return $results;
}

function performSystemCheck($db) {
    $results = [];
    
    // Check database connection
    try {
        $db->selectOne("SELECT 1");
        $results[] = "✅ اتصال قاعدة البيانات: يعمل";
    } catch (Exception $e) {
        $results[] = "❌ اتصال قاعدة البيانات: فاشل - " . $e->getMessage();
    }
    
    // Check required tables
    $requiredTables = ['transfers', 'users', 'countries'];
    foreach ($requiredTables as $table) {
        try {
            $count = $db->selectOne("SELECT COUNT(*) as count FROM $table")['count'];
            $results[] = "✅ جدول $table: $count سجل";
        } catch (Exception $e) {
            $results[] = "❌ جدول $table: غير موجود أو تالف";
        }
    }
    
    // Check file permissions
    $criticalFiles = [
        'includes/config.php',
        'includes/database_manager_v2.php',
        'includes/session_helper_v2.php'
    ];
    
    foreach ($criticalFiles as $file) {
        $filePath = __DIR__ . '/' . $file;
        if (file_exists($filePath)) {
            if (is_readable($filePath)) {
                $results[] = "✅ ملف $file: قابل للقراءة";
            } else {
                $results[] = "❌ ملف $file: غير قابل للقراءة";
            }
        } else {
            $results[] = "❌ ملف $file: غير موجود";
        }
    }
    
    // Check PHP version and extensions
    $results[] = "✅ إصدار PHP: " . PHP_VERSION;
    
    $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
    foreach ($requiredExtensions as $ext) {
        if (extension_loaded($ext)) {
            $results[] = "✅ امتداد $ext: مثبت";
        } else {
            $results[] = "❌ امتداد $ext: غير مثبت";
        }
    }
    
    // Check memory usage
    $memoryUsage = memory_get_usage(true);
    $memoryLimit = ini_get('memory_limit');
    $results[] = "ℹ️ استخدام الذاكرة: " . formatBytes($memoryUsage) . " من $memoryLimit";
    
    return $results;
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث النظام - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .upgrade-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1000px;
            overflow: hidden;
        }
        
        .upgrade-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .upgrade-content {
            padding: 40px;
        }
        
        .upgrade-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .upgrade-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.1);
        }
        
        .upgrade-card h5 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .upgrade-card p {
            color: #6c757d;
            margin-bottom: 20px;
        }
        
        .btn-upgrade {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-upgrade:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-upgrade:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        .progress-container {
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        
        .result-container {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        
        .result-container.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .system-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .version-badge {
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="upgrade-container">
            <!-- Header -->
            <div class="upgrade-header">
                <h1 class="mb-3">
                    <i class="bi bi-arrow-up-circle me-3"></i>
                    تحديث النظام الشامل
                </h1>
                <p class="mb-0 opacity-75">تحديث وتحسين جميع مكونات النظام للحصول على أفضل أداء</p>
                <div class="mt-3">
                    <span class="version-badge">الإصدار الحالي: <?= SYSTEM_VERSION ?></span>
                </div>
            </div>
            
            <!-- Content -->
            <div class="upgrade-content">
                <!-- System Info -->
                <div class="system-info">
                    <h5><i class="bi bi-info-circle me-2"></i>معلومات النظام</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <strong>اسم النظام:</strong><br>
                            <?= SYSTEM_NAME ?>
                        </div>
                        <div class="col-md-3">
                            <strong>الإصدار:</strong><br>
                            <?= SYSTEM_VERSION ?>
                        </div>
                        <div class="col-md-3">
                            <strong>PHP:</strong><br>
                            <?= PHP_VERSION ?>
                        </div>
                        <div class="col-md-3">
                            <strong>قاعدة البيانات:</strong><br>
                            MySQL
                        </div>
                    </div>
                </div>

                <!-- Upgrade Options -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="upgrade-card">
                            <h5><i class="bi bi-database me-2"></i>تحديث قاعدة البيانات</h5>
                            <p>إضافة الأعمدة المفقودة وإنشاء الفهارس المطلوبة لتحسين الأداء</p>
                            <button class="btn btn-upgrade" onclick="performUpgrade('upgrade_database')">
                                <i class="bi bi-arrow-up me-2"></i>
                                تحديث قاعدة البيانات
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="upgrade-card">
                            <h5><i class="bi bi-speedometer2 me-2"></i>تحسين الأداء</h5>
                            <p>تحسين جداول قاعدة البيانات وتحليل الاستعلامات لأداء أفضل</p>
                            <button class="btn btn-upgrade" onclick="performUpgrade('optimize_database')">
                                <i class="bi bi-lightning me-2"></i>
                                تحسين الأداء
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="upgrade-card">
                            <h5><i class="bi bi-file-code me-2"></i>تحديث الملفات</h5>
                            <p>استبدال الملفات القديمة بالإصدارات المحدثة والمحسنة</p>
                            <button class="btn btn-upgrade" onclick="performUpgrade('update_files')">
                                <i class="bi bi-download me-2"></i>
                                تحديث الملفات
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="upgrade-card">
                            <h5><i class="bi bi-trash me-2"></i>مسح الذاكرة المؤقتة</h5>
                            <p>مسح جميع ملفات الذاكرة المؤقتة وإعادة تعيين الجلسات</p>
                            <button class="btn btn-upgrade" onclick="performUpgrade('clear_cache')">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                مسح الذاكرة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- System Check -->
                <div class="upgrade-card">
                    <h5><i class="bi bi-shield-check me-2"></i>فحص النظام الشامل</h5>
                    <p>فحص جميع مكونات النظام والتأكد من سلامة التشغيل</p>
                    <button class="btn btn-upgrade" onclick="performUpgrade('system_check')">
                        <i class="bi bi-search me-2"></i>
                        فحص النظام
                    </button>
                </div>

                <!-- Progress Container -->
                <div class="progress-container" id="progressContainer">
                    <h6><i class="bi bi-hourglass-split me-2"></i>جاري التنفيذ...</h6>
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             style="width: 100%"></div>
                    </div>
                </div>

                <!-- Result Container -->
                <div class="result-container" id="resultContainer">
                    <h6 id="resultTitle"></h6>
                    <div id="resultContent"></div>
                </div>

                <!-- Quick Links -->
                <div class="text-center mt-4">
                    <h6>روابط سريعة:</h6>
                    <div class="d-flex gap-2 justify-content-center flex-wrap">
                        <a href="index_v2.php" class="btn btn-outline-primary">الصفحة الرئيسية المحدثة</a>
                        <a href="dashboard_v2.php" class="btn btn-outline-success">لوحة التحكم المحدثة</a>
                        <a href="create_transfer_fixed.php" class="btn btn-outline-info">إنشاء تحويل</a>
                        <a href="track_transfer_fixed.php" class="btn btn-outline-warning">تتبع التحويل</a>
                        <a href="test_server.php" class="btn btn-outline-secondary">اختبار الخادم</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        function performUpgrade(action) {
            console.log(`🚀 Starting upgrade: ${action}`);
            
            // Show progress
            $('#progressContainer').show();
            $('#resultContainer').hide();
            
            // Disable all buttons
            $('.btn-upgrade').prop('disabled', true);
            
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: action },
                dataType: 'json',
                success: function(response) {
                    $('#progressContainer').hide();
                    
                    if (response && response.success) {
                        showResult(response.message, response.details, 'success');
                    } else {
                        showResult(response.message || 'فشل في التنفيذ', [], 'error');
                    }
                },
                error: function(xhr, status, error) {
                    $('#progressContainer').hide();
                    showResult('خطأ في الاتصال: ' + error, [], 'error');
                },
                complete: function() {
                    // Re-enable buttons
                    $('.btn-upgrade').prop('disabled', false);
                }
            });
        }
        
        function showResult(message, details, type) {
            const container = $('#resultContainer');
            const title = $('#resultTitle');
            const content = $('#resultContent');
            
            // Set title
            const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';
            title.html(`<i class="bi bi-${icon} me-2"></i>${message}`);
            
            // Set content
            if (details && details.length > 0) {
                let detailsHtml = '<ul class="mt-3">';
                details.forEach(detail => {
                    detailsHtml += `<li>${detail}</li>`;
                });
                detailsHtml += '</ul>';
                content.html(detailsHtml);
            } else {
                content.html('');
            }
            
            // Set style
            container.removeClass('error');
            if (type === 'error') {
                container.addClass('error');
            }
            
            // Show container
            container.show();
            
            // Scroll to result
            container[0].scrollIntoView({ behavior: 'smooth' });
        }
        
        // Auto-refresh page info
        setInterval(function() {
            console.log('🔄 System upgrade tool active');
        }, 30000);
    </script>
</body>
</html>
