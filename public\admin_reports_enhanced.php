<?php

// Elite Transfer System - Enhanced Admin Reports
// Complete reporting functionality

// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

// Load database manager
require_once __DIR__ . '/includes/database_manager.php';

try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'get_financial_report':
            $date_from = $_POST['date_from'] ?? date('Y-m-01');
            $date_to = $_POST['date_to'] ?? date('Y-m-d');
            
            // Financial statistics
            $financial_data = [
                'total_transfers' => $db->prepare("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN ? AND ? AND deleted_at IS NULL")->execute([$date_from, $date_to]) ? $db->fetchColumn() : 0,
                'completed_transfers' => $db->prepare("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN ? AND ? AND status = 'completed' AND deleted_at IS NULL")->execute([$date_from, $date_to]) ? $db->fetchColumn() : 0,
                'total_amount' => $db->prepare("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE DATE(created_at) BETWEEN ? AND ? AND status = 'completed' AND deleted_at IS NULL")->execute([$date_from, $date_to]) ? $db->fetchColumn() : 0,
                'total_fees' => $db->prepare("SELECT COALESCE(SUM(fee), 0) FROM transfers WHERE DATE(created_at) BETWEEN ? AND ? AND status = 'completed' AND deleted_at IS NULL")->execute([$date_from, $date_to]) ? $db->fetchColumn() : 0,
                'average_amount' => 0
            ];
            
            if ($financial_data['completed_transfers'] > 0) {
                $financial_data['average_amount'] = $financial_data['total_amount'] / $financial_data['completed_transfers'];
            }
            
            echo json_encode(['success' => true, 'data' => $financial_data]);
            exit;
            
        case 'get_daily_chart':
            $date_from = $_POST['date_from'] ?? date('Y-m-01');
            $date_to = $_POST['date_to'] ?? date('Y-m-d');
            
            $stmt = $db->prepare("
                SELECT DATE(created_at) as date, 
                       COUNT(*) as transfers,
                       COALESCE(SUM(amount), 0) as amount
                FROM transfers 
                WHERE DATE(created_at) BETWEEN ? AND ? 
                  AND status = 'completed' 
                  AND deleted_at IS NULL
                GROUP BY DATE(created_at)
                ORDER BY DATE(created_at)
            ");
            $stmt->execute([$date_from, $date_to]);
            $daily_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'data' => $daily_data]);
            exit;
            
        case 'get_country_report':
            $date_from = $_POST['date_from'] ?? date('Y-m-01');
            $date_to = $_POST['date_to'] ?? date('Y-m-d');
            
            $stmt = $db->prepare("
                SELECT c.name as country,
                       COUNT(t.id) as transfers,
                       COALESCE(SUM(t.amount), 0) as amount
                FROM transfers t
                LEFT JOIN countries c ON t.recipient_country_id = c.id
                WHERE DATE(t.created_at) BETWEEN ? AND ? 
                  AND t.status = 'completed' 
                  AND t.deleted_at IS NULL
                GROUP BY c.id, c.name
                ORDER BY amount DESC
                LIMIT 10
            ");
            $stmt->execute([$date_from, $date_to]);
            $country_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'data' => $country_data]);
            exit;
            
        case 'get_user_report':
            $date_from = $_POST['date_from'] ?? date('Y-m-01');
            $date_to = $_POST['date_to'] ?? date('Y-m-d');
            
            $stmt = $db->prepare("
                SELECT u.name as user_name,
                       u.email,
                       COUNT(t.id) as transfers,
                       COALESCE(SUM(t.amount), 0) as amount
                FROM transfers t
                LEFT JOIN users u ON t.user_id = u.id
                WHERE DATE(t.created_at) BETWEEN ? AND ? 
                  AND t.status = 'completed' 
                  AND t.deleted_at IS NULL
                GROUP BY u.id, u.name, u.email
                ORDER BY amount DESC
                LIMIT 10
            ");
            $stmt->execute([$date_from, $date_to]);
            $user_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'data' => $user_data]);
            exit;
    }
}

// Get current month statistics
$current_month_stats = [
    'total_transfers' => $db->query("SELECT COUNT(*) FROM transfers WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE()) AND deleted_at IS NULL")->fetchColumn(),
    'completed_transfers' => $db->query("SELECT COUNT(*) FROM transfers WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE()) AND status = 'completed' AND deleted_at IS NULL")->fetchColumn(),
    'total_amount' => $db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE()) AND status = 'completed' AND deleted_at IS NULL")->fetchColumn(),
    'total_fees' => $db->query("SELECT COALESCE(SUM(fee), 0) FROM transfers WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE()) AND status = 'completed' AND deleted_at IS NULL")->fetchColumn()
];

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
        }
        
        .stats-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stats-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .report-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .report-card-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .report-card-body {
            padding: 20px;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-1">
                        <i class="bi bi-graph-up me-2"></i>
                        التقارير والإحصائيات
                    </h1>
                    <p class="mb-0 opacity-75">تقارير شاملة وإحصائيات مفصلة للنظام</p>
                    <small class="opacity-50">مرحباً <?= htmlspecialchars($userName) ?></small>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light me-2" onclick="refreshReports()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        تحديث
                    </button>
                    <button class="btn btn-success" onclick="exportReport()">
                        <i class="bi bi-download me-1"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>

            <!-- Current Month Statistics -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($current_month_stats['total_transfers']) ?></div>
                        <div class="stats-label">إجمالي التحويلات هذا الشهر</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($current_month_stats['completed_transfers']) ?></div>
                        <div class="stats-label">التحويلات المكتملة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number">$<?= number_format($current_month_stats['total_amount'], 0) ?></div>
                        <div class="stats-label">إجمالي المبلغ</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number">$<?= number_format($current_month_stats['total_fees'], 0) ?></div>
                        <div class="stats-label">إجمالي الرسوم</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Date Filter -->
            <div class="filter-section">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ:</label>
                        <input type="date" class="form-control" id="dateFrom" value="<?= date('Y-m-01') ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ:</label>
                        <input type="date" class="form-control" id="dateTo" value="<?= date('Y-m-d') ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary w-100" onclick="updateReports()">
                            <i class="bi bi-search me-1"></i>
                            تحديث التقارير
                        </button>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <select class="form-select" id="reportType">
                            <option value="all">جميع التقارير</option>
                            <option value="financial">التقرير المالي</option>
                            <option value="daily">التقرير اليومي</option>
                            <option value="country">تقرير الدول</option>
                            <option value="users">تقرير المستخدمين</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Financial Metrics -->
            <div class="row" id="financialMetrics">
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value" id="totalTransfers">-</div>
                        <div class="metric-label">إجمالي التحويلات</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value" id="completedTransfers">-</div>
                        <div class="metric-label">التحويلات المكتملة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value" id="totalAmount">-</div>
                        <div class="metric-label">إجمالي المبلغ</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value" id="averageAmount">-</div>
                        <div class="metric-label">متوسط المبلغ</div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="row">
                <div class="col-md-8">
                    <div class="report-card">
                        <div class="report-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-bar-chart me-2"></i>
                                التحويلات اليومية
                            </h5>
                        </div>
                        <div class="report-card-body">
                            <div class="chart-container">
                                <canvas id="dailyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="report-card">
                        <div class="report-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-pie-chart me-2"></i>
                                توزيع الدول
                            </h5>
                        </div>
                        <div class="report-card-body">
                            <div class="chart-container">
                                <canvas id="countryChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tables Section -->
            <div class="row">
                <div class="col-md-6">
                    <div class="table-container">
                        <div class="report-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-flag me-2"></i>
                                أفضل الدول
                            </h5>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الدولة</th>
                                        <th>التحويلات</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody id="countryTableBody">
                                    <tr>
                                        <td colspan="3" class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="table-container">
                        <div class="report-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-people me-2"></i>
                                أفضل المستخدمين
                            </h5>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>التحويلات</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody id="userTableBody">
                                    <tr>
                                        <td colspan="3" class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        let dailyChart, countryChart;

        $(document).ready(function() {
            updateReports();
        });

        function updateReports() {
            const dateFrom = $('#dateFrom').val();
            const dateTo = $('#dateTo').val();

            // Update financial metrics
            updateFinancialReport(dateFrom, dateTo);

            // Update charts
            updateDailyChart(dateFrom, dateTo);
            updateCountryChart(dateFrom, dateTo);

            // Update tables
            updateCountryTable(dateFrom, dateTo);
            updateUserTable(dateFrom, dateTo);
        }

        function updateFinancialReport(dateFrom, dateTo) {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_financial_report',
                    date_from: dateFrom,
                    date_to: dateTo
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const data = response.data;
                        $('#totalTransfers').text(formatNumber(data.total_transfers));
                        $('#completedTransfers').text(formatNumber(data.completed_transfers));
                        $('#totalAmount').text('$' + formatNumber(data.total_amount));
                        $('#averageAmount').text('$' + formatNumber(data.average_amount));
                    }
                },
                error: function() {
                    showAlert('خطأ في تحميل التقرير المالي', 'danger');
                }
            });
        }

        function updateDailyChart(dateFrom, dateTo) {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_daily_chart',
                    date_from: dateFrom,
                    date_to: dateTo
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const data = response.data;

                        const labels = data.map(item => formatDate(item.date));
                        const transfers = data.map(item => item.transfers);
                        const amounts = data.map(item => item.amount);

                        const ctx = document.getElementById('dailyChart').getContext('2d');

                        if (dailyChart) {
                            dailyChart.destroy();
                        }

                        dailyChart = new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: 'عدد التحويلات',
                                    data: transfers,
                                    borderColor: 'rgb(75, 192, 192)',
                                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                    yAxisID: 'y'
                                }, {
                                    label: 'المبلغ ($)',
                                    data: amounts,
                                    borderColor: 'rgb(255, 99, 132)',
                                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                    yAxisID: 'y1'
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                interaction: {
                                    mode: 'index',
                                    intersect: false,
                                },
                                scales: {
                                    x: {
                                        display: true,
                                        title: {
                                            display: true,
                                            text: 'التاريخ'
                                        }
                                    },
                                    y: {
                                        type: 'linear',
                                        display: true,
                                        position: 'left',
                                        title: {
                                            display: true,
                                            text: 'عدد التحويلات'
                                        }
                                    },
                                    y1: {
                                        type: 'linear',
                                        display: true,
                                        position: 'right',
                                        title: {
                                            display: true,
                                            text: 'المبلغ ($)'
                                        },
                                        grid: {
                                            drawOnChartArea: false,
                                        },
                                    }
                                }
                            }
                        });
                    }
                },
                error: function() {
                    showAlert('خطأ في تحميل الرسم البياني اليومي', 'danger');
                }
            });
        }

        function updateCountryChart(dateFrom, dateTo) {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_country_report',
                    date_from: dateFrom,
                    date_to: dateTo
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const data = response.data;

                        const labels = data.map(item => item.country);
                        const amounts = data.map(item => parseFloat(item.amount));

                        const ctx = document.getElementById('countryChart').getContext('2d');

                        if (countryChart) {
                            countryChart.destroy();
                        }

                        countryChart = new Chart(ctx, {
                            type: 'doughnut',
                            data: {
                                labels: labels,
                                datasets: [{
                                    data: amounts,
                                    backgroundColor: [
                                        '#FF6384',
                                        '#36A2EB',
                                        '#FFCE56',
                                        '#4BC0C0',
                                        '#9966FF',
                                        '#FF9F40',
                                        '#FF6384',
                                        '#C9CBCF',
                                        '#4BC0C0',
                                        '#FF6384'
                                    ]
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'bottom'
                                    }
                                }
                            }
                        });
                    }
                },
                error: function() {
                    showAlert('خطأ في تحميل رسم الدول البياني', 'danger');
                }
            });
        }

        function updateCountryTable(dateFrom, dateTo) {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_country_report',
                    date_from: dateFrom,
                    date_to: dateTo
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const data = response.data;
                        const tbody = $('#countryTableBody');
                        tbody.empty();

                        if (data.length === 0) {
                            tbody.append(`
                                <tr>
                                    <td colspan="3" class="text-center py-4">
                                        <i class="bi bi-inbox display-6 text-muted"></i>
                                        <p class="text-muted mt-2">لا توجد بيانات للعرض</p>
                                    </td>
                                </tr>
                            `);
                            return;
                        }

                        data.forEach((item, index) => {
                            tbody.append(`
                                <tr>
                                    <td>
                                        <span class="badge bg-primary me-2">${index + 1}</span>
                                        ${item.country || 'غير محدد'}
                                    </td>
                                    <td>${formatNumber(item.transfers)}</td>
                                    <td class="text-success fw-bold">$${formatNumber(item.amount)}</td>
                                </tr>
                            `);
                        });
                    }
                },
                error: function() {
                    showAlert('خطأ في تحميل جدول الدول', 'danger');
                }
            });
        }

        function updateUserTable(dateFrom, dateTo) {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_user_report',
                    date_from: dateFrom,
                    date_to: dateTo
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const data = response.data;
                        const tbody = $('#userTableBody');
                        tbody.empty();

                        if (data.length === 0) {
                            tbody.append(`
                                <tr>
                                    <td colspan="3" class="text-center py-4">
                                        <i class="bi bi-inbox display-6 text-muted"></i>
                                        <p class="text-muted mt-2">لا توجد بيانات للعرض</p>
                                    </td>
                                </tr>
                            `);
                            return;
                        }

                        data.forEach((item, index) => {
                            tbody.append(`
                                <tr>
                                    <td>
                                        <div>
                                            <span class="badge bg-primary me-2">${index + 1}</span>
                                            <strong>${item.user_name || 'غير محدد'}</strong>
                                            <br>
                                            <small class="text-muted">${item.email || ''}</small>
                                        </div>
                                    </td>
                                    <td>${formatNumber(item.transfers)}</td>
                                    <td class="text-success fw-bold">$${formatNumber(item.amount)}</td>
                                </tr>
                            `);
                        });
                    }
                },
                error: function() {
                    showAlert('خطأ في تحميل جدول المستخدمين', 'danger');
                }
            });
        }

        function refreshReports() {
            updateReports();
            showAlert('تم تحديث التقارير بنجاح', 'success');
        }

        function exportReport() {
            const dateFrom = $('#dateFrom').val();
            const dateTo = $('#dateTo').val();
            const reportType = $('#reportType').val();

            // Create a simple CSV export
            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "تقرير Elite Transfer System\n";
            csvContent += `الفترة: من ${dateFrom} إلى ${dateTo}\n\n`;

            // Add financial summary
            csvContent += "الملخص المالي:\n";
            csvContent += `إجمالي التحويلات,${$('#totalTransfers').text()}\n`;
            csvContent += `التحويلات المكتملة,${$('#completedTransfers').text()}\n`;
            csvContent += `إجمالي المبلغ,${$('#totalAmount').text()}\n`;
            csvContent += `متوسط المبلغ,${$('#averageAmount').text()}\n\n`;

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `elite_transfer_report_${dateFrom}_${dateTo}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showAlert('تم تصدير التقرير بنجاح', 'success');
        }

        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);

            // Auto dismiss after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        }

        function formatNumber(num) {
            if (num === null || num === undefined) return '0';
            return parseFloat(num).toLocaleString('en-US');
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                month: 'short',
                day: 'numeric'
            });
        }
    </script>
</body>
</html>
