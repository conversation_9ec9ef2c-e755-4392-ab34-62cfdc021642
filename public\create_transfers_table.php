<?php

/**
 * Create Transfers Table
 * Elite Transfer System - Create Transfers Table and Sample Data
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';

$db = DatabaseManager::getInstance();

echo "<h2>🔧 إنشاء جدول التحويلات</h2>";

try {
    // Drop existing table
    echo "<p>🗑️ حذف الجدول الموجود...</p>";
    try {
        $db->query("DROP TABLE IF EXISTS transfers");
        echo "<p style='color: green;'>✅ تم حذف الجدول القديم</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ لم يكن هناك جدول للحذف</p>";
    }
    
    // Create transfers table
    echo "<p>🔨 إنشاء جدول التحويلات الجديد...</p>";
    $db->query("
        CREATE TABLE transfers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            transfer_code VARCHAR(20) NOT NULL UNIQUE,
            user_id INT,
            sender_name VARCHAR(255) NOT NULL,
            sender_phone VARCHAR(20),
            sender_country_id INT,
            recipient_name VARCHAR(255) NOT NULL,
            recipient_phone VARCHAR(20),
            recipient_country_id INT,
            amount DECIMAL(15,2) NOT NULL,
            fees DECIMAL(15,2) DEFAULT 0.00,
            total_amount DECIMAL(15,2) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
            payment_method VARCHAR(50) DEFAULT 'cash',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP NULL,
            INDEX idx_transfer_code (transfer_code),
            INDEX idx_user_id (user_id),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at),
            INDEX idx_deleted_at (deleted_at),
            FOREIGN KEY (sender_country_id) REFERENCES countries(id),
            FOREIGN KEY (recipient_country_id) REFERENCES countries(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✅ تم إنشاء جدول التحويلات بنجاح!</p>";
    
    // Get some countries for sample data
    $countries = $db->select("SELECT id, name, code FROM countries LIMIT 10");
    
    if (empty($countries)) {
        echo "<p style='color: red;'>❌ لا توجد بلدان! يجب إنشاء البلدان أولاً.</p>";
        echo "<p><a href='create_countries_simple.php'>إنشاء البلدان أولاً</a></p>";
        exit;
    }
    
    echo "<p>📥 إدراج بيانات التحويلات التجريبية...</p>";
    
    // Sample transfers data
    $sampleTransfers = [
        [
            'sender_name' => 'أحمد محمد علي',
            'sender_phone' => '+966501234567',
            'recipient_name' => 'فاطمة أحمد',
            'recipient_phone' => '+971501234567',
            'amount' => 1000.00,
            'fees' => 25.00,
            'status' => 'completed'
        ],
        [
            'sender_name' => 'محمد عبدالله',
            'sender_phone' => '+966502345678',
            'recipient_name' => 'عائشة محمد',
            'recipient_phone' => '+965501234567',
            'amount' => 500.00,
            'fees' => 15.00,
            'status' => 'completed'
        ],
        [
            'sender_name' => 'سارة أحمد',
            'sender_phone' => '+966503456789',
            'recipient_name' => 'خديجة علي',
            'recipient_phone' => '+974501234567',
            'amount' => 750.00,
            'fees' => 20.00,
            'status' => 'processing'
        ],
        [
            'sender_name' => 'عبدالرحمن محمد',
            'sender_phone' => '+966504567890',
            'recipient_name' => 'زينب أحمد',
            'recipient_phone' => '+973501234567',
            'amount' => 1200.00,
            'fees' => 30.00,
            'status' => 'pending'
        ],
        [
            'sender_name' => 'خالد عبدالله',
            'sender_phone' => '+966505678901',
            'recipient_name' => 'مريم محمد',
            'recipient_phone' => '+968501234567',
            'amount' => 300.00,
            'fees' => 10.00,
            'status' => 'completed'
        ],
        [
            'sender_name' => 'نورا أحمد',
            'sender_phone' => '+966506789012',
            'recipient_name' => 'هدى علي',
            'recipient_phone' => '+962501234567',
            'amount' => 800.00,
            'fees' => 22.00,
            'status' => 'completed'
        ],
        [
            'sender_name' => 'يوسف محمد',
            'sender_phone' => '+966507890123',
            'recipient_name' => 'ليلى أحمد',
            'recipient_phone' => '+961501234567',
            'amount' => 600.00,
            'fees' => 18.00,
            'status' => 'failed'
        ],
        [
            'sender_name' => 'عمر عبدالله',
            'sender_phone' => '+966508901234',
            'recipient_name' => 'رقية محمد',
            'recipient_phone' => '+20501234567',
            'amount' => 1500.00,
            'fees' => 35.00,
            'status' => 'completed'
        ],
        [
            'sender_name' => 'هند أحمد',
            'sender_phone' => '+966509012345',
            'recipient_name' => 'أم كلثوم علي',
            'recipient_phone' => '+212501234567',
            'amount' => 400.00,
            'fees' => 12.00,
            'status' => 'pending'
        ],
        [
            'sender_name' => 'طارق محمد',
            'sender_phone' => '+966500123456',
            'recipient_name' => 'سلمى أحمد',
            'recipient_phone' => '+216501234567',
            'amount' => 900.00,
            'fees' => 25.00,
            'status' => 'completed'
        ]
    ];
    
    $inserted = 0;
    $errors = 0;
    
    foreach ($sampleTransfers as $index => $transfer) {
        try {
            // Generate transfer code
            $transferCode = 'TR' . date('Ymd') . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            
            // Random countries
            $senderCountry = $countries[array_rand($countries)];
            $recipientCountry = $countries[array_rand($countries)];
            
            // Calculate total amount
            $totalAmount = $transfer['amount'] + $transfer['fees'];
            
            // Random dates in the last 30 days
            $daysAgo = rand(0, 30);
            $createdAt = date('Y-m-d H:i:s', strtotime("-$daysAgo days"));
            
            $transferData = [
                'transfer_code' => $transferCode,
                'user_id' => 1, // Admin user
                'sender_name' => $transfer['sender_name'],
                'sender_phone' => $transfer['sender_phone'],
                'sender_country_id' => $senderCountry['id'],
                'recipient_name' => $transfer['recipient_name'],
                'recipient_phone' => $transfer['recipient_phone'],
                'recipient_country_id' => $recipientCountry['id'],
                'amount' => $transfer['amount'],
                'fees' => $transfer['fees'],
                'total_amount' => $totalAmount,
                'exchange_rate' => 1.0000,
                'status' => $transfer['status'],
                'payment_method' => 'cash',
                'notes' => 'تحويل تجريبي',
                'created_at' => $createdAt,
                'updated_at' => $createdAt
            ];
            
            $result = $db->insert('transfers', $transferData);
            
            if ($result) {
                $inserted++;
                echo "<span style='color: green;'>✅</span> {$transferCode} - {$transfer['sender_name']} → {$transfer['recipient_name']} ({$transfer['status']})<br>";
            } else {
                $errors++;
                echo "<span style='color: red;'>❌</span> فشل في إدراج {$transferCode}<br>";
            }
        } catch (Exception $e) {
            $errors++;
            echo "<span style='color: red;'>❌</span> خطأ في التحويل {$index + 1}: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<br><p style='color: green;'><strong>✅ تم إدراج $inserted تحويل بنجاح!</strong></p>";
    if ($errors > 0) {
        echo "<p style='color: orange;'>⚠️ فشل في إدراج $errors تحويل</p>";
    }
    
    // Verify the data
    $totalTransfers = $db->selectOne("SELECT COUNT(*) as count FROM transfers")['count'];
    echo "<p><strong>إجمالي التحويلات في قاعدة البيانات: $totalTransfers</strong></p>";
    
    // Test statistics
    echo "<h3>🧪 اختبار الإحصائيات:</h3>";
    
    try {
        $stats = $db->getStatistics();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>الإحصائية</th><th>القيمة</th></tr>";
        
        foreach ($stats as $key => $value) {
            echo "<tr>";
            echo "<td>" . ucfirst(str_replace('_', ' ', $key)) . "</td>";
            echo "<td><strong>$value</strong></td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        echo "<p style='color: green;'><strong>🎉 الإحصائيات تعمل بشكل مثالي!</strong></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في الإحصائيات: " . $e->getMessage() . "</p>";
    }
    
    // Show sample data
    echo "<h3>📊 عينة من التحويلات:</h3>";
    $sampleData = $db->select("
        SELECT t.*, sc.name as sender_country, rc.name as recipient_country
        FROM transfers t
        LEFT JOIN countries sc ON t.sender_country_id = sc.id
        LEFT JOIN countries rc ON t.recipient_country_id = rc.id
        ORDER BY t.created_at DESC
        LIMIT 5
    ");
    
    if (!empty($sampleData)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>الكود</th><th>المرسل</th><th>المستقبل</th><th>المبلغ</th><th>الحالة</th><th>التاريخ</th></tr>";
        
        foreach ($sampleData as $transfer) {
            echo "<tr>";
            echo "<td>{$transfer['transfer_code']}</td>";
            echo "<td>{$transfer['sender_name']}<br><small>{$transfer['sender_country']}</small></td>";
            echo "<td>{$transfer['recipient_name']}<br><small>{$transfer['recipient_country']}</small></td>";
            echo "<td>\${$transfer['total_amount']}</td>";
            echo "<td><span style='color: " . ($transfer['status'] == 'completed' ? 'green' : ($transfer['status'] == 'failed' ? 'red' : 'orange')) . ";'>{$transfer['status']}</span></td>";
            echo "<td>" . date('Y-m-d', strtotime($transfer['created_at'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<h3>🔗 الروابط للاختبار:</h3>";
    echo "<p><a href='dashboard_advanced.php' target='_blank' style='color: #667eea; font-weight: bold;'>📊 لوحة التحكم</a></p>";
    echo "<p><a href='analytics_advanced.php' target='_blank' style='color: #667eea; font-weight: bold;'>📈 التحليلات</a></p>";
    echo "<p><a href='reports_advanced.php' target='_blank' style='color: #667eea; font-weight: bold;'>📋 التقارير</a></p>";
    
    echo "<br><p style='color: blue; font-size: 18px;'><strong>🚀 تم إعداد التحويلات بنجاح! يمكنك الآن اختبار لوحة التحكم.</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ خطأ عام: " . $e->getMessage() . "</strong></p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء التحويلات - <?= SYSTEM_NAME ?></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        h2, h3 {
            color: #333;
        }
        
        table {
            background: white;
            margin: 20px 0;
            width: 100%;
            border-collapse: collapse;
        }
        
        th {
            background: #667eea;
            color: white;
            padding: 10px;
        }
        
        td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        
        a {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
