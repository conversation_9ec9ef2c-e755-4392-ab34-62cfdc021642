<?php

/**
 * Simple Countries Creation Script
 * Elite Transfer System - Create Countries Table and Data
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';

$db = DatabaseManager::getInstance();

echo "<h2>🔧 إنشاء جدول البلدان البسيط</h2>";

try {
    // Drop existing table
    echo "<p>🗑️ حذف الجدول الموجود...</p>";
    try {
        $db->query("DROP TABLE IF EXISTS countries");
        echo "<p style='color: green;'>✅ تم حذف الجدول القديم</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ لم يكن هناك جدول للحذف</p>";
    }
    
    // Create simple countries table
    echo "<p>🔨 إنشاء جدول البلدان الجديد...</p>";
    $db->query("
        CREATE TABLE countries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            currency VARCHAR(3) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            status ENUM('active', 'inactive') DEFAULT 'active',
            flag_url VARCHAR(500) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP NULL,
            INDEX idx_code (code),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✅ تم إنشاء جدول البلدان بنجاح!</p>";
    
    // Insert countries data
    echo "<p>📥 إدراج بيانات البلدان...</p>";
    
    $countries = [
        ['name' => 'المملكة العربية السعودية', 'code' => 'SA', 'currency' => 'SAR', 'exchange_rate' => 1.0000],
        ['name' => 'الإمارات العربية المتحدة', 'code' => 'AE', 'currency' => 'AED', 'exchange_rate' => 1.0200],
        ['name' => 'الكويت', 'code' => 'KW', 'currency' => 'KWD', 'exchange_rate' => 0.3000],
        ['name' => 'قطر', 'code' => 'QA', 'currency' => 'QAR', 'exchange_rate' => 3.6400],
        ['name' => 'البحرين', 'code' => 'BH', 'currency' => 'BHD', 'exchange_rate' => 0.3800],
        ['name' => 'عمان', 'code' => 'OM', 'currency' => 'OMR', 'exchange_rate' => 0.3800],
        ['name' => 'الأردن', 'code' => 'JO', 'currency' => 'JOD', 'exchange_rate' => 0.7100],
        ['name' => 'لبنان', 'code' => 'LB', 'currency' => 'LBP', 'exchange_rate' => 1507.5000],
        ['name' => 'مصر', 'code' => 'EG', 'currency' => 'EGP', 'exchange_rate' => 30.9000],
        ['name' => 'المغرب', 'code' => 'MA', 'currency' => 'MAD', 'exchange_rate' => 10.1000],
        ['name' => 'تونس', 'code' => 'TN', 'currency' => 'TND', 'exchange_rate' => 3.1000],
        ['name' => 'الجزائر', 'code' => 'DZ', 'currency' => 'DZD', 'exchange_rate' => 134.4000],
        ['name' => 'العراق', 'code' => 'IQ', 'currency' => 'IQD', 'exchange_rate' => 1460.0000],
        ['name' => 'سوريا', 'code' => 'SY', 'currency' => 'SYP', 'exchange_rate' => 2512.0000],
        ['name' => 'اليمن', 'code' => 'YE', 'currency' => 'YER', 'exchange_rate' => 250.0000],
        ['name' => 'السودان', 'code' => 'SD', 'currency' => 'SDG', 'exchange_rate' => 601.0000],
        ['name' => 'ليبيا', 'code' => 'LY', 'currency' => 'LYD', 'exchange_rate' => 4.8000],
        ['name' => 'الصومال', 'code' => 'SO', 'currency' => 'SOS', 'exchange_rate' => 570.0000],
        ['name' => 'جيبوتي', 'code' => 'DJ', 'currency' => 'DJF', 'exchange_rate' => 177.7000],
        ['name' => 'موريتانيا', 'code' => 'MR', 'currency' => 'MRU', 'exchange_rate' => 36.7000],
        ['name' => 'الولايات المتحدة', 'code' => 'US', 'currency' => 'USD', 'exchange_rate' => 1.0000],
        ['name' => 'المملكة المتحدة', 'code' => 'GB', 'currency' => 'GBP', 'exchange_rate' => 0.8000],
        ['name' => 'ألمانيا', 'code' => 'DE', 'currency' => 'EUR', 'exchange_rate' => 0.9000],
        ['name' => 'فرنسا', 'code' => 'FR', 'currency' => 'EUR', 'exchange_rate' => 0.9000],
        ['name' => 'كندا', 'code' => 'CA', 'currency' => 'CAD', 'exchange_rate' => 1.3500],
        ['name' => 'أستراليا', 'code' => 'AU', 'currency' => 'AUD', 'exchange_rate' => 1.5000],
        ['name' => 'اليابان', 'code' => 'JP', 'currency' => 'JPY', 'exchange_rate' => 150.0000],
        ['name' => 'الصين', 'code' => 'CN', 'currency' => 'CNY', 'exchange_rate' => 7.2000],
        ['name' => 'الهند', 'code' => 'IN', 'currency' => 'INR', 'exchange_rate' => 83.0000],
        ['name' => 'باكستان', 'code' => 'PK', 'currency' => 'PKR', 'exchange_rate' => 280.0000]
    ];
    
    $inserted = 0;
    $errors = 0;
    
    foreach ($countries as $country) {
        try {
            $result = $db->insert('countries', [
                'name' => $country['name'],
                'code' => $country['code'],
                'currency' => $country['currency'],
                'exchange_rate' => $country['exchange_rate'],
                'status' => 'active'
            ]);
            
            if ($result) {
                $inserted++;
                echo "<span style='color: green;'>✅</span> {$country['name']} ({$country['code']})<br>";
            } else {
                $errors++;
                echo "<span style='color: red;'>❌</span> فشل في إدراج {$country['name']}<br>";
            }
        } catch (Exception $e) {
            $errors++;
            echo "<span style='color: red;'>❌</span> خطأ في {$country['name']}: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<br><p style='color: green;'><strong>✅ تم إدراج $inserted بلد بنجاح!</strong></p>";
    if ($errors > 0) {
        echo "<p style='color: orange;'>⚠️ فشل في إدراج $errors بلد</p>";
    }
    
    // Verify the data
    $totalCountries = $db->selectOne("SELECT COUNT(*) as count FROM countries")['count'];
    echo "<p><strong>إجمالي البلدان في قاعدة البيانات: $totalCountries</strong></p>";
    
    // Test the query that countries_management.php uses
    echo "<h3>🧪 اختبار الاستعلام:</h3>";
    
    $testQuery = "
        SELECT 
            c.*,
            0 as transfer_count,
            0 as total_volume
        FROM countries c
        WHERE (deleted_at IS NULL OR deleted_at = '')
        ORDER BY c.name ASC
        LIMIT 10
    ";
    
    $testResults = $db->select($testQuery);
    echo "<p>عدد النتائج من الاستعلام: <strong>" . count($testResults) . "</strong></p>";
    
    if (!empty($testResults)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الرمز</th><th>العملة</th><th>سعر الصرف</th><th>الحالة</th></tr>";
        
        foreach (array_slice($testResults, 0, 5) as $country) {
            echo "<tr>";
            echo "<td>{$country['id']}</td>";
            echo "<td>{$country['name']}</td>";
            echo "<td>{$country['code']}</td>";
            echo "<td>{$country['currency']}</td>";
            echo "<td>{$country['exchange_rate']}</td>";
            echo "<td>{$country['status']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        echo "<p style='color: green;'><strong>🎉 الاستعلام يعمل بشكل مثالي!</strong></p>";
    } else {
        echo "<p style='color: red;'>❌ الاستعلام لا يُرجع نتائج!</p>";
    }
    
    // Test AJAX response format
    echo "<h3>📡 اختبار تنسيق AJAX:</h3>";
    
    $ajaxResponse = [
        'success' => true,
        'countries' => array_slice($testResults, 0, 3),
        'total' => intval($totalCountries),
        'page' => 1,
        'limit' => 10,
        'pages' => ceil($totalCountries / 10)
    ];
    
    echo "<pre>" . json_encode($ajaxResponse, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";
    
    echo "<h3>🔗 الروابط للاختبار:</h3>";
    echo "<p><a href='countries_management.php' target='_blank' style='color: #667eea; font-weight: bold;'>🌍 إدارة البلدان</a></p>";
    echo "<p><a href='dashboard_advanced.php' target='_blank' style='color: #667eea; font-weight: bold;'>📊 لوحة التحكم</a></p>";
    echo "<p><a href='analytics_advanced.php' target='_blank' style='color: #667eea; font-weight: bold;'>📈 التحليلات</a></p>";
    echo "<p><a href='reports_advanced.php' target='_blank' style='color: #667eea; font-weight: bold;'>📋 التقارير</a></p>";
    
    echo "<br><p style='color: blue; font-size: 18px;'><strong>🚀 تم إعداد البلدان بنجاح! يمكنك الآن اختبار الصفحات.</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ خطأ عام: " . $e->getMessage() . "</strong></p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء البلدان - <?= SYSTEM_NAME ?></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        h2, h3 {
            color: #333;
        }
        
        table {
            background: white;
            margin: 20px 0;
            width: 100%;
            border-collapse: collapse;
        }
        
        th {
            background: #667eea;
            color: white;
            padding: 10px;
        }
        
        td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        
        a {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
</body>
</html>
