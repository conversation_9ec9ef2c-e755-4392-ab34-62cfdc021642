<?php

echo "🔗 Testing Database Connection - Elite Transfer System\n\n";

// Function to test MySQL connection
function testMySQLConnection() {
    echo "🔄 Testing MySQL Connection...\n";

    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $database = $_ENV['DB_DATABASE'] ?? 'elite_transfer';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';

    try {
        $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);

        $version = $pdo->query("SELECT VERSION()")->fetchColumn();
        echo "✅ MySQL Connection: SUCCESS\n";
        echo "   Version: $version\n";
        echo "   Host: $host:$port\n";
        echo "   Database: $database\n";
        echo "   Username: $username\n\n";

        return $pdo;

    } catch (PDOException $e) {
        echo "❌ MySQL Connection: FAILED\n";
        echo "   Error: " . $e->getMessage() . "\n\n";
        return false;
    }
}

// Function to test SQLite connection
function testSQLiteConnection() {
    echo "🔄 Testing SQLite Connection...\n";

    $database = $_ENV['DB_DATABASE'] ?? 'database/elite_transfer_production.db';
    $fullPath = __DIR__ . '/' . $database;

    try {
        if (!file_exists($fullPath)) {
            echo "❌ SQLite Database file not found: $fullPath\n\n";
            return false;
        }

        $dsn = "sqlite:$fullPath";
        $pdo = new PDO($dsn, null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);

        $version = $pdo->query("SELECT sqlite_version()")->fetchColumn();
        echo "✅ SQLite Connection: SUCCESS\n";
        echo "   Version: $version\n";
        echo "   Database: $database\n";
        echo "   File size: " . number_format(filesize($fullPath)) . " bytes\n\n";

        return $pdo;

    } catch (PDOException $e) {
        echo "❌ SQLite Connection: FAILED\n";
        echo "   Error: " . $e->getMessage() . "\n\n";
        return false;
    }
}

try {
    // Include the database manager
    require_once 'public/includes/database_manager.php';

    echo "📋 Loading Database Manager...\n";

    // Get database instance
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();

    echo "✅ Database Manager loaded successfully\n\n";
    
    // Get connection info
    $connectionInfo = $dbManager->getConnectionInfo();
    echo "📊 Connection Information:\n";
    echo "  Type: {$connectionInfo['type']}\n";
    echo "  Database: {$connectionInfo['database']}\n";
    echo "  Host: {$connectionInfo['host']}\n";
    echo "  Charset: {$connectionInfo['charset']}\n\n";
    
    // Test basic connection
    echo "🧪 Testing basic connection...\n";
    $version = $db->query("SELECT sqlite_version()")->fetchColumn();
    echo "✅ SQLite Version: $version\n\n";
    
    // Test tables existence
    echo "📋 Checking tables...\n";
    $tables = ['users', 'countries', 'transfers', 'exchange_rates', 'system_settings'];
    
    foreach ($tables as $table) {
        if ($dbManager->tableExists($table)) {
            $count = $dbManager->getTableCount($table);
            echo "  ✅ $table: $count records\n";
        } else {
            echo "  ❌ $table: not found\n";
        }
    }
    
    echo "\n🔍 Testing helper functions...\n";
    
    // Test helper functions
    $totalUsers = dbFetchColumn("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL");
    echo "✅ dbFetchColumn(): $totalUsers users\n";
    
    $firstUser = dbFetchOne("SELECT name, email, role FROM users WHERE deleted_at IS NULL LIMIT 1");
    if ($firstUser) {
        echo "✅ dbFetchOne(): {$firstUser['name']} ({$firstUser['role']})\n";
    }
    
    $allCountries = dbFetchAll("SELECT name, code FROM countries WHERE is_active = 1 LIMIT 3");
    echo "✅ dbFetchAll(): " . count($allCountries) . " countries\n";
    
    foreach ($allCountries as $country) {
        echo "    - {$country['name']} ({$country['code']})\n";
    }
    
    // Test statistics
    echo "\n📊 Database Statistics:\n";
    
    $stats = [
        'Users' => dbFetchColumn("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL"),
        'Active Users' => dbFetchColumn("SELECT COUNT(*) FROM users WHERE status = 'active' AND deleted_at IS NULL"),
        'Countries' => dbFetchColumn("SELECT COUNT(*) FROM countries WHERE is_active = 1"),
        'Exchange Rates' => dbFetchColumn("SELECT COUNT(*) FROM exchange_rates"),
        'System Settings' => dbFetchColumn("SELECT COUNT(*) FROM system_settings"),
        'Transfers' => dbFetchColumn("SELECT COUNT(*) FROM transfers WHERE deleted_at IS NULL")
    ];
    
    foreach ($stats as $label => $count) {
        echo "  $label: " . number_format($count) . "\n";
    }
    
    // Test user roles
    echo "\n👥 User Roles Distribution:\n";
    $roles = dbFetchAll("
        SELECT role, COUNT(*) as count 
        FROM users 
        WHERE deleted_at IS NULL 
        GROUP BY role 
        ORDER BY count DESC
    ");
    
    foreach ($roles as $role) {
        echo "  {$role['role']}: {$role['count']} users\n";
    }
    
    // Test recent activity
    echo "\n⏰ Recent Activity:\n";
    $recentUsers = dbFetchAll("
        SELECT name, email, role, created_at 
        FROM users 
        WHERE deleted_at IS NULL 
        ORDER BY created_at DESC 
        LIMIT 3
    ");
    
    foreach ($recentUsers as $user) {
        $date = date('Y-m-d H:i', strtotime($user['created_at']));
        echo "  {$user['name']} ({$user['role']}) - $date\n";
    }
    
    // Test environment variables
    echo "\n🔧 Environment Configuration:\n";
    $envVars = [
        'APP_ENV' => $_ENV['APP_ENV'] ?? 'not set',
        'APP_DEBUG' => $_ENV['APP_DEBUG'] ?? 'not set',
        'DB_CONNECTION' => $_ENV['DB_CONNECTION'] ?? 'not set',
        'DB_DATABASE' => $_ENV['DB_DATABASE'] ?? 'not set'
    ];
    
    foreach ($envVars as $key => $value) {
        echo "  $key: $value\n";
    }
    
    // Performance test
    echo "\n⚡ Performance Test:\n";
    $start = microtime(true);
    
    for ($i = 0; $i < 10; $i++) {
        dbFetchColumn("SELECT COUNT(*) FROM users");
    }
    
    $end = microtime(true);
    $duration = round(($end - $start) * 1000, 2);
    echo "✅ 10 queries executed in {$duration}ms\n";
    
    // Test transaction
    echo "\n💾 Testing Transaction:\n";
    try {
        $dbManager->beginTransaction();
        
        // Test insert (will be rolled back)
        $testData = [
            'user_code' => 'TEST' . time(),
            'name' => 'Test User',
            'email' => 'test' . time() . '@example.com',
            'password_hash' => password_hash('test123', PASSWORD_DEFAULT),
            'role' => 'customer',
            'status' => 'active',
            'country_id' => 1
        ];
        
        $userId = $dbManager->insert('users', $testData);
        echo "✅ Test user inserted with ID: $userId\n";
        
        // Rollback to clean up
        $dbManager->rollback();
        echo "✅ Transaction rolled back successfully\n";
        
    } catch (Exception $e) {
        $dbManager->rollback();
        echo "❌ Transaction test failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n🎉 Database connection test completed successfully!\n";
    echo "🔗 Connection is stable and ready for production use.\n";
    echo "\n📝 Test Summary:\n";
    echo "  ✅ Database connection: Working\n";
    echo "  ✅ Tables structure: Complete\n";
    echo "  ✅ Helper functions: Working\n";
    echo "  ✅ Statistics: Available\n";
    echo "  ✅ Performance: Good\n";
    echo "  ✅ Transactions: Working\n";
    echo "  ✅ Environment: Configured\n";

} catch (Exception $e) {
    echo "❌ Database connection test failed!\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "\n🔧 Troubleshooting:\n";
    echo "1. Check if database file exists: database/elite_transfer_production.db\n";
    echo "2. Verify .env file configuration\n";
    echo "3. Ensure proper file permissions\n";
    echo "4. Run setup_production_database.php if needed\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🔧 ADDITIONAL CONNECTION TESTS\n";
echo str_repeat("=", 60) . "\n\n";

// Test different connection types
$currentConnection = $_ENV['DB_CONNECTION'] ?? 'sqlite';

if ($currentConnection === 'sqlite') {
    echo "📊 Current connection: SQLite\n";
    testSQLiteConnection();

    echo "🔄 Testing alternative MySQL connection...\n";
    // Temporarily change to MySQL for testing
    $_ENV['DB_CONNECTION'] = 'mysql';
    $_ENV['DB_HOST'] = 'localhost';
    $_ENV['DB_PORT'] = '3306';
    $_ENV['DB_DATABASE'] = 'elite_transfer';
    $_ENV['DB_USERNAME'] = 'root';
    $_ENV['DB_PASSWORD'] = '';

    testMySQLConnection();

    // Restore original settings
    $_ENV['DB_CONNECTION'] = 'sqlite';

} else {
    echo "📊 Current connection: MySQL\n";
    testMySQLConnection();

    echo "🔄 Testing alternative SQLite connection...\n";
    testSQLiteConnection();
}

echo "\n🔍 CONNECTION RECOMMENDATIONS:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";

if (file_exists(__DIR__ . '/database/elite_transfer_production.db')) {
    echo "✅ SQLite database is available and ready\n";
    echo "   📁 File: database/elite_transfer_production.db\n";
    echo "   📊 Size: " . number_format(filesize(__DIR__ . '/database/elite_transfer_production.db')) . " bytes\n";
    echo "   🚀 Recommended for: Development, Small to Medium applications\n";
    echo "   ⚡ Advantages: No server setup, fast for read operations, portable\n\n";
}

// Check if MySQL is available
$mysqlAvailable = false;
try {
    $testPdo = new PDO("mysql:host=localhost;port=3306", 'root', '', [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    $mysqlAvailable = true;
    $testPdo = null;
} catch (Exception $e) {
    // MySQL not available
}

if ($mysqlAvailable) {
    echo "✅ MySQL server is available\n";
    echo "   🌐 Host: localhost:3306\n";
    echo "   🚀 Recommended for: Production, High-traffic applications\n";
    echo "   ⚡ Advantages: Better concurrency, advanced features, scalability\n\n";
} else {
    echo "❌ MySQL server is not available\n";
    echo "   💡 To use MySQL: Install XAMPP/WAMP or MySQL server\n";
    echo "   🔧 Start MySQL service in XAMPP Control Panel\n\n";
}

echo "🎯 CURRENT SETUP ANALYSIS:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
echo "Current DB Type: " . ($currentConnection === 'sqlite' ? 'SQLite' : 'MySQL') . "\n";
echo "Environment: " . ($_ENV['APP_ENV'] ?? 'production') . "\n";
echo "Debug Mode: " . ($_ENV['APP_DEBUG'] ?? 'false') . "\n";

if ($currentConnection === 'sqlite') {
    echo "Status: ✅ Working with SQLite\n";
    echo "Performance: 🟢 Good for current setup\n";
    echo "Scalability: 🟡 Limited for high-traffic\n";
} else {
    echo "Status: " . ($mysqlAvailable ? "✅ Working with MySQL" : "❌ MySQL not available") . "\n";
    echo "Performance: 🟢 Excellent for production\n";
    echo "Scalability: 🟢 High scalability\n";
}

echo "\n💡 SWITCHING DATABASE GUIDE:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
echo "To switch to MySQL:\n";
echo "1. Start MySQL in XAMPP Control Panel\n";
echo "2. Create database 'elite_transfer'\n";
echo "3. Update .env file:\n";
echo "   DB_CONNECTION=mysql\n";
echo "   DB_HOST=localhost\n";
echo "   DB_PORT=3306\n";
echo "   DB_DATABASE=elite_transfer\n";
echo "   DB_USERNAME=root\n";
echo "   DB_PASSWORD=\n";
echo "4. Run: php setup_production_database.php\n\n";

echo "To switch to SQLite:\n";
echo "1. Update .env file:\n";
echo "   DB_CONNECTION=sqlite\n";
echo "   DB_DATABASE=database/elite_transfer_production.db\n";
echo "2. Ensure database file exists\n";
echo "3. Run: php setup_production_database.php (if needed)\n\n";

echo "🔚 Connection test completed!\n";

?>
