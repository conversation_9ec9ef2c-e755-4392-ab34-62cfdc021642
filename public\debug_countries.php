<?php

/**
 * Debug Countries Table
 * Elite Transfer System - Debug Countries Issues
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';

$db = DatabaseManager::getInstance();

echo "<h2>🔍 فحص جدول البلدان</h2>";

try {
    // Check if countries table exists
    echo "<h3>1. فحص وجود الجدول:</h3>";
    $tables = $db->select("SHOW TABLES LIKE 'countries'");
    
    if (empty($tables)) {
        echo "<p style='color: red;'>❌ جدول البلدان غير موجود!</p>";
        
        // Create the table
        echo "<p>🔧 إنشاء جدول البلدان...</p>";
        $db->query("
            CREATE TABLE countries (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                code VARCHAR(3) NOT NULL UNIQUE,
                currency VARCHAR(3) NOT NULL,
                exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
                status ENUM('active', 'inactive') DEFAULT 'active',
                flag_url VARCHAR(500),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                deleted_at TIMESTAMP NULL,
                INDEX idx_code (code),
                INDEX idx_status (status),
                INDEX idx_deleted_at (deleted_at)
            )
        ");
        echo "<p style='color: green;'>✅ تم إنشاء جدول البلدان!</p>";
    } else {
        echo "<p style='color: green;'>✅ جدول البلدان موجود!</p>";
    }
    
    // Check table structure
    echo "<h3>2. فحص هيكل الجدول:</h3>";
    $structure = $db->select("DESCRIBE countries");
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($structure as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check data count
    echo "<h3>3. فحص البيانات:</h3>";
    $count = $db->selectOne("SELECT COUNT(*) as count FROM countries")['count'];
    echo "<p>عدد البلدان: <strong>$count</strong></p>";
    
    if ($count == 0) {
        echo "<p style='color: orange;'>⚠️ لا توجد بيانات في جدول البلدان!</p>";
        
        // Insert sample data
        echo "<p>📥 إدراج بيانات تجريبية...</p>";
        
        $sampleCountries = [
            ['name' => 'المملكة العربية السعودية', 'code' => 'SA', 'currency' => 'SAR', 'exchange_rate' => 1.0000],
            ['name' => 'الإمارات العربية المتحدة', 'code' => 'AE', 'currency' => 'AED', 'exchange_rate' => 1.0200],
            ['name' => 'الكويت', 'code' => 'KW', 'currency' => 'KWD', 'exchange_rate' => 0.3000],
            ['name' => 'قطر', 'code' => 'QA', 'currency' => 'QAR', 'exchange_rate' => 3.6400],
            ['name' => 'البحرين', 'code' => 'BH', 'currency' => 'BHD', 'exchange_rate' => 0.3800],
            ['name' => 'عمان', 'code' => 'OM', 'currency' => 'OMR', 'exchange_rate' => 0.3800],
            ['name' => 'الأردن', 'code' => 'JO', 'currency' => 'JOD', 'exchange_rate' => 0.7100],
            ['name' => 'لبنان', 'code' => 'LB', 'currency' => 'LBP', 'exchange_rate' => 1507.5000],
            ['name' => 'مصر', 'code' => 'EG', 'currency' => 'EGP', 'exchange_rate' => 30.9000],
            ['name' => 'المغرب', 'code' => 'MA', 'currency' => 'MAD', 'exchange_rate' => 10.1000]
        ];
        
        $inserted = 0;
        foreach ($sampleCountries as $country) {
            try {
                $countryData = [
                    'name' => $country['name'],
                    'code' => $country['code'],
                    'currency' => $country['currency'],
                    'exchange_rate' => $country['exchange_rate'],
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                if ($db->insert('countries', $countryData)) {
                    $inserted++;
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>خطأ في إدراج {$country['name']}: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p style='color: green;'>✅ تم إدراج $inserted بلد!</p>";
        
        // Update count
        $count = $db->selectOne("SELECT COUNT(*) as count FROM countries")['count'];
        echo "<p>العدد الجديد: <strong>$count</strong></p>";
    }
    
    // Show sample data
    echo "<h3>4. عينة من البيانات:</h3>";
    $sampleData = $db->select("SELECT * FROM countries LIMIT 5");
    
    if (!empty($sampleData)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الرمز</th><th>العملة</th><th>سعر الصرف</th><th>الحالة</th></tr>";
        
        foreach ($sampleData as $country) {
            echo "<tr>";
            echo "<td>{$country['id']}</td>";
            echo "<td>{$country['name']}</td>";
            echo "<td>{$country['code']}</td>";
            echo "<td>{$country['currency']}</td>";
            echo "<td>{$country['exchange_rate']}</td>";
            echo "<td>{$country['status']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد بيانات للعرض!</p>";
    }
    
    // Test the get_countries action
    echo "<h3>5. اختبار استعلام البلدان:</h3>";
    
    try {
        $conditions = ["(deleted_at IS NULL OR deleted_at = '')"];
        $params = [];
        $whereClause = implode(' AND ', $conditions);
        
        // Test total count
        $totalQuery = "SELECT COUNT(*) as total FROM countries WHERE $whereClause";
        $total = $db->selectOne($totalQuery, $params)['total'];
        echo "<p>إجمالي البلدان (مع الشروط): <strong>$total</strong></p>";
        
        // Test countries query
        $countriesQuery = "
            SELECT 
                c.*,
                0 as transfer_count,
                0 as total_volume
            FROM countries c
            WHERE $whereClause
            ORDER BY c.name ASC
            LIMIT 10
        ";
        $countries = $db->select($countriesQuery, $params);
        
        echo "<p>عدد البلدان المُسترجعة: <strong>" . count($countries) . "</strong></p>";
        
        if (!empty($countries)) {
            echo "<p style='color: green;'>✅ استعلام البلدان يعمل بشكل صحيح!</p>";
        } else {
            echo "<p style='color: red;'>❌ استعلام البلدان لا يُرجع نتائج!</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام البلدان: " . $e->getMessage() . "</p>";
    }
    
    // Test AJAX endpoint
    echo "<h3>6. اختبار AJAX endpoint:</h3>";
    echo "<button onclick='testAjax()'>اختبار تحميل البلدان عبر AJAX</button>";
    echo "<div id='ajaxResult'></div>";
    
    echo "<h3>7. الروابط للاختبار:</h3>";
    echo "<p><a href='countries_management.php' target='_blank'>إدارة البلدان</a></p>";
    echo "<p><a href='dashboard_advanced.php' target='_blank'>لوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص البلدان - <?= SYSTEM_NAME ?></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        h2, h3 {
            color: #333;
        }
        
        table {
            background: white;
            margin: 20px 0;
            width: 100%;
        }
        
        th {
            background: #667eea;
            color: white;
            padding: 10px;
        }
        
        td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        
        button {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        a {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
</head>
<body>

<script>
function testAjax() {
    $('#ajaxResult').html('<p>جاري الاختبار...</p>');
    
    $.ajax({
        url: 'countries_management.php',
        method: 'POST',
        data: {
            action: 'get_countries',
            page: 1,
            limit: 5,
            search: '',
            status: ''
        },
        dataType: 'json',
        success: function(response) {
            if (response && response.success) {
                $('#ajaxResult').html(`
                    <p style='color: green;'>✅ AJAX يعمل بشكل صحيح!</p>
                    <p>عدد البلدان: ${response.countries.length}</p>
                    <p>إجمالي البلدان: ${response.total}</p>
                `);
            } else {
                $('#ajaxResult').html(`
                    <p style='color: red;'>❌ AJAX فشل: ${response.message || 'خطأ غير معروف'}</p>
                `);
            }
        },
        error: function(xhr, status, error) {
            $('#ajaxResult').html(`
                <p style='color: red;'>❌ خطأ في AJAX:</p>
                <p>Status: ${status}</p>
                <p>Error: ${error}</p>
                <p>Response: ${xhr.responseText}</p>
            `);
        }
    });
}
</script>

</body>
</html>
