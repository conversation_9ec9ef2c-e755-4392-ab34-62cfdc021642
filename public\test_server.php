<?php

/**
 * Server Test Page
 * Elite Transfer System - Test server configuration and file access
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار الخادم - Elite Transfer System</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); min-height: 100vh; padding: 20px; }";
echo ".test-card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 20px; }";
echo ".test-item { padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid; }";
echo ".success { border-left-color: #28a745; background: #d4edda; color: #155724; }";
echo ".error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }";
echo ".warning { border-left-color: #ffc107; background: #fff3cd; color: #856404; }";
echo ".info { border-left-color: #17a2b8; background: #d1ecf1; color: #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 class='text-white text-center mb-4'>🧪 اختبار الخادم والملفات</h1>";

// Test 1: PHP Information
echo "<div class='test-card'>";
echo "<h3>1. معلومات PHP</h3>";
echo "<div class='test-item success'>✅ PHP Version: " . PHP_VERSION . "</div>";
echo "<div class='test-item success'>✅ Server: " . $_SERVER['SERVER_SOFTWARE'] . "</div>";
echo "<div class='test-item success'>✅ Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</div>";
echo "<div class='test-item success'>✅ Script Name: " . $_SERVER['SCRIPT_NAME'] . "</div>";
echo "<div class='test-item success'>✅ Current Directory: " . __DIR__ . "</div>";
echo "</div>";

// Test 2: File System
echo "<div class='test-card'>";
echo "<h3>2. نظام الملفات</h3>";

$testFiles = [
    'index.php' => 'الصفحة الرئيسية',
    'dashboard.php' => 'لوحة التحكم',
    'track-transfer.php' => 'تتبع التحويل (الأصلي)',
    'track_transfer_fixed.php' => 'تتبع التحويل (المُصلح)',
    'transfers_safe.php' => 'إدارة التحويلات الآمنة',
    'complete_fix.php' => 'الإصلاح الشامل',
    'includes/database_manager.php' => 'مدير قاعدة البيانات',
    'includes/session_helper.php' => 'مساعد الجلسات'
];

foreach ($testFiles as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        echo "<div class='test-item success'>✅ $description ($file) - " . number_format($size) . " bytes</div>";
    } else {
        echo "<div class='test-item error'>❌ $description ($file) - غير موجود</div>";
    }
}

echo "</div>";

// Test 3: Database Connection
echo "<div class='test-card'>";
echo "<h3>3. اتصال قاعدة البيانات</h3>";

try {
    require_once __DIR__ . '/includes/database_manager.php';
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    echo "<div class='test-item success'>✅ اتصال قاعدة البيانات نجح</div>";
    
    $connectionInfo = $dbManager->getConnectionInfo();
    echo "<div class='test-item info'>ℹ️ نوع قاعدة البيانات: " . $connectionInfo['type'] . "</div>";
    echo "<div class='test-item info'>ℹ️ قاعدة البيانات: " . $connectionInfo['database'] . "</div>";
    echo "<div class='test-item info'>ℹ️ الخادم: " . $connectionInfo['host'] . "</div>";
    
    // Test transfers table
    try {
        $count = $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn();
        echo "<div class='test-item success'>✅ جدول transfers: $count تحويل</div>";
    } catch (Exception $e) {
        echo "<div class='test-item error'>❌ جدول transfers: " . $e->getMessage() . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ فشل اتصال قاعدة البيانات: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 4: URL Testing
echo "<div class='test-card'>";
echo "<h3>4. اختبار الروابط</h3>";

$baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']);

$testUrls = [
    '' => 'الصفحة الرئيسية',
    '/dashboard.php' => 'لوحة التحكم',
    '/track-transfer.php' => 'تتبع التحويل (الأصلي)',
    '/track_transfer_fixed.php' => 'تتبع التحويل (المُصلح)',
    '/transfers_safe.php' => 'إدارة التحويلات الآمنة',
    '/complete_fix.php' => 'الإصلاح الشامل',
    '/debug_ajax.php' => 'تشخيص AJAX'
];

foreach ($testUrls as $path => $description) {
    $url = $baseUrl . $path;
    echo "<div class='test-item info'>";
    echo "<strong>$description:</strong> ";
    echo "<a href='$url' target='_blank' class='btn btn-sm btn-outline-primary'>$url</a>";
    echo "</div>";
}

echo "</div>";

// Test 5: Sample Transfer Data
echo "<div class='test-card'>";
echo "<h3>5. بيانات التحويلات التجريبية</h3>";

try {
    $stmt = $db->prepare("SELECT transfer_code, pickup_code, sender_name, recipient_name, amount, status FROM transfers ORDER BY created_at DESC LIMIT 5");
    $stmt->execute();
    $transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($transfers) > 0) {
        echo "<div class='test-item success'>✅ تم العثور على " . count($transfers) . " تحويل</div>";
        
        echo "<div class='table-responsive mt-3'>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>رمز التحويل</th><th>رمز الاستلام</th><th>المرسل</th><th>المستلم</th><th>المبلغ</th><th>الحالة</th><th>اختبار</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($transfers as $transfer) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($transfer['transfer_code']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['pickup_code']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['sender_name']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['recipient_name']) . "</td>";
            echo "<td>$" . number_format($transfer['amount'], 2) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['status']) . "</td>";
            echo "<td>";
            echo "<a href='track_transfer_fixed.php?code=" . urlencode($transfer['transfer_code']) . "' class='btn btn-sm btn-success me-1' target='_blank'>تتبع</a>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<div class='test-item warning'>⚠️ لا توجد تحويلات - <a href='complete_fix.php' class='btn btn-sm btn-warning'>إضافة بيانات تجريبية</a></div>";
    }
    
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ خطأ في جلب التحويلات: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 6: Quick Actions
echo "<div class='test-card'>";
echo "<h3>6. إجراءات سريعة</h3>";

echo "<div class='d-grid gap-2 d-md-flex'>";
echo "<a href='complete_fix.php' class='btn btn-primary'>الإصلاح الشامل</a>";
echo "<a href='transfers_safe.php' class='btn btn-success'>الصفحة الآمنة</a>";
echo "<a href='track_transfer_fixed.php' class='btn btn-info'>تتبع التحويل المُصلح</a>";
echo "<a href='debug_ajax.php' class='btn btn-warning'>تشخيص AJAX</a>";
echo "<a href='dashboard.php' class='btn btn-secondary'>لوحة التحكم</a>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

?>
