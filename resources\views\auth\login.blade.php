@extends('layouts.app')

@section('title', 'Login - Elite Financial Transfer System')

@section('content')
<div class="container">
    <div class="row justify-content-center align-items-center min-vh-100">
        <div class="col-md-6 col-lg-5">
            <div class="card">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-bank2 text-primary" style="font-size: 3rem;"></i>
                        <h2 class="mt-3 mb-1">{{ __('Welcome Back') }}</h2>
                        <p class="text-muted">{{ __('Sign in to your Elite Transfer account') }}</p>
                    </div>

                    <!-- Login Form -->
                    <form id="loginForm" method="POST" action="{{ route('login') }}">
                        @csrf
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="bi bi-envelope me-1"></i>
                                {{ __('Email Address') }}
                            </label>
                            <input type="email" 
                                   class="form-control @error('email') is-invalid @enderror" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email') }}" 
                                   required 
                                   autofocus
                                   placeholder="{{ __('Enter your email address') }}">
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="bi bi-lock me-1"></i>
                                {{ __('Password') }}
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       id="password" 
                                       name="password" 
                                       required
                                       placeholder="{{ __('Enter your password') }}">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                {{ __('Remember me') }}
                            </label>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary" data-original-text="{{ __('Sign In') }}">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                {{ __('Sign In') }}
                            </button>
                        </div>

                        <div class="text-center">
                            <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">
                                {{ __('Forgot your password?') }}
                            </a>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-0">{{ __("Don't have an account?") }}</p>
                        <a href="{{ route('register') }}" class="btn btn-outline-primary mt-2">
                            <i class="bi bi-person-plus me-2"></i>
                            {{ __('Create Account') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- OTP Verification Modal -->
<div class="modal fade" id="otpModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-shield-check me-2"></i>
                    {{ __('Two-Factor Authentication') }}
                </h5>
            </div>
            <div class="modal-body">
                <p class="text-muted mb-4">{{ __('Please enter the 6-digit code sent to your registered phone number.') }}</p>
                
                <form id="otpForm">
                    @csrf
                    <input type="hidden" id="otpUserId" name="user_id">
                    
                    <div class="mb-3">
                        <label for="otp" class="form-label">{{ __('Verification Code') }}</label>
                        <input type="text" 
                               class="form-control text-center" 
                               id="otp" 
                               name="otp" 
                               maxlength="6" 
                               pattern="[0-9]{6}"
                               placeholder="000000"
                               required
                               style="font-size: 1.5rem; letter-spacing: 0.5rem;">
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" data-original-text="{{ __('Verify') }}">
                            {{ __('Verify') }}
                        </button>
                    </div>
                </form>
                
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-link" id="resendOtp">
                        {{ __('Resend Code') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Forgot Password Modal -->
<div class="modal fade" id="forgotPasswordModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-key me-2"></i>
                    {{ __('Reset Password') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted mb-4">{{ __('Enter your email address and we will send you a link to reset your password.') }}</p>
                
                <form id="forgotPasswordForm">
                    @csrf
                    <div class="mb-3">
                        <label for="resetEmail" class="form-label">{{ __('Email Address') }}</label>
                        <input type="email" class="form-control" id="resetEmail" name="email" required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" data-original-text="{{ __('Send Reset Link') }}">
                            {{ __('Send Reset Link') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordInput = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.className = 'bi bi-eye-slash';
    } else {
        passwordInput.type = 'password';
        icon.className = 'bi bi-eye';
    }
});

// Login form submission
document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    
    setLoading(submitBtn, true);
    
    axios.post(this.action, formData)
        .then(response => {
            if (response.data.success) {
                if (response.data.requires_otp) {
                    // Show OTP modal
                    document.getElementById('otpUserId').value = response.data.user_id;
                    const otpModal = new bootstrap.Modal(document.getElementById('otpModal'));
                    otpModal.show();
                    showNotification(response.data.message, 'success');
                } else {
                    // Redirect to dashboard
                    showNotification(response.data.message, 'success');
                    setTimeout(() => {
                        window.location.href = response.data.redirect;
                    }, 1000);
                }
            } else {
                showNotification(response.data.message, 'error');
            }
        })
        .catch(error => {
            if (error.response?.status === 422) {
                // Validation errors
                const errors = error.response.data.errors;
                Object.keys(errors).forEach(field => {
                    const input = document.querySelector(`[name="${field}"]`);
                    if (input) {
                        input.classList.add('is-invalid');
                        const feedback = input.parentNode.querySelector('.invalid-feedback') || 
                                       document.createElement('div');
                        feedback.className = 'invalid-feedback';
                        feedback.textContent = errors[field][0];
                        if (!input.parentNode.querySelector('.invalid-feedback')) {
                            input.parentNode.appendChild(feedback);
                        }
                    }
                });
            } else {
                const message = error.response?.data?.message || 'Login failed. Please try again.';
                showNotification(message, 'error');
            }
        })
        .finally(() => {
            setLoading(submitBtn, false);
        });
});

// OTP form submission
document.getElementById('otpForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    
    setLoading(submitBtn, true);
    
    axios.post('{{ route("verify.otp") }}', formData)
        .then(response => {
            if (response.data.success) {
                showNotification(response.data.message, 'success');
                setTimeout(() => {
                    window.location.href = response.data.redirect;
                }, 1000);
            } else {
                showNotification(response.data.message, 'error');
            }
        })
        .catch(error => {
            const message = error.response?.data?.message || 'OTP verification failed.';
            showNotification(message, 'error');
        })
        .finally(() => {
            setLoading(submitBtn, false);
        });
});

// OTP input formatting
document.getElementById('otp').addEventListener('input', function(e) {
    this.value = this.value.replace(/\D/g, '');
});

// Auto-submit OTP when 6 digits entered
document.getElementById('otp').addEventListener('input', function(e) {
    if (this.value.length === 6) {
        document.getElementById('otpForm').dispatchEvent(new Event('submit'));
    }
});

// Resend OTP
document.getElementById('resendOtp').addEventListener('click', function() {
    const userId = document.getElementById('otpUserId').value;
    
    axios.post('/auth/resend-otp', { user_id: userId })
        .then(response => {
            showNotification('OTP code resent successfully', 'success');
        })
        .catch(error => {
            showNotification('Failed to resend OTP', 'error');
        });
});

// Forgot password form
document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    
    setLoading(submitBtn, true);
    
    axios.post('/auth/forgot-password', formData)
        .then(response => {
            showNotification('Password reset link sent to your email', 'success');
            bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal')).hide();
        })
        .catch(error => {
            const message = error.response?.data?.message || 'Failed to send reset link';
            showNotification(message, 'error');
        })
        .finally(() => {
            setLoading(submitBtn, false);
        });
});

// Clear validation errors on input
document.querySelectorAll('input').forEach(input => {
    input.addEventListener('input', function() {
        this.classList.remove('is-invalid');
        const feedback = this.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    });
});
</script>
@endpush
