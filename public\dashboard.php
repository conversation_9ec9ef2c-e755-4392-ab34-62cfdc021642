<?php

/**
 * Elite Transfer System - Enhanced Dashboard v2.0
 * High-performance admin dashboard with real-time data
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $db = DatabaseManager::getInstance();
        
        switch ($_POST['action']) {
            case 'get_stats':
                $stats = $db->getStatistics();
                echo json_encode(['success' => true, 'stats' => $stats], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'get_recent_transfers':
                $transfers = $db->getTransfers([], 10, 0);
                echo json_encode(['success' => true, 'transfers' => $transfers], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'update_transfer_status':
                $transferId = intval($_POST['transfer_id']);
                $newStatus = $_POST['status'];
                
                $updated = $db->update('transfers', 
                    ['status' => $newStatus, 'updated_at' => date('Y-m-d H:i:s')], 
                    'id = :id', 
                    ['id' => $transferId]
                );
                
                if ($updated) {
                    echo json_encode(['success' => true, 'message' => 'تم تحديث حالة التحويل بنجاح'], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في تحديث حالة التحويل'], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            case 'get_system_info':
                $info = [
                    'database' => $db->getConnectionInfo(),
                    'session' => getSessionInfo(),
                    'system' => [
                        'name' => SYSTEM_NAME,
                        'version' => SYSTEM_VERSION,
                        'php_version' => PHP_VERSION,
                        'memory_usage' => memory_get_usage(true),
                        'peak_memory' => memory_get_peak_usage(true)
                    ]
                ];
                echo json_encode(['success' => true, 'info' => $info], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح'], JSON_UNESCAPED_UNICODE);
        }
        
    } catch (Exception $e) {
        logMessage('ERROR', 'Dashboard AJAX error', ['error' => $e->getMessage()]);
        echo json_encode(['success' => false, 'message' => 'خطأ في الخادم: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
    
    exit;
}

// Get initial data
try {
    $db = DatabaseManager::getInstance();
    $stats = $db->getStatistics();
    $recentTransfers = $db->getTransfers([], 5, 0);
} catch (Exception $e) {
    logMessage('ERROR', 'Failed to load dashboard data', ['error' => $e->getMessage()]);
    $stats = [];
    $recentTransfers = [];
}

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';
$userRole = $userData['role'] ?? 'admin';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: var(--light-color);
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar.collapsed {
            width: 80px;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-item {
            display: block;
            padding: 15px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
        }
        
        .menu-item:hover,
        .menu-item.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .menu-item i {
            width: 20px;
            margin-left: 10px;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .main-content.expanded {
            margin-right: 80px;
        }
        
        .top-bar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-color);
        }
        
        .stat-card.success::before { background: var(--success-color); }
        .stat-card.warning::before { background: var(--warning-color); }
        .stat-card.danger::before { background: var(--danger-color); }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        
        .stat-card.success .stat-icon { color: var(--success-color); }
        .stat-card.warning .stat-icon { color: var(--warning-color); }
        .stat-card.danger .stat-icon { color: var(--danger-color); }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6b7280;
            font-weight: 500;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
            font-weight: 600;
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--dark-color);
            background: #f8fafc;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-processing { background: #dbeafe; color: #1e40af; }
        .status-completed { background: #d1fae5; color: #065f46; }
        .status-cancelled { background: #fee2e2; color: #991b1b; }
        .status-failed { background: #fee2e2; color: #991b1b; }
        
        .btn {
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .quick-action {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            text-decoration: none;
            color: var(--dark-color);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .quick-action:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            color: var(--primary-color);
        }
        
        .quick-action i {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .real-time-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: var(--success-color);
            border-radius: 50%;
            margin-left: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .performance-info {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4 class="mb-0">
                <i class="bi bi-speedometer2 me-2"></i>
                <span class="sidebar-text">لوحة التحكم</span>
            </h4>
            <small class="opacity-75">مرحباً <?= htmlspecialchars($userName) ?></small>
        </div>
        
        <div class="sidebar-menu">
            <a href="#" class="menu-item active" data-section="dashboard">
                <i class="bi bi-house"></i>
                <span class="sidebar-text">الرئيسية</span>
            </a>
            <a href="transfers_safe.php" class="menu-item">
                <i class="bi bi-arrow-left-right"></i>
                <span class="sidebar-text">التحويلات</span>
            </a>
            <a href="users_management.php" class="menu-item">
                <i class="bi bi-people"></i>
                <span class="sidebar-text">المستخدمين</span>
            </a>
            <a href="reports_management.php" class="menu-item">
                <i class="bi bi-graph-up"></i>
                <span class="sidebar-text">التقارير</span>
            </a>
            <a href="settings_management.php" class="menu-item">
                <i class="bi bi-gear"></i>
                <span class="sidebar-text">الإعدادات</span>
            </a>
            <hr class="my-3" style="border-color: rgba(255,255,255,0.1);">
            <a href="create_transfer_fixed.php" class="menu-item">
                <i class="bi bi-plus-circle"></i>
                <span class="sidebar-text">تحويل جديد</span>
            </a>
            <a href="track_transfer_fixed.php" class="menu-item">
                <i class="bi bi-search"></i>
                <span class="sidebar-text">تتبع التحويل</span>
            </a>
            <a href="system_upgrade.php" class="menu-item">
                <i class="bi bi-arrow-up-circle"></i>
                <span class="sidebar-text">تحديث النظام</span>
            </a>
            <hr class="my-3" style="border-color: rgba(255,255,255,0.1);">
            <a href="logout.php" class="menu-item" style="color: #fca5a5;">
                <i class="bi bi-box-arrow-right"></i>
                <span class="sidebar-text">تسجيل الخروج</span>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Bar -->
        <div class="top-bar">
            <div>
                <button class="btn btn-outline-primary" id="sidebarToggle">
                    <i class="bi bi-list"></i>
                </button>
                <span class="ms-3 fw-bold">لوحة التحكم المتقدمة</span>
                <span class="real-time-indicator"></span>
            </div>
            <div>
                <span class="badge bg-success me-2">v<?= SYSTEM_VERSION ?></span>
                <span class="text-muted"><?= date('Y-m-d H:i') ?></span>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboardSection">
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="create_transfer_fixed.php" class="quick-action">
                    <i class="bi bi-plus-circle"></i>
                    <div>تحويل جديد</div>
                </a>
                <a href="track_transfer_fixed.php" class="quick-action">
                    <i class="bi bi-search"></i>
                    <div>تتبع التحويل</div>
                </a>
                <a href="transfers_safe.php" class="quick-action">
                    <i class="bi bi-shield-check"></i>
                    <div>إدارة آمنة</div>
                </a>
                <a href="#" class="quick-action" onclick="refreshData()">
                    <i class="bi bi-arrow-clockwise"></i>
                    <div>تحديث البيانات</div>
                </a>
            </div>

            <!-- Statistics -->
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card">
                    <i class="bi bi-arrow-left-right stat-icon"></i>
                    <div class="stat-number" id="totalTransfers"><?= number_format($stats['total_transfers'] ?? 0) ?></div>
                    <div class="stat-label">إجمالي التحويلات</div>
                </div>
                
                <div class="stat-card success">
                    <i class="bi bi-check-circle stat-icon"></i>
                    <div class="stat-number" id="completedTransfers"><?= number_format($stats['completed_transfers'] ?? 0) ?></div>
                    <div class="stat-label">تحويلات مكتملة</div>
                </div>
                
                <div class="stat-card warning">
                    <i class="bi bi-clock stat-icon"></i>
                    <div class="stat-number" id="pendingTransfers"><?= number_format($stats['pending_transfers'] ?? 0) ?></div>
                    <div class="stat-label">في الانتظار</div>
                </div>
                
                <div class="stat-card">
                    <i class="bi bi-currency-dollar stat-icon"></i>
                    <div class="stat-number" id="totalAmount"><?= formatCurrency($stats['total_amount'] ?? 0) ?></div>
                    <div class="stat-label">إجمالي المبلغ</div>
                </div>
                
                <div class="stat-card success">
                    <i class="bi bi-calendar-day stat-icon"></i>
                    <div class="stat-number" id="todayTransfers"><?= number_format($stats['today_transfers'] ?? 0) ?></div>
                    <div class="stat-label">تحويلات اليوم</div>
                </div>
                
                <div class="stat-card">
                    <i class="bi bi-cash-stack stat-icon"></i>
                    <div class="stat-number" id="todayAmount"><?= formatCurrency($stats['today_amount'] ?? 0) ?></div>
                    <div class="stat-label">مبلغ اليوم</div>
                </div>
            </div>

            <!-- Recent Transfers -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        أحدث التحويلات
                        <span class="real-time-indicator"></span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رمز التحويل</th>
                                    <th>المرسل</th>
                                    <th>المستلم</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="recentTransfersTable">
                                <?php foreach ($recentTransfers as $transfer): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($transfer['transfer_code']) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= htmlspecialchars($transfer['pickup_code']) ?></small>
                                    </td>
                                    <td><?= htmlspecialchars($transfer['sender_name']) ?></td>
                                    <td><?= htmlspecialchars($transfer['recipient_name']) ?></td>
                                    <td><?= formatCurrency($transfer['amount']) ?></td>
                                    <td>
                                        <span class="status-badge status-<?= $transfer['status'] ?>">
                                            <?= getStatusLabel($transfer['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= formatDate($transfer['created_at'], 'Y-m-d H:i') ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="updateTransferStatus(<?= $transfer['id'] ?>, '<?= $transfer['status'] ?>')">
                                            <i class="bi bi-arrow-repeat"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Performance Info -->
            <div class="performance-info">
                <div class="row">
                    <div class="col-md-3">
                        <i class="bi bi-database me-1"></i>
                        <strong>قاعدة البيانات:</strong> MySQL محسنة
                    </div>
                    <div class="col-md-3">
                        <i class="bi bi-lightning me-1"></i>
                        <strong>الأداء:</strong> <span id="performanceStatus">ممتاز</span>
                    </div>
                    <div class="col-md-3">
                        <i class="bi bi-shield-check me-1"></i>
                        <strong>الأمان:</strong> مفعل
                    </div>
                    <div class="col-md-3">
                        <i class="bi bi-clock me-1"></i>
                        <strong>آخر تحديث:</strong> <span id="lastUpdate"><?= date('H:i:s') ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        let refreshInterval;
        
        $(document).ready(function() {
            console.log('🚀 Enhanced Dashboard v2.0 Loaded');
            
            // Sidebar toggle
            $('#sidebarToggle').click(function() {
                $('#sidebar').toggleClass('collapsed');
                $('#mainContent').toggleClass('expanded');
            });
            
            // Menu navigation
            $('.menu-item').click(function(e) {
                if ($(this).attr('href') === '#') {
                    e.preventDefault();
                    $('.menu-item').removeClass('active');
                    $(this).addClass('active');
                }
            });
            
            // Start auto-refresh
            startAutoRefresh();
            
            // Performance monitoring
            monitorPerformance();
        });
        
        function startAutoRefresh() {
            refreshInterval = setInterval(function() {
                refreshData();
            }, 30000); // Refresh every 30 seconds
        }
        
        function refreshData() {
            console.log('🔄 Refreshing dashboard data...');
            
            // Update stats
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_stats' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        updateStats(response.stats);
                    }
                }
            });
            
            // Update recent transfers
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_recent_transfers' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        updateRecentTransfers(response.transfers);
                    }
                }
            });
            
            // Update last update time
            $('#lastUpdate').text(new Date().toLocaleTimeString('ar-SA'));
        }
        
        function updateStats(stats) {
            $('#totalTransfers').text(new Intl.NumberFormat('ar-SA').format(stats.total_transfers || 0));
            $('#completedTransfers').text(new Intl.NumberFormat('ar-SA').format(stats.completed_transfers || 0));
            $('#pendingTransfers').text(new Intl.NumberFormat('ar-SA').format(stats.pending_transfers || 0));
            $('#totalAmount').text('USD ' + new Intl.NumberFormat('ar-SA', {minimumFractionDigits: 2}).format(stats.total_amount || 0));
            $('#todayTransfers').text(new Intl.NumberFormat('ar-SA').format(stats.today_transfers || 0));
            $('#todayAmount').text('USD ' + new Intl.NumberFormat('ar-SA', {minimumFractionDigits: 2}).format(stats.today_amount || 0));
        }
        
        function updateRecentTransfers(transfers) {
            const tbody = $('#recentTransfersTable');
            tbody.empty();
            
            transfers.forEach(transfer => {
                const statusLabels = {
                    'pending': 'في الانتظار',
                    'processing': 'قيد المعالجة',
                    'completed': 'مكتمل',
                    'cancelled': 'ملغي',
                    'failed': 'فاشل'
                };
                
                const row = `
                    <tr>
                        <td>
                            <strong>${transfer.transfer_code}</strong>
                            <br>
                            <small class="text-muted">${transfer.pickup_code}</small>
                        </td>
                        <td>${transfer.sender_name}</td>
                        <td>${transfer.recipient_name}</td>
                        <td>USD ${new Intl.NumberFormat('ar-SA', {minimumFractionDigits: 2}).format(transfer.amount)}</td>
                        <td>
                            <span class="status-badge status-${transfer.status}">
                                ${statusLabels[transfer.status] || transfer.status}
                            </span>
                        </td>
                        <td>${new Date(transfer.created_at).toLocaleString('ar-SA')}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="updateTransferStatus(${transfer.id}, '${transfer.status}')">
                                <i class="bi bi-arrow-repeat"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
        
        function updateTransferStatus(transferId, currentStatus) {
            const statuses = ['pending', 'processing', 'completed', 'cancelled', 'failed'];
            const statusLabels = {
                'pending': 'في الانتظار',
                'processing': 'قيد المعالجة',
                'completed': 'مكتمل',
                'cancelled': 'ملغي',
                'failed': 'فاشل'
            };
            
            let options = '';
            statuses.forEach(status => {
                const selected = status === currentStatus ? 'selected' : '';
                options += `<option value="${status}" ${selected}>${statusLabels[status]}</option>`;
            });
            
            const newStatus = prompt(`تحديث حالة التحويل:\n\nاختر الحالة الجديدة:\n${Object.values(statusLabels).join('\n')}`);
            
            if (newStatus && statuses.includes(newStatus)) {
                $.ajax({
                    url: '',
                    method: 'POST',
                    data: { 
                        action: 'update_transfer_status',
                        transfer_id: transferId,
                        status: newStatus
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.success) {
                            showAlert('تم تحديث حالة التحويل بنجاح', 'success');
                            refreshData();
                        } else {
                            showAlert(response.message || 'فشل في تحديث الحالة', 'danger');
                        }
                    },
                    error: function() {
                        showAlert('خطأ في الاتصال', 'danger');
                    }
                });
            }
        }
        
        function monitorPerformance() {
            const startTime = performance.now();
            
            window.addEventListener('load', function() {
                const loadTime = performance.now() - startTime;
                
                if (loadTime < 1000) {
                    $('#performanceStatus').text('ممتاز').css('color', '#10b981');
                } else if (loadTime < 2000) {
                    $('#performanceStatus').text('جيد').css('color', '#f59e0b');
                } else {
                    $('#performanceStatus').text('بطيء').css('color', '#ef4444');
                }
                
                console.log(`⚡ Dashboard loaded in ${loadTime.toFixed(2)}ms`);
            });
        }
        
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);
            
            setTimeout(function() {
                $('.alert').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
