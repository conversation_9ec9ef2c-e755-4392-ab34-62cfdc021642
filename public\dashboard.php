<?php

/**
 * Professional Dashboard
 * Elite Transfer System - Ultra Professional Admin Dashboard
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $db = DatabaseManager::getInstance();
        
        switch ($_POST['action']) {
            case 'get_dashboard_data':
                $stats = $db->getStatistics();
                $recentTransfers = $db->getTransfers([], 10, 0);
                $recentUsers = $db->getUsers([], 5, 0);
                
                // Get pending transfers count
                $pendingCount = $db->selectOne("
                    SELECT COUNT(*) as count 
                    FROM transfers 
                    WHERE status = 'pending' AND (deleted_at IS NULL OR deleted_at = '')
                ")['count'];
                
                // Get today's revenue
                $todayRevenue = $db->selectOne("
                    SELECT COALESCE(SUM(fee), 0) as revenue 
                    FROM transfers 
                    WHERE DATE(created_at) = CURDATE() 
                    AND status = 'completed' 
                    AND (deleted_at IS NULL OR deleted_at = '')
                ")['revenue'];
                
                echo json_encode([
                    'success' => true,
                    'stats' => $stats,
                    'recent_transfers' => $recentTransfers,
                    'recent_users' => $recentUsers,
                    'pending_count' => intval($pendingCount),
                    'today_revenue' => floatval($todayRevenue)
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'approve_transfer':
                $transferId = intval($_POST['transfer_id']);
                
                $updated = $db->update('transfers', 
                    [
                        'status' => 'processing',
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 
                    'id = :id', 
                    ['id' => $transferId]
                );
                
                if ($updated) {
                    logMessage('INFO', 'Transfer approved', [
                        'transfer_id' => $transferId,
                        'user_id' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true, 
                        'message' => 'تم الموافقة على التحويل بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode([
                        'success' => false, 
                        'message' => 'فشل في الموافقة على التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            case 'reject_transfer':
                $transferId = intval($_POST['transfer_id']);
                $reason = $_POST['reason'] ?? 'لم يتم تحديد السبب';
                
                $updated = $db->update('transfers', 
                    [
                        'status' => 'cancelled',
                        'notes' => $reason,
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 
                    'id = :id', 
                    ['id' => $transferId]
                );
                
                if ($updated) {
                    logMessage('INFO', 'Transfer rejected', [
                        'transfer_id' => $transferId,
                        'reason' => $reason,
                        'user_id' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true, 
                        'message' => 'تم رفض التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode([
                        'success' => false, 
                        'message' => 'فشل في رفض التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            case 'complete_transfer':
                $transferId = intval($_POST['transfer_id']);
                
                $updated = $db->update('transfers', 
                    [
                        'status' => 'completed',
                        'completed_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 
                    'id = :id', 
                    ['id' => $transferId]
                );
                
                if ($updated) {
                    logMessage('INFO', 'Transfer completed', [
                        'transfer_id' => $transferId,
                        'user_id' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true, 
                        'message' => 'تم إكمال التحويل بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode([
                        'success' => false, 
                        'message' => 'فشل في إكمال التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            case 'delete_transfer':
                $transferId = intval($_POST['transfer_id']);
                
                $updated = $db->update('transfers', 
                    ['deleted_at' => date('Y-m-d H:i:s')], 
                    'id = :id', 
                    ['id' => $transferId]
                );
                
                if ($updated) {
                    logMessage('INFO', 'Transfer deleted', [
                        'transfer_id' => $transferId,
                        'user_id' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true, 
                        'message' => 'تم حذف التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode([
                        'success' => false, 
                        'message' => 'فشل في حذف التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            case 'get_system_status':
                $systemStatus = [
                    'database' => $db->getConnectionInfo(),
                    'session' => getSessionInfo(),
                    'server' => [
                        'php_version' => PHP_VERSION,
                        'memory_usage' => memory_get_usage(true),
                        'peak_memory' => memory_get_peak_usage(true),
                        'uptime' => $_SERVER['REQUEST_TIME'] - $_SERVER['REQUEST_TIME_FLOAT']
                    ]
                ];
                
                echo json_encode([
                    'success' => true,
                    'status' => $systemStatus
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode([
                    'success' => false, 
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
        
    } catch (Exception $e) {
        logMessage('ERROR', 'Dashboard AJAX error', ['error' => $e->getMessage()]);
        echo json_encode([
            'success' => false, 
            'message' => 'خطأ في الخادم: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    
    exit;
}

// Get initial data
try {
    $db = DatabaseManager::getInstance();
    $stats = $db->getStatistics();
    $recentTransfers = $db->getTransfers([], 5, 0);
} catch (Exception $e) {
    logMessage('ERROR', 'Failed to load dashboard data', ['error' => $e->getMessage()]);
    $stats = [];
    $recentTransfers = [];
}

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';
$userRole = $userData['role'] ?? 'admin';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الاحترافية - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --sidebar-width: 280px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-left: 1px solid var(--glass-border);
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }
        
        .sidebar.collapsed {
            width: 80px;
        }
        
        .sidebar-header {
            padding: 25px 20px;
            border-bottom: 1px solid var(--glass-border);
            text-align: center;
        }
        
        .sidebar-logo {
            width: 60px;
            height: 60px;
            background: var(--success-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.8rem;
            color: white;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
        }
        
        .sidebar-title {
            color: white;
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-section {
            margin-bottom: 30px;
        }
        
        .menu-section-title {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 20px 10px;
            margin-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: var(--success-gradient);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }
        
        .menu-item:hover,
        .menu-item.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .menu-item:hover::before,
        .menu-item.active::before {
            transform: scaleY(1);
        }
        
        .menu-item i {
            width: 24px;
            font-size: 1.2rem;
            margin-left: 15px;
        }
        
        .menu-text {
            font-weight: 500;
            transition: opacity 0.3s ease;
        }
        
        .sidebar.collapsed .menu-text,
        .sidebar.collapsed .sidebar-title,
        .sidebar.collapsed .sidebar-subtitle,
        .sidebar.collapsed .menu-section-title {
            opacity: 0;
        }
        
        /* Main Content */
        .main-content {
            margin-right: var(--sidebar-width);
            min-height: 100vh;
            transition: all 0.3s ease;
        }
        
        .main-content.expanded {
            margin-right: 80px;
        }
        
        /* Top Bar */
        .top-bar {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .top-bar-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 10px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .sidebar-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .page-title {
            color: white;
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
        }
        
        .top-bar-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .notification-btn {
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .notification-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
        }
        
        .user-avatar {
            width: 45px;
            height: 45px;
            background: var(--success-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 700;
        }
        
        .user-details h6 {
            margin: 0;
            font-weight: 600;
        }
        
        .user-details small {
            opacity: 0.8;
        }
        
        /* Dashboard Content */
        .dashboard-content {
            padding: 30px;
        }
        
        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 30px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--success-gradient);
        }
        
        .stat-card.warning::before {
            background: var(--warning-gradient);
        }
        
        .stat-card.danger::before {
            background: var(--danger-gradient);
        }
        
        .stat-card.info::before {
            background: var(--primary-gradient);
        }
        
        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            background: var(--success-gradient);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
        }
        
        .stat-card.warning .stat-icon {
            background: var(--warning-gradient);
        }
        
        .stat-card.danger .stat-icon {
            background: var(--danger-gradient);
        }
        
        .stat-card.info .stat-icon {
            background: var(--primary-gradient);
        }
        
        .stat-trend {
            background: rgba(72, 187, 120, 0.2);
            color: #48bb78;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .stat-trend.down {
            background: rgba(245, 101, 101, 0.2);
            color: #f56565;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            font-weight: 500;
        }
        
        /* Cards */
        .dashboard-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            overflow: hidden;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid var(--glass-border);
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            color: white;
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card-actions {
            display: flex;
            gap: 10px;
        }
        
        .card-body {
            padding: 30px;
        }
        
        /* Buttons */
        .btn {
            border-radius: 12px;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: var(--primary-gradient);
        }
        
        .btn-success {
            background: var(--success-gradient);
        }
        
        .btn-warning {
            background: var(--warning-gradient);
            color: #8b4513;
        }
        
        .btn-danger {
            background: var(--danger-gradient);
            color: #8b0000;
        }
        
        .btn-sm {
            padding: 8px 16px;
            font-size: 0.9rem;
        }
        
        /* Tables */
        .table-responsive {
            border-radius: 15px;
            overflow: hidden;
        }
        
        .table {
            margin: 0;
            color: white;
        }
        
        .table th {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            font-weight: 700;
            color: white;
            padding: 20px 15px;
        }
        
        .table td {
            border: none;
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .table tbody tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        /* Status Badges */
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-pending {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }
        
        .status-processing {
            background: rgba(13, 110, 253, 0.2);
            color: #0d6efd;
            border: 1px solid rgba(13, 110, 253, 0.3);
        }
        
        .status-completed {
            background: rgba(25, 135, 84, 0.2);
            color: #198754;
            border: 1px solid rgba(25, 135, 84, 0.3);
        }
        
        .status-cancelled {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }
        
        .status-failed {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }
        
        /* Real-time indicator */
        .real-time-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            margin-left: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        /* Loading */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .top-bar {
                padding: 15px 20px;
            }
            
            .dashboard-content {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="bi bi-speedometer2"></i>
            </div>
            <h4 class="sidebar-title"><?= SYSTEM_NAME ?></h4>
            <p class="sidebar-subtitle">لوحة التحكم الاحترافية</p>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-section">
                <div class="menu-section-title">الرئيسية</div>
                <a href="#" class="menu-item active" data-section="dashboard">
                    <i class="bi bi-house"></i>
                    <span class="menu-text">لوحة التحكم</span>
                </a>
                <a href="#" class="menu-item" data-section="analytics">
                    <i class="bi bi-graph-up"></i>
                    <span class="menu-text">التحليلات</span>
                </a>
            </div>
            
            <div class="menu-section">
                <div class="menu-section-title">العمليات</div>
                <a href="transfers_safe.php" class="menu-item">
                    <i class="bi bi-arrow-left-right"></i>
                    <span class="menu-text">إدارة التحويلات</span>
                </a>
                <a href="users_management.php" class="menu-item">
                    <i class="bi bi-people"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
                <a href="reports_management.php" class="menu-item">
                    <i class="bi bi-file-earmark-text"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </div>
            
            <div class="menu-section">
                <div class="menu-section-title">الخدمات</div>
                <a href="create_transfer_fixed.php" class="menu-item">
                    <i class="bi bi-plus-circle"></i>
                    <span class="menu-text">تحويل جديد</span>
                </a>
                <a href="track_transfer_fixed.php" class="menu-item">
                    <i class="bi bi-search"></i>
                    <span class="menu-text">تتبع التحويل</span>
                </a>
            </div>
            
            <div class="menu-section">
                <div class="menu-section-title">النظام</div>
                <a href="settings_management.php" class="menu-item">
                    <i class="bi bi-gear"></i>
                    <span class="menu-text">الإعدادات</span>
                </a>
                <a href="system_upgrade.php" class="menu-item">
                    <i class="bi bi-arrow-up-circle"></i>
                    <span class="menu-text">تحديث النظام</span>
                </a>
                <a href="logout.php" class="menu-item" style="color: #ff6b6b;">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="top-bar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="bi bi-list"></i>
                </button>
                <h1 class="page-title">لوحة التحكم الاحترافية</h1>
                <span class="real-time-indicator"></span>
            </div>
            
            <div class="top-bar-right">
                <button class="notification-btn" id="notificationBtn">
                    <i class="bi bi-bell"></i>
                    <span class="notification-badge" id="notificationBadge">3</span>
                </button>
                
                <button class="notification-btn" onclick="refreshDashboard()">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
                
                <div class="user-info">
                    <div class="user-avatar">
                        <?= strtoupper(substr($userName, 0, 2)) ?>
                    </div>
                    <div class="user-details">
                        <h6><?= htmlspecialchars($userName) ?></h6>
                        <small><?= htmlspecialchars($userRole) ?></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Stats Grid -->
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card animate__animated animate__fadeInUp">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="bi bi-arrow-left-right"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="bi bi-arrow-up"></i> +12%
                        </div>
                    </div>
                    <div class="stat-number" id="totalTransfers"><?= number_format($stats['total_transfers'] ?? 0) ?></div>
                    <div class="stat-label">إجمالي التحويلات</div>
                </div>
                
                <div class="stat-card warning animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="bi bi-clock"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="bi bi-arrow-up"></i> +5%
                        </div>
                    </div>
                    <div class="stat-number" id="pendingTransfers"><?= number_format($stats['pending_transfers'] ?? 0) ?></div>
                    <div class="stat-label">في الانتظار</div>
                </div>
                
                <div class="stat-card info animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="bi bi-arrow-up"></i> +18%
                        </div>
                    </div>
                    <div class="stat-number" id="completedTransfers"><?= number_format($stats['completed_transfers'] ?? 0) ?></div>
                    <div class="stat-label">مكتملة</div>
                </div>
                
                <div class="stat-card danger animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="bi bi-arrow-up"></i> +25%
                        </div>
                    </div>
                    <div class="stat-number" id="todayRevenue">$<?= number_format($stats['today_amount'] ?? 0, 2) ?></div>
                    <div class="stat-label">إيرادات اليوم</div>
                </div>
            </div>

            <!-- Recent Transfers -->
            <div class="dashboard-card animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-clock-history"></i>
                        أحدث التحويلات
                        <span class="real-time-indicator"></span>
                    </h3>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary" onclick="refreshTransfers()">
                            <i class="bi bi-arrow-clockwise"></i>
                            تحديث
                        </button>
                        <button class="btn btn-sm btn-success" onclick="exportTransfers()">
                            <i class="bi bi-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover" id="transfersTable">
                            <thead>
                                <tr>
                                    <th>رمز التحويل</th>
                                    <th>المرسل</th>
                                    <th>المستلم</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="transfersTableBody">
                                <?php foreach ($recentTransfers as $transfer): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($transfer['transfer_code']) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= htmlspecialchars($transfer['pickup_code']) ?></small>
                                    </td>
                                    <td><?= htmlspecialchars($transfer['sender_name']) ?></td>
                                    <td><?= htmlspecialchars($transfer['recipient_name']) ?></td>
                                    <td>$<?= number_format($transfer['amount'], 2) ?></td>
                                    <td>
                                        <span class="status-badge status-<?= $transfer['status'] ?>">
                                            <?= getStatusLabel($transfer['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= formatDate($transfer['created_at'], 'Y-m-d H:i') ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <?php if ($transfer['status'] === 'pending'): ?>
                                                <button class="btn btn-success" onclick="approveTransfer(<?= $transfer['id'] ?>)" title="موافقة">
                                                    <i class="bi bi-check"></i>
                                                </button>
                                                <button class="btn btn-warning" onclick="rejectTransfer(<?= $transfer['id'] ?>)" title="رفض">
                                                    <i class="bi bi-x"></i>
                                                </button>
                                            <?php elseif ($transfer['status'] === 'processing'): ?>
                                                <button class="btn btn-primary" onclick="completeTransfer(<?= $transfer['id'] ?>)" title="إكمال">
                                                    <i class="bi bi-check-circle"></i>
                                                </button>
                                            <?php endif; ?>
                                            <button class="btn btn-info" onclick="printTransfer(<?= $transfer['id'] ?>)" title="طباعة">
                                                <i class="bi bi-printer"></i>
                                            </button>
                                            <button class="btn btn-outline-primary" onclick="editTransfer(<?= $transfer['id'] ?>)" title="تعديل">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-danger" onclick="deleteTransfer(<?= $transfer['id'] ?>)" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        let refreshInterval;
        
        $(document).ready(function() {
            console.log('🚀 Professional Dashboard Loaded - Elite Transfer System v<?= SYSTEM_VERSION ?>');
            
            // Initialize DataTable
            $('#transfersTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                pageLength: 10,
                responsive: true,
                order: [[5, 'desc']]
            });
            
            // Sidebar toggle
            $('#sidebarToggle').click(function() {
                $('#sidebar').toggleClass('collapsed');
                $('#mainContent').toggleClass('expanded');
            });
            
            // Start auto-refresh
            startAutoRefresh();
            
            // Load initial data
            refreshDashboard();
        });
        
        function startAutoRefresh() {
            refreshInterval = setInterval(function() {
                refreshDashboard();
            }, 30000); // Refresh every 30 seconds
        }
        
        function refreshDashboard() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_dashboard_data' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        updateStats(response.stats);
                        updateTransfersTable(response.recent_transfers);
                        updateNotificationBadge(response.pending_count);
                    }
                },
                error: function() {
                    showNotification('خطأ في تحديث البيانات', 'error');
                }
            });
        }
        
        function updateStats(stats) {
            $('#totalTransfers').text(new Intl.NumberFormat('ar-SA').format(stats.total_transfers || 0));
            $('#pendingTransfers').text(new Intl.NumberFormat('ar-SA').format(stats.pending_transfers || 0));
            $('#completedTransfers').text(new Intl.NumberFormat('ar-SA').format(stats.completed_transfers || 0));
            $('#todayRevenue').text('$' + new Intl.NumberFormat('ar-SA', {minimumFractionDigits: 2}).format(stats.today_amount || 0));
        }
        
        function updateTransfersTable(transfers) {
            const table = $('#transfersTable').DataTable();
            table.clear();
            
            transfers.forEach(transfer => {
                const statusLabels = {
                    'pending': 'في الانتظار',
                    'processing': 'قيد المعالجة',
                    'completed': 'مكتمل',
                    'cancelled': 'ملغي',
                    'failed': 'فاشل'
                };
                
                let actions = '<div class="btn-group btn-group-sm">';
                
                if (transfer.status === 'pending') {
                    actions += `<button class="btn btn-success" onclick="approveTransfer(${transfer.id})" title="موافقة"><i class="bi bi-check"></i></button>`;
                    actions += `<button class="btn btn-warning" onclick="rejectTransfer(${transfer.id})" title="رفض"><i class="bi bi-x"></i></button>`;
                } else if (transfer.status === 'processing') {
                    actions += `<button class="btn btn-primary" onclick="completeTransfer(${transfer.id})" title="إكمال"><i class="bi bi-check-circle"></i></button>`;
                }
                
                actions += `<button class="btn btn-info" onclick="printTransfer(${transfer.id})" title="طباعة"><i class="bi bi-printer"></i></button>`;
                actions += `<button class="btn btn-outline-primary" onclick="editTransfer(${transfer.id})" title="تعديل"><i class="bi bi-pencil"></i></button>`;
                actions += `<button class="btn btn-danger" onclick="deleteTransfer(${transfer.id})" title="حذف"><i class="bi bi-trash"></i></button>`;
                actions += '</div>';
                
                table.row.add([
                    `<strong>${transfer.transfer_code}</strong><br><small class="text-muted">${transfer.pickup_code}</small>`,
                    transfer.sender_name,
                    transfer.recipient_name,
                    '$' + new Intl.NumberFormat('ar-SA', {minimumFractionDigits: 2}).format(transfer.amount),
                    `<span class="status-badge status-${transfer.status}">${statusLabels[transfer.status] || transfer.status}</span>`,
                    new Date(transfer.created_at).toLocaleString('ar-SA'),
                    actions
                ]);
            });
            
            table.draw();
        }
        
        function updateNotificationBadge(count) {
            const badge = $('#notificationBadge');
            if (count > 0) {
                badge.text(count).show();
            } else {
                badge.hide();
            }
        }
        
        // Transfer Actions
        function approveTransfer(transferId) {
            Swal.fire({
                title: 'تأكيد الموافقة',
                text: 'هل أنت متأكد من الموافقة على هذا التحويل؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، موافق',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#28a745'
            }).then((result) => {
                if (result.isConfirmed) {
                    performTransferAction('approve_transfer', transferId);
                }
            });
        }
        
        function rejectTransfer(transferId) {
            Swal.fire({
                title: 'رفض التحويل',
                input: 'textarea',
                inputLabel: 'سبب الرفض',
                inputPlaceholder: 'اكتب سبب رفض التحويل...',
                showCancelButton: true,
                confirmButtonText: 'رفض',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545',
                inputValidator: (value) => {
                    if (!value) {
                        return 'يرجى كتابة سبب الرفض';
                    }
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    performTransferAction('reject_transfer', transferId, result.value);
                }
            });
        }
        
        function completeTransfer(transferId) {
            Swal.fire({
                title: 'إكمال التحويل',
                text: 'هل أنت متأكد من إكمال هذا التحويل؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، إكمال',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#007bff'
            }).then((result) => {
                if (result.isConfirmed) {
                    performTransferAction('complete_transfer', transferId);
                }
            });
        }
        
        function deleteTransfer(transferId) {
            Swal.fire({
                title: 'حذف التحويل',
                text: 'هل أنت متأكد من حذف هذا التحويل؟ لا يمكن التراجع عن هذا الإجراء.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545'
            }).then((result) => {
                if (result.isConfirmed) {
                    performTransferAction('delete_transfer', transferId);
                }
            });
        }
        
        function performTransferAction(action, transferId, reason = '') {
            const data = {
                action: action,
                transfer_id: transferId
            };
            
            if (reason) {
                data.reason = reason;
            }
            
            $.ajax({
                url: '',
                method: 'POST',
                data: data,
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        Swal.fire({
                            title: 'نجح!',
                            text: response.message,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                        refreshDashboard();
                    } else {
                        Swal.fire({
                            title: 'خطأ!',
                            text: response.message || 'فشل في تنفيذ العملية',
                            icon: 'error'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'خطأ!',
                        text: 'خطأ في الاتصال بالخادم',
                        icon: 'error'
                    });
                }
            });
        }
        
        function printTransfer(transferId) {
            window.open(`print_transfer.php?id=${transferId}`, '_blank');
        }
        
        function editTransfer(transferId) {
            window.location.href = `edit_transfer.php?id=${transferId}`;
        }
        
        function exportTransfers() {
            window.location.href = 'export_transfers.php';
        }
        
        function refreshTransfers() {
            refreshDashboard();
            showNotification('تم تحديث البيانات', 'success');
        }
        
        function showNotification(message, type) {
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
            
            Toast.fire({
                icon: type,
                title: message
            });
        }
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
