<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Branch extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'country_id',
        'city',
        'address',
        'phone',
        'email',
        'manager_id',
        'is_active',
        'operating_hours',
        'supported_currencies',
        'daily_limit',
        'monthly_limit',
        'commission_rate',
        'latitude',
        'longitude',
        'timezone',
        'services_offered',
        'contact_person',
        'license_number',
        'established_date',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'operating_hours' => 'array',
        'supported_currencies' => 'array',
        'daily_limit' => 'decimal:2',
        'monthly_limit' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'services_offered' => 'array',
        'established_date' => 'date',
    ];

    /**
     * Get the country this branch belongs to
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the branch manager
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * Get all users in this branch
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get all agents in this branch
     */
    public function agents(): HasMany
    {
        return $this->hasMany(User::class)->where('role', User::ROLE_AGENT);
    }

    /**
     * Get all transfers from this branch
     */
    public function transfers(): HasMany
    {
        return $this->hasMany(Transfer::class);
    }

    /**
     * Check if branch is currently open
     */
    public function isOpen(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now($this->timezone);
        $dayOfWeek = strtolower($now->format('l'));
        
        if (!isset($this->operating_hours[$dayOfWeek])) {
            return false;
        }

        $hours = $this->operating_hours[$dayOfWeek];
        if ($hours['closed'] ?? false) {
            return false;
        }

        $currentTime = $now->format('H:i');
        return $currentTime >= $hours['open'] && $currentTime <= $hours['close'];
    }

    /**
     * Get branch statistics
     */
    public function getStatistics(): array
    {
        $today = now()->startOfDay();
        $thisMonth = now()->startOfMonth();

        return [
            'total_agents' => $this->agents()->count(),
            'active_agents' => $this->agents()->where('is_active', true)->count(),
            'today_transfers' => $this->transfers()->whereDate('created_at', $today)->count(),
            'today_amount' => $this->transfers()->whereDate('created_at', $today)->sum('amount'),
            'month_transfers' => $this->transfers()->whereDate('created_at', '>=', $thisMonth)->count(),
            'month_amount' => $this->transfers()->whereDate('created_at', '>=', $thisMonth)->sum('amount'),
            'pending_transfers' => $this->transfers()->where('status', Transfer::STATUS_PENDING)->count(),
        ];
    }

    /**
     * Check if currency is supported
     */
    public function supportsCurrency(string $currencyCode): bool
    {
        return in_array($currencyCode, $this->supported_currencies ?? []);
    }

    /**
     * Get distance from coordinates
     */
    public function getDistanceFrom(float $latitude, float $longitude): float
    {
        if (!$this->latitude || !$this->longitude) {
            return 0;
        }

        $earthRadius = 6371; // km

        $latDelta = deg2rad($latitude - $this->latitude);
        $lonDelta = deg2rad($longitude - $this->longitude);

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos(deg2rad($this->latitude)) * cos(deg2rad($latitude)) *
             sin($lonDelta / 2) * sin($lonDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }
}
