<?php

// Elite Transfer System - Enhanced Compliance Management
// Complete compliance and regulatory management

// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

// Load database manager
require_once __DIR__ . '/includes/database_manager.php';

try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'get_kyc_pending':
            $page = intval($_POST['page'] ?? 1);
            $limit = intval($_POST['limit'] ?? 10);
            $offset = ($page - 1) * $limit;
            
            $countQuery = "SELECT COUNT(*) FROM users WHERE kyc_status = 'pending' AND deleted_at IS NULL";
            $total = $db->query($countQuery)->fetchColumn();
            
            $query = "
                SELECT u.id, u.name, u.email, u.phone, u.created_at, u.kyc_status,
                       c.name as country_name
                FROM users u
                LEFT JOIN countries c ON u.country_id = c.id
                WHERE u.kyc_status = 'pending' AND u.deleted_at IS NULL
                ORDER BY u.created_at DESC
                LIMIT $limit OFFSET $offset
            ";
            
            $users = $db->query($query)->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'users' => $users,
                'total' => $total,
                'page' => $page,
                'pages' => ceil($total / $limit)
            ]);
            exit;
            
        case 'update_kyc_status':
            $user_id = intval($_POST['user_id']);
            $status = $_POST['status'] ?? '';
            $notes = $_POST['notes'] ?? '';
            
            $stmt = $db->prepare("
                UPDATE users 
                SET kyc_status = ?, kyc_verified_at = ?, updated_at = NOW()
                WHERE id = ?
            ");
            
            $verified_at = ($status === 'approved') ? date('Y-m-d H:i:s') : null;
            $result = $stmt->execute([$status, $verified_at, $user_id]);
            
            if ($result) {
                // Log the action
                try {
                    $stmt = $db->prepare("
                        INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, created_at)
                        VALUES (?, 'kyc_status_update', 'users', ?, ?, NOW())
                    ");
                    $stmt->execute([
                        $_SESSION['user_id'],
                        $user_id,
                        json_encode(['status' => $status, 'notes' => $notes])
                    ]);
                } catch (Exception $e) {
                    // Continue if audit log fails
                }
                
                echo json_encode(['success' => true, 'message' => 'تم تحديث حالة التحقق بنجاح']);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في تحديث حالة التحقق']);
            }
            exit;
            
        case 'get_suspicious_transactions':
            $date_from = $_POST['date_from'] ?? date('Y-m-01');
            $date_to = $_POST['date_to'] ?? date('Y-m-d');
            
            $query = "
                SELECT t.*, u.name as user_name, u.email,
                       sc.name as sender_country, rc.name as recipient_country
                FROM transfers t
                LEFT JOIN users u ON t.user_id = u.id
                LEFT JOIN countries sc ON t.sender_country_id = sc.id
                LEFT JOIN countries rc ON t.recipient_country_id = rc.id
                WHERE DATE(t.created_at) BETWEEN ? AND ?
                  AND (t.amount > 10000 OR t.status = 'failed')
                  AND t.deleted_at IS NULL
                ORDER BY t.created_at DESC
                LIMIT 50
            ";
            
            $stmt = $db->prepare($query);
            $stmt->execute([$date_from, $date_to]);
            $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'transactions' => $transactions]);
            exit;
            
        case 'get_compliance_report':
            $date_from = $_POST['date_from'] ?? date('Y-m-01');
            $date_to = $_POST['date_to'] ?? date('Y-m-d');
            
            $report = [
                'total_transactions' => $db->prepare("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN ? AND ? AND deleted_at IS NULL")->execute([$date_from, $date_to]) ? $db->fetchColumn() : 0,
                'high_value_transactions' => $db->prepare("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN ? AND ? AND amount > 10000 AND deleted_at IS NULL")->execute([$date_from, $date_to]) ? $db->fetchColumn() : 0,
                'failed_transactions' => $db->prepare("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN ? AND ? AND status = 'failed' AND deleted_at IS NULL")->execute([$date_from, $date_to]) ? $db->fetchColumn() : 0,
                'pending_kyc' => $db->query("SELECT COUNT(*) FROM users WHERE kyc_status = 'pending' AND deleted_at IS NULL")->fetchColumn(),
                'approved_kyc' => $db->query("SELECT COUNT(*) FROM users WHERE kyc_status = 'approved' AND deleted_at IS NULL")->fetchColumn(),
                'rejected_kyc' => $db->query("SELECT COUNT(*) FROM users WHERE kyc_status = 'rejected' AND deleted_at IS NULL")->fetchColumn()
            ];
            
            echo json_encode(['success' => true, 'report' => $report]);
            exit;
    }
}

// Get compliance statistics
$compliance_stats = [
    'pending_kyc' => $db->query("SELECT COUNT(*) FROM users WHERE kyc_status = 'pending' AND deleted_at IS NULL")->fetchColumn(),
    'high_value_today' => $db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = CURDATE() AND amount > 10000 AND deleted_at IS NULL")->fetchColumn(),
    'failed_today' => $db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'failed' AND deleted_at IS NULL")->fetchColumn(),
    'total_alerts' => $db->query("SELECT COUNT(*) FROM audit_logs WHERE action LIKE '%alert%' AND DATE(created_at) = CURDATE()")->fetchColumn()
];

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الامتثال التنظيمي - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
        }
        
        .stats-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stats-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .compliance-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .compliance-card-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .compliance-card-body {
            padding: 20px;
        }
        
        .kyc-status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .kyc-pending { background: #fff3cd; color: #664d03; }
        .kyc-approved { background: #d1e7dd; color: #0f5132; }
        .kyc-rejected { background: #f8d7da; color: #842029; }
        
        .alert-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .alert-high { border-left-color: #dc3545; background: #f8d7da; }
        .alert-medium { border-left-color: #ffc107; background: #fff3cd; }
        .alert-low { border-left-color: #17a2b8; background: #d1ecf1; }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .btn-action {
            margin: 2px;
            padding: 5px 10px;
            font-size: 0.8em;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-1">
                        <i class="bi bi-shield-check me-2"></i>
                        الامتثال التنظيمي
                    </h1>
                    <p class="mb-0 opacity-75">إدارة شاملة للامتثال والمراقبة التنظيمية</p>
                    <small class="opacity-50">مرحباً <?= htmlspecialchars($userName) ?></small>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light me-2" onclick="refreshCompliance()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        تحديث
                    </button>
                    <button class="btn btn-warning me-2" onclick="generateComplianceReport()">
                        <i class="bi bi-file-earmark-text me-1"></i>
                        تقرير الامتثال
                    </button>
                    <button class="btn btn-danger" onclick="exportAuditLog()">
                        <i class="bi bi-download me-1"></i>
                        تصدير سجل المراجعة
                    </button>
                </div>
            </div>

            <!-- Compliance Statistics -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($compliance_stats['pending_kyc']) ?></div>
                        <div class="stats-label">في انتظار التحقق</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($compliance_stats['high_value_today']) ?></div>
                        <div class="stats-label">تحويلات عالية القيمة اليوم</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($compliance_stats['failed_today']) ?></div>
                        <div class="stats-label">تحويلات فاشلة اليوم</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($compliance_stats['total_alerts']) ?></div>
                        <div class="stats-label">التنبيهات اليوم</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="complianceTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="kyc-tab" data-bs-toggle="tab" data-bs-target="#kyc-panel" type="button" role="tab">
                        <i class="bi bi-person-check me-2"></i>
                        التحقق من الهوية (KYC)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="aml-tab" data-bs-toggle="tab" data-bs-target="#aml-panel" type="button" role="tab">
                        <i class="bi bi-shield-exclamation me-2"></i>
                        مكافحة غسل الأموال (AML)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports-panel" type="button" role="tab">
                        <i class="bi bi-graph-up me-2"></i>
                        التقارير التنظيمية
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="alerts-tab" data-bs-toggle="tab" data-bs-target="#alerts-panel" type="button" role="tab">
                        <i class="bi bi-bell me-2"></i>
                        التنبيهات والمراقبة
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="complianceTabContent">
                <!-- KYC Panel -->
                <div class="tab-pane fade show active" id="kyc-panel" role="tabpanel">
                    <div class="compliance-card">
                        <div class="compliance-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-person-check me-2"></i>
                                طلبات التحقق من الهوية المعلقة
                            </h5>
                        </div>
                        <div class="compliance-card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>المستخدم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الهاتف</th>
                                            <th>الدولة</th>
                                            <th>تاريخ التسجيل</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="kycTableBody">
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="spinner-border text-primary" role="status"></div>
                                                <p class="mt-2">جاري تحميل طلبات التحقق...</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div id="kycTableInfo" class="text-muted"></div>
                                <nav>
                                    <ul class="pagination mb-0" id="kycPagination"></ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AML Panel -->
                <div class="tab-pane fade" id="aml-panel" role="tabpanel">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="filter-section">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label class="form-label">من تاريخ:</label>
                                        <input type="date" class="form-control" id="amlDateFrom" value="<?= date('Y-m-01') ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">إلى تاريخ:</label>
                                        <input type="date" class="form-control" id="amlDateTo" value="<?= date('Y-m-d') ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-primary w-100" onclick="loadSuspiciousTransactions()">
                                            <i class="bi bi-search me-1"></i>
                                            البحث عن المعاملات المشبوهة
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-warning w-100" onclick="exportSuspiciousTransactions()">
                                            <i class="bi bi-download me-1"></i>
                                            تصدير النتائج
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="compliance-card">
                        <div class="compliance-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                المعاملات المشبوهة
                            </h5>
                        </div>
                        <div class="compliance-card-body">
                            <div id="suspiciousTransactionsContent">
                                <div class="text-center py-4">
                                    <i class="bi bi-search display-4 text-muted"></i>
                                    <p class="text-muted mt-2">استخدم الفلاتر أعلاه للبحث عن المعاملات المشبوهة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Panel -->
                <div class="tab-pane fade" id="reports-panel" role="tabpanel">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="compliance-card">
                                <div class="compliance-card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-file-earmark-bar-graph me-2"></i>
                                        تقرير الامتثال الشهري
                                    </h5>
                                </div>
                                <div class="compliance-card-body" id="complianceReportContent">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <p class="mt-2">جاري تحميل التقرير...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="compliance-card">
                                <div class="compliance-card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-pie-chart me-2"></i>
                                        إحصائيات التحقق من الهوية
                                    </h5>
                                </div>
                                <div class="compliance-card-body">
                                    <canvas id="kycStatsChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alerts Panel -->
                <div class="tab-pane fade" id="alerts-panel" role="tabpanel">
                    <div class="compliance-card">
                        <div class="compliance-card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-bell me-2"></i>
                                التنبيهات النشطة
                            </h5>
                        </div>
                        <div class="compliance-card-body">
                            <div id="alertsContent">
                                <div class="alert-item alert-high">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>تنبيه عالي الأولوية</strong>
                                            <p class="mb-0">تم اكتشاف معاملة عالية القيمة تتطلب مراجعة فورية</p>
                                        </div>
                                        <span class="badge bg-danger">عالي</span>
                                    </div>
                                </div>

                                <div class="alert-item alert-medium">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>تنبيه متوسط الأولوية</strong>
                                            <p class="mb-0">عدة محاولات دخول فاشلة من نفس عنوان IP</p>
                                        </div>
                                        <span class="badge bg-warning">متوسط</span>
                                    </div>
                                </div>

                                <div class="alert-item alert-low">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>تنبيه منخفض الأولوية</strong>
                                            <p class="mb-0">تحديث في إعدادات النظام</p>
                                        </div>
                                        <span class="badge bg-info">منخفض</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KYC Review Modal -->
    <div class="modal fade" id="kycReviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-check me-2"></i>
                        مراجعة طلب التحقق من الهوية
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="kycReviewForm">
                        <input type="hidden" id="kycUserId" name="user_id">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>اسم المستخدم:</strong>
                                <p id="kycUserName" class="text-muted"></p>
                            </div>
                            <div class="col-md-6">
                                <strong>البريد الإلكتروني:</strong>
                                <p id="kycUserEmail" class="text-muted"></p>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>رقم الهاتف:</strong>
                                <p id="kycUserPhone" class="text-muted"></p>
                            </div>
                            <div class="col-md-6">
                                <strong>الدولة:</strong>
                                <p id="kycUserCountry" class="text-muted"></p>
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            <select class="form-select" id="kycStatus" name="status" required>
                                <option value="approved">موافق عليه</option>
                                <option value="rejected">مرفوض</option>
                                <option value="pending">في الانتظار</option>
                            </select>
                            <label for="kycStatus">قرار المراجعة</label>
                        </div>

                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="kycNotes" name="notes" style="height: 100px"></textarea>
                            <label for="kycNotes">ملاحظات المراجعة</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="submitKycReview()">
                        <i class="bi bi-check-lg me-1"></i>
                        حفظ القرار
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        let currentKycPage = 1;
        let kycChart;

        $(document).ready(function() {
            loadKycPending();
            loadComplianceReport();
            initKycChart();
        });

        function loadKycPending() {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_kyc_pending',
                    page: currentKycPage,
                    limit: 10
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        displayKycUsers(response.users);
                        updateKycPagination(response.page, response.pages, response.total);
                    }
                },
                error: function() {
                    $('#kycTableBody').html(`
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="bi bi-exclamation-triangle text-danger"></i>
                                <p class="text-danger mt-2">خطأ في تحميل البيانات</p>
                            </td>
                        </tr>
                    `);
                }
            });
        }

        function displayKycUsers(users) {
            const tbody = $('#kycTableBody');
            tbody.empty();

            if (users.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <i class="bi bi-check-circle text-success display-4"></i>
                            <p class="text-muted mt-2">لا توجد طلبات تحقق معلقة</p>
                        </td>
                    </tr>
                `);
                return;
            }

            users.forEach(user => {
                const row = `
                    <tr>
                        <td>
                            <div>
                                <strong>${user.name}</strong>
                                <br>
                                <small class="text-muted">ID: ${user.id}</small>
                            </div>
                        </td>
                        <td>${user.email}</td>
                        <td>${user.phone || '-'}</td>
                        <td>${user.country_name || '-'}</td>
                        <td>${formatDate(user.created_at)}</td>
                        <td>
                            <span class="kyc-status-badge kyc-${user.kyc_status}">
                                ${getKycStatusText(user.kyc_status)}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-primary btn-action" onclick="reviewKyc(${user.id}, '${user.name}', '${user.email}', '${user.phone || ''}', '${user.country_name || ''}')" title="مراجعة">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-success btn-action" onclick="quickApprove(${user.id})" title="موافقة سريعة">
                                <i class="bi bi-check"></i>
                            </button>
                            <button class="btn btn-sm btn-danger btn-action" onclick="quickReject(${user.id})" title="رفض سريع">
                                <i class="bi bi-x"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        function updateKycPagination(page, pages, total) {
            const pagination = $('#kycPagination');
            pagination.empty();

            $('#kycTableInfo').text(`عرض ${total} طلب تحقق`);

            if (pages <= 1) return;

            // Previous button
            if (page > 1) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changeKycPage(${page - 1})">السابق</a>
                    </li>
                `);
            }

            // Page numbers
            for (let i = Math.max(1, page - 2); i <= Math.min(pages, page + 2); i++) {
                pagination.append(`
                    <li class="page-item ${i === page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changeKycPage(${i})">${i}</a>
                    </li>
                `);
            }

            // Next button
            if (page < pages) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changeKycPage(${page + 1})">التالي</a>
                    </li>
                `);
            }
        }

        function changeKycPage(page) {
            currentKycPage = page;
            loadKycPending();
        }

        function reviewKyc(id, name, email, phone, country) {
            $('#kycUserId').val(id);
            $('#kycUserName').text(name);
            $('#kycUserEmail').text(email);
            $('#kycUserPhone').text(phone);
            $('#kycUserCountry').text(country);
            $('#kycStatus').val('pending');
            $('#kycNotes').val('');
            $('#kycReviewModal').modal('show');
        }

        function submitKycReview() {
            const formData = new FormData($('#kycReviewForm')[0]);
            formData.append('action', 'update_kyc_status');

            $.ajax({
                url: '',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showAlert(response.message, 'success');
                        $('#kycReviewModal').modal('hide');
                        loadKycPending();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في الاتصال بالخادم', 'danger');
                }
            });
        }

        function quickApprove(userId) {
            if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
                $.ajax({
                    url: '',
                    method: 'POST',
                    data: {
                        action: 'update_kyc_status',
                        user_id: userId,
                        status: 'approved',
                        notes: 'موافقة سريعة'
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.message, 'success');
                            loadKycPending();
                        } else {
                            showAlert(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showAlert('خطأ في الاتصال بالخادم', 'danger');
                    }
                });
            }
        }

        function quickReject(userId) {
            if (confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
                $.ajax({
                    url: '',
                    method: 'POST',
                    data: {
                        action: 'update_kyc_status',
                        user_id: userId,
                        status: 'rejected',
                        notes: 'رفض سريع'
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.message, 'success');
                            loadKycPending();
                        } else {
                            showAlert(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showAlert('خطأ في الاتصال بالخادم', 'danger');
                    }
                });
            }
        }

        function loadSuspiciousTransactions() {
            const dateFrom = $('#amlDateFrom').val();
            const dateTo = $('#amlDateTo').val();

            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_suspicious_transactions',
                    date_from: dateFrom,
                    date_to: dateTo
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        displaySuspiciousTransactions(response.transactions);
                    }
                },
                error: function() {
                    $('#suspiciousTransactionsContent').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            خطأ في تحميل المعاملات المشبوهة
                        </div>
                    `);
                }
            });
        }

        function displaySuspiciousTransactions(transactions) {
            if (transactions.length === 0) {
                $('#suspiciousTransactionsContent').html(`
                    <div class="text-center py-4">
                        <i class="bi bi-check-circle text-success display-4"></i>
                        <p class="text-muted mt-2">لا توجد معاملات مشبوهة في الفترة المحددة</p>
                    </div>
                `);
                return;
            }

            let content = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رمز التحويل</th>
                                <th>المستخدم</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>السبب</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            transactions.forEach(transaction => {
                const reason = transaction.amount > 10000 ? 'مبلغ عالي' : 'معاملة فاشلة';
                const alertLevel = transaction.amount > 50000 ? 'danger' : transaction.amount > 10000 ? 'warning' : 'info';

                content += `
                    <tr>
                        <td>
                            <span class="transfer-code">${transaction.transfer_code || ''}</span>
                        </td>
                        <td>
                            <div>
                                <strong>${transaction.user_name || 'غير محدد'}</strong>
                                <br>
                                <small class="text-muted">${transaction.email || ''}</small>
                            </div>
                        </td>
                        <td>
                            <span class="text-${alertLevel} fw-bold">$${formatNumber(transaction.amount)}</span>
                        </td>
                        <td>
                            <span class="badge bg-${transaction.status === 'completed' ? 'success' : transaction.status === 'failed' ? 'danger' : 'warning'}">
                                ${getStatusText(transaction.status)}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-${alertLevel}">${reason}</span>
                        </td>
                        <td>${formatDate(transaction.created_at)}</td>
                        <td>
                            <button class="btn btn-sm btn-info btn-action" onclick="viewTransactionDetails(${transaction.id})" title="عرض التفاصيل">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-warning btn-action" onclick="flagTransaction(${transaction.id})" title="وضع علامة">
                                <i class="bi bi-flag"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            content += `
                        </tbody>
                    </table>
                </div>
            `;

            $('#suspiciousTransactionsContent').html(content);
        }

        function loadComplianceReport() {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_compliance_report',
                    date_from: new Date().toISOString().slice(0, 7) + '-01',
                    date_to: new Date().toISOString().slice(0, 10)
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        displayComplianceReport(response.report);
                        updateKycChart(response.report);
                    }
                },
                error: function() {
                    $('#complianceReportContent').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            خطأ في تحميل تقرير الامتثال
                        </div>
                    `);
                }
            });
        }

        function displayComplianceReport(report) {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="metric-item">
                            <span>إجمالي المعاملات:</span>
                            <span class="fw-bold">${formatNumber(report.total_transactions)}</span>
                        </div>
                        <div class="metric-item">
                            <span>معاملات عالية القيمة:</span>
                            <span class="fw-bold text-warning">${formatNumber(report.high_value_transactions)}</span>
                        </div>
                        <div class="metric-item">
                            <span>معاملات فاشلة:</span>
                            <span class="fw-bold text-danger">${formatNumber(report.failed_transactions)}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="metric-item">
                            <span>طلبات KYC معلقة:</span>
                            <span class="fw-bold text-warning">${formatNumber(report.pending_kyc)}</span>
                        </div>
                        <div class="metric-item">
                            <span>طلبات KYC موافق عليها:</span>
                            <span class="fw-bold text-success">${formatNumber(report.approved_kyc)}</span>
                        </div>
                        <div class="metric-item">
                            <span>طلبات KYC مرفوضة:</span>
                            <span class="fw-bold text-danger">${formatNumber(report.rejected_kyc)}</span>
                        </div>
                    </div>
                </div>
            `;
            $('#complianceReportContent').html(content);
        }

        function initKycChart() {
            const ctx = document.getElementById('kycStatsChart').getContext('2d');
            kycChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['معلق', 'موافق عليه', 'مرفوض'],
                    datasets: [{
                        data: [0, 0, 0],
                        backgroundColor: ['#ffc107', '#28a745', '#dc3545']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateKycChart(report) {
            if (kycChart) {
                kycChart.data.datasets[0].data = [
                    report.pending_kyc,
                    report.approved_kyc,
                    report.rejected_kyc
                ];
                kycChart.update();
            }
        }

        function refreshCompliance() {
            loadKycPending();
            loadComplianceReport();
            showAlert('تم تحديث بيانات الامتثال', 'success');
        }

        function generateComplianceReport() {
            const reportData = {
                title: 'تقرير الامتثال التنظيمي',
                date: new Date().toLocaleDateString('ar-SA'),
                summary: 'تقرير شامل عن حالة الامتثال في النظام'
            };

            const dataStr = JSON.stringify(reportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `compliance_report_${new Date().toISOString().slice(0, 10)}.json`;
            link.click();

            showAlert('تم إنشاء تقرير الامتثال بنجاح', 'success');
        }

        function exportAuditLog() {
            showAlert('جاري تصدير سجل المراجعة...', 'info');

            // Simulate export process
            setTimeout(() => {
                const auditData = {
                    title: 'سجل المراجعة',
                    exported_at: new Date().toISOString(),
                    note: 'هذا ملف تجريبي لسجل المراجعة'
                };

                const dataStr = JSON.stringify(auditData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `audit_log_${new Date().toISOString().slice(0, 10)}.json`;
                link.click();

                showAlert('تم تصدير سجل المراجعة بنجاح', 'success');
            }, 2000);
        }

        function exportSuspiciousTransactions() {
            showAlert('جاري تصدير المعاملات المشبوهة...', 'info');

            setTimeout(() => {
                const data = {
                    title: 'المعاملات المشبوهة',
                    period: `${$('#amlDateFrom').val()} إلى ${$('#amlDateTo').val()}`,
                    exported_at: new Date().toISOString()
                };

                const dataStr = JSON.stringify(data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `suspicious_transactions_${new Date().toISOString().slice(0, 10)}.json`;
                link.click();

                showAlert('تم تصدير المعاملات المشبوهة بنجاح', 'success');
            }, 1500);
        }

        function viewTransactionDetails(id) {
            showAlert('عرض تفاصيل المعاملة قيد التطوير', 'info');
        }

        function flagTransaction(id) {
            if (confirm('هل تريد وضع علامة على هذه المعاملة كمشبوهة؟')) {
                showAlert('تم وضع علامة على المعاملة', 'warning');
            }
        }

        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);

            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        }

        function getKycStatusText(status) {
            const statuses = {
                'pending': 'في الانتظار',
                'approved': 'موافق عليه',
                'rejected': 'مرفوض'
            };
            return statuses[status] || status;
        }

        function getStatusText(status) {
            const statuses = {
                'pending': 'في الانتظار',
                'processing': 'قيد المعالجة',
                'completed': 'مكتمل',
                'cancelled': 'ملغي',
                'failed': 'فاشل'
            };
            return statuses[status] || status;
        }

        function formatNumber(num) {
            if (num === null || num === undefined) return '0';
            return parseFloat(num).toLocaleString('en-US');
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    </script>
</body>
</html>
