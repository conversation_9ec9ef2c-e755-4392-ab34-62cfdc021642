<?php

/**
 * Users Management Page
 * Elite Transfer System - Complete user management system
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $db = DatabaseManager::getInstance();
        
        switch ($_POST['action']) {
            case 'get_users':
                $page = intval($_POST['page'] ?? 1);
                $limit = intval($_POST['limit'] ?? 10);
                $search = $_POST['search'] ?? '';
                $role = $_POST['role'] ?? '';
                
                $filters = [];
                if (!empty($search)) $filters['search'] = $search;
                if (!empty($role)) $filters['role'] = $role;
                
                $offset = ($page - 1) * $limit;
                $users = $db->getUsers($filters, $limit, $offset);
                $total = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE (deleted_at IS NULL OR deleted_at = '')")['count'];
                
                echo json_encode([
                    'success' => true,
                    'users' => $users,
                    'total' => intval($total),
                    'page' => $page,
                    'pages' => ceil($total / $limit)
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'create_user':
                $userData = [
                    'user_code' => 'USR' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT),
                    'name' => trim($_POST['name']),
                    'email' => trim($_POST['email']),
                    'phone' => trim($_POST['phone']),
                    'password_hash' => password_hash($_POST['password'], PASSWORD_DEFAULT),
                    'role' => $_POST['role'],
                    'status' => $_POST['status'] ?? 'active',
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                $userId = $db->insert('users', $userData);
                
                if ($userId) {
                    echo json_encode(['success' => true, 'message' => 'تم إنشاء المستخدم بنجاح', 'user_id' => $userId], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في إنشاء المستخدم'], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            case 'update_user':
                $userId = intval($_POST['user_id']);
                $updateData = [
                    'name' => trim($_POST['name']),
                    'email' => trim($_POST['email']),
                    'phone' => trim($_POST['phone']),
                    'role' => $_POST['role'],
                    'status' => $_POST['status'],
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                if (!empty($_POST['password'])) {
                    $updateData['password_hash'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
                }
                
                $updated = $db->update('users', $updateData, 'id = :id', ['id' => $userId]);
                
                if ($updated) {
                    echo json_encode(['success' => true, 'message' => 'تم تحديث المستخدم بنجاح'], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في تحديث المستخدم'], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            case 'delete_user':
                $userId = intval($_POST['user_id']);
                
                $updated = $db->update('users', 
                    ['deleted_at' => date('Y-m-d H:i:s')], 
                    'id = :id', 
                    ['id' => $userId]
                );
                
                if ($updated) {
                    echo json_encode(['success' => true, 'message' => 'تم حذف المستخدم بنجاح'], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في حذف المستخدم'], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح'], JSON_UNESCAPED_UNICODE);
        }
        
    } catch (Exception $e) {
        logMessage('ERROR', 'Users management error', ['error' => $e->getMessage()]);
        echo json_encode(['success' => false, 'message' => 'خطأ في الخادم: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
    
    exit;
}

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8fafc;
        }
        
        .page-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-icon {
            font-size: 2.5rem;
            color: #3b82f6;
            margin-bottom: 15px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6b7280;
            font-weight: 500;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            padding: 20px;
            font-weight: 600;
        }
        
        .table th {
            background: #f8fafc;
            border-top: none;
            font-weight: 600;
            color: #374151;
        }
        
        .btn {
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .role-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .role-admin { background: #fee2e2; color: #991b1b; }
        .role-manager { background: #fef3c7; color: #92400e; }
        .role-agent { background: #dbeafe; color: #1e40af; }
        .role-user { background: #d1fae5; color: #065f46; }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-active { background: #d1fae5; color: #065f46; }
        .status-inactive { background: #fee2e2; color: #991b1b; }
        .status-suspended { background: #fef3c7; color: #92400e; }
        
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        
        .modal-header {
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            border-radius: 15px 15px 0 0;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }
        
        .search-box {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .pagination {
            justify-content: center;
            margin-top: 20px;
        }
        
        .page-link {
            border-radius: 10px;
            margin: 0 2px;
            border: none;
            color: #3b82f6;
        }
        
        .page-link:hover {
            background: #3b82f6;
            color: white;
        }
        
        .page-item.active .page-link {
            background: #3b82f6;
            border-color: #3b82f6;
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="bi bi-people me-3"></i>
                        إدارة المستخدمين
                    </h1>
                    <p class="mb-0 opacity-75">إدارة شاملة لجميع مستخدمي النظام</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light btn-lg" onclick="showCreateModal()">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة مستخدم جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistics Cards -->
        <div class="stats-cards" id="statsCards">
            <div class="stat-card">
                <i class="bi bi-people stat-icon"></i>
                <div class="stat-number" id="totalUsers">0</div>
                <div class="stat-label">إجمالي المستخدمين</div>
            </div>
            <div class="stat-card">
                <i class="bi bi-person-check stat-icon" style="color: #10b981;"></i>
                <div class="stat-number" id="activeUsers">0</div>
                <div class="stat-label">مستخدمين نشطين</div>
            </div>
            <div class="stat-card">
                <i class="bi bi-shield-check stat-icon" style="color: #f59e0b;"></i>
                <div class="stat-number" id="adminUsers">0</div>
                <div class="stat-label">مديرين</div>
            </div>
            <div class="stat-card">
                <i class="bi bi-person-plus stat-icon" style="color: #8b5cf6;"></i>
                <div class="stat-number" id="newUsers">0</div>
                <div class="stat-label">مستخدمين جدد</div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="search-box">
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو البريد الإلكتروني">
                </div>
                <div class="col-md-3">
                    <label class="form-label">الدور</label>
                    <select class="form-select" id="roleFilter">
                        <option value="">جميع الأدوار</option>
                        <option value="admin">مدير النظام</option>
                        <option value="manager">مدير</option>
                        <option value="agent">وكيل</option>
                        <option value="user">مستخدم</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="suspended">معلق</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" onclick="loadUsers()">
                        <i class="bi bi-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="main-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-table me-2"></i>
                    قائمة المستخدمين
                    <span class="badge bg-primary ms-2" id="usersCount">0</span>
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>آخر دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <nav aria-label="صفحات المستخدمين">
            <ul class="pagination" id="pagination">
                <!-- Pagination will be generated here -->
            </ul>
        </nav>

        <!-- Back to Dashboard -->
        <div class="text-center mt-4">
            <a href="dashboard.php" class="btn btn-outline-primary">
                <i class="bi bi-arrow-right me-2"></i>
                العودة إلى لوحة التحكم
            </a>
        </div>
    </div>

    <!-- Create/Edit User Modal -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId" name="user_id">
                        
                        <div class="mb-3">
                            <label for="userName" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="userName" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="userEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="userEmail" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="userPhone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="userPhone" name="phone">
                        </div>
                        
                        <div class="mb-3">
                            <label for="userPassword" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="userPassword" name="password">
                            <div class="form-text">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label for="userRole" class="form-label">الدور</label>
                                <select class="form-select" id="userRole" name="role" required>
                                    <option value="user">مستخدم</option>
                                    <option value="agent">وكيل</option>
                                    <option value="manager">مدير</option>
                                    <option value="admin">مدير النظام</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="userStatus" class="form-label">الحالة</label>
                                <select class="form-select" id="userStatus" name="status" required>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="suspended">معلق</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        let currentPage = 1;
        let currentLimit = 10;
        
        $(document).ready(function() {
            console.log('🚀 Users Management Page Loaded');
            loadUsers();
            
            // Search on enter
            $('#searchInput').on('keypress', function(e) {
                if (e.which === 13) {
                    loadUsers();
                }
            });
        });
        
        function loadUsers(page = 1) {
            currentPage = page;
            
            const searchData = {
                action: 'get_users',
                page: page,
                limit: currentLimit,
                search: $('#searchInput').val(),
                role: $('#roleFilter').val(),
                status: $('#statusFilter').val()
            };
            
            $.ajax({
                url: '',
                method: 'POST',
                data: searchData,
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        displayUsers(response.users);
                        updatePagination(response.page, response.pages, response.total);
                        updateStats(response);
                    }
                },
                error: function() {
                    showAlert('خطأ في تحميل البيانات', 'danger');
                }
            });
        }
        
        function displayUsers(users) {
            const tbody = $('#usersTableBody');
            tbody.empty();
            
            if (users.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد مستخدمين</p>
                        </td>
                    </tr>
                `);
                return;
            }
            
            users.forEach(user => {
                const roleLabels = {
                    'admin': 'مدير النظام',
                    'manager': 'مدير',
                    'agent': 'وكيل',
                    'user': 'مستخدم'
                };
                
                const statusLabels = {
                    'active': 'نشط',
                    'inactive': 'غير نشط',
                    'suspended': 'معلق'
                };
                
                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-3">
                                    <i class="bi bi-person-circle fs-2 text-primary"></i>
                                </div>
                                <div>
                                    <strong>${user.name}</strong>
                                    <br>
                                    <small class="text-muted">${user.user_code || 'غير محدد'}</small>
                                </div>
                            </div>
                        </td>
                        <td>${user.email}</td>
                        <td>${user.phone || '-'}</td>
                        <td>
                            <span class="role-badge role-${user.role}">
                                ${roleLabels[user.role] || user.role}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-${user.status}">
                                ${statusLabels[user.status] || user.status}
                            </span>
                        </td>
                        <td>${new Date(user.created_at).toLocaleDateString('ar-SA')}</td>
                        <td>${user.last_login ? new Date(user.last_login).toLocaleDateString('ar-SA') : '-'}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteUser(${user.id})" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
        
        function updatePagination(currentPage, totalPages, totalItems) {
            $('#usersCount').text(totalItems);
            
            const pagination = $('#pagination');
            pagination.empty();
            
            if (totalPages <= 1) return;
            
            // Previous button
            if (currentPage > 1) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadUsers(${currentPage - 1})">السابق</a>
                    </li>
                `);
            }
            
            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const active = i === currentPage ? 'active' : '';
                pagination.append(`
                    <li class="page-item ${active}">
                        <a class="page-link" href="#" onclick="loadUsers(${i})">${i}</a>
                    </li>
                `);
            }
            
            // Next button
            if (currentPage < totalPages) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadUsers(${currentPage + 1})">التالي</a>
                    </li>
                `);
            }
        }
        
        function updateStats(response) {
            // This would be updated with real statistics
            $('#totalUsers').text(response.total || 0);
            $('#activeUsers').text(response.total || 0);
            $('#adminUsers').text('1');
            $('#newUsers').text('0');
        }
        
        function showCreateModal() {
            $('#modalTitle').text('إضافة مستخدم جديد');
            $('#userForm')[0].reset();
            $('#userId').val('');
            $('#userModal').modal('show');
        }
        
        function editUser(userId) {
            // This would load user data and show edit modal
            $('#modalTitle').text('تعديل المستخدم');
            $('#userId').val(userId);
            $('#userModal').modal('show');
        }
        
        function saveUser() {
            const formData = new FormData($('#userForm')[0]);
            const action = $('#userId').val() ? 'update_user' : 'create_user';
            formData.append('action', action);
            
            $.ajax({
                url: '',
                method: 'POST',
                data: Object.fromEntries(formData),
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        showAlert(response.message, 'success');
                        $('#userModal').modal('hide');
                        loadUsers(currentPage);
                    } else {
                        showAlert(response.message || 'فشل في حفظ المستخدم', 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في الاتصال', 'danger');
                }
            });
        }
        
        function deleteUser(userId) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                $.ajax({
                    url: '',
                    method: 'POST',
                    data: { action: 'delete_user', user_id: userId },
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.success) {
                            showAlert(response.message, 'success');
                            loadUsers(currentPage);
                        } else {
                            showAlert(response.message || 'فشل في حذف المستخدم', 'danger');
                        }
                    },
                    error: function() {
                        showAlert('خطأ في الاتصال', 'danger');
                    }
                });
            }
        }
        
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);
            
            setTimeout(function() {
                $('.alert').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    </script>
</body>
</html>
