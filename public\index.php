<?php

/**
 * Elite Transfer System - Main Homepage v2.0
 * Fast, Stable, and Database-Integrated
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

// Get system statistics
try {
    $db = DatabaseManager::getInstance();
    $stats = $db->getStatistics();
} catch (Exception $e) {
    logMessage('ERROR', 'Failed to load statistics', ['error' => $e->getMessage()]);
    $stats = [
        'total_transfers' => 0,
        'completed_transfers' => 0,
        'pending_transfers' => 0,
        'total_amount' => 0,
        'today_transfers' => 0,
        'today_amount' => 0
    ];
}

$userData = getUserData();
$userName = $userData['name'] ?? 'مرحباً';
$userRole = $userData['role'] ?? 'user';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= SYSTEM_NAME ?> - الصفحة الرئيسية</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #10b981;
            --secondary-color: #059669;
            --accent-color: #34d399;
            --dark-color: #064e3b;
            --light-color: #ecfdf5;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .hero-section {
            padding: 100px 0;
            color: white;
            text-align: center;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .stats-section {
            background: white;
            margin: -50px 20px 0;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, var(--light-color) 0%, #f0fdf4 100%);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            border: 2px solid var(--accent-color);
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(16, 185, 129, 0.2);
        }
        
        .stat-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: var(--secondary-color);
            font-weight: 600;
        }
        
        .features-section {
            padding: 80px 0;
            background: white;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 15px;
            padding: 40px 30px;
            text-align: center;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-color);
        }
        
        .feature-icon {
            font-size: 3.5rem;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 1rem;
        }
        
        .feature-description {
            color: #64748b;
            line-height: 1.6;
        }
        
        .cta-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: 80px 0;
            color: white;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        
        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .footer {
            background: var(--dark-color);
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        .system-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9rem;
        }
        
        .performance-badge {
            position: fixed;
            top: 20px;
            left: 20px;
            background: var(--primary-color);
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .stats-section {
                margin: -30px 10px 0;
                padding: 20px;
            }
            
            .stat-card {
                margin-bottom: 20px;
            }
            
            .performance-badge {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
                display: inline-block;
            }
        }
    </style>
</head>
<body>
    <!-- Performance Badge -->
    <div class="performance-badge">
        <i class="bi bi-lightning-fill me-1"></i>
        v<?= SYSTEM_VERSION ?> - محسن
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-bank me-2"></i>
                <?= SYSTEM_NAME ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">الميزات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="track_transfer_fixed.php">تتبع التحويل</a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">لوحة التحكم</a>
                        </li>
                        <li class="nav-item">
                            <span class="nav-link">مرحباً <?= htmlspecialchars($userName) ?></span>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">تسجيل الدخول</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <h1 class="hero-title">
                <i class="bi bi-send-check me-3"></i>
                نظام التحويلات المتطور
            </h1>
            <p class="hero-subtitle">
                أرسل واستقبل الأموال بسرعة وأمان في جميع أنحاء العالم
            </p>
            <div class="mt-4">
                <a href="create_transfer_fixed.php" class="btn btn-light btn-lg me-3">
                    <i class="bi bi-plus-circle me-2"></i>
                    إنشاء تحويل جديد
                </a>
                <a href="track_transfer_fixed.php" class="btn btn-outline-light btn-lg">
                    <i class="bi bi-search me-2"></i>
                    تتبع التحويل
                </a>
            </div>
            
            <!-- System Info -->
            <div class="system-info">
                <div class="row text-center">
                    <div class="col-md-3">
                        <i class="bi bi-database me-1"></i>
                        قاعدة بيانات: MySQL
                    </div>
                    <div class="col-md-3">
                        <i class="bi bi-shield-check me-1"></i>
                        آمن ومشفر
                    </div>
                    <div class="col-md-3">
                        <i class="bi bi-lightning me-1"></i>
                        سريع ومحسن
                    </div>
                    <div class="col-md-3">
                        <i class="bi bi-clock me-1"></i>
                        متاح 24/7
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="stat-card">
                        <i class="bi bi-arrow-left-right stat-icon"></i>
                        <div class="stat-number"><?= number_format($stats['total_transfers']) ?></div>
                        <div class="stat-label">إجمالي التحويلات</div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="stat-card">
                        <i class="bi bi-check-circle stat-icon"></i>
                        <div class="stat-number"><?= number_format($stats['completed_transfers'] ?? 0) ?></div>
                        <div class="stat-label">تحويلات مكتملة</div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="stat-card">
                        <i class="bi bi-currency-dollar stat-icon"></i>
                        <div class="stat-number"><?= formatCurrency($stats['total_amount']) ?></div>
                        <div class="stat-label">إجمالي المبلغ</div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-6 mb-4">
                    <div class="stat-card">
                        <i class="bi bi-calendar-day stat-icon"></i>
                        <div class="stat-number"><?= number_format($stats['today_transfers']) ?></div>
                        <div class="stat-label">تحويلات اليوم</div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="stat-card">
                        <i class="bi bi-cash-stack stat-icon"></i>
                        <div class="stat-number"><?= formatCurrency($stats['today_amount']) ?></div>
                        <div class="stat-label">مبلغ اليوم</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-4 fw-bold text-dark mb-3">لماذا تختار نظامنا؟</h2>
                <p class="lead text-muted">نوفر أفضل خدمات التحويل المالي مع أحدث التقنيات</p>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <i class="bi bi-lightning-charge feature-icon"></i>
                        <h3 class="feature-title">سرعة فائقة</h3>
                        <p class="feature-description">
                            تحويلات فورية خلال دقائق معدودة مع أحدث تقنيات قاعدة البيانات المحسنة
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <i class="bi bi-shield-lock feature-icon"></i>
                        <h3 class="feature-title">أمان متقدم</h3>
                        <p class="feature-description">
                            تشفير متقدم وحماية شاملة لجميع بياناتك المالية والشخصية
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <i class="bi bi-globe feature-icon"></i>
                        <h3 class="feature-title">تغطية عالمية</h3>
                        <p class="feature-description">
                            أرسل الأموال إلى أكثر من 200 دولة حول العالم بأسعار تنافسية
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <i class="bi bi-phone feature-icon"></i>
                        <h3 class="feature-title">سهولة الاستخدام</h3>
                        <p class="feature-description">
                            واجهة بسيطة ومتجاوبة تعمل على جميع الأجهزة والمتصفحات
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <i class="bi bi-headset feature-icon"></i>
                        <h3 class="feature-title">دعم متواصل</h3>
                        <p class="feature-description">
                            فريق دعم فني متخصص متاح 24/7 لمساعدتك في أي وقت
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <i class="bi bi-graph-up feature-icon"></i>
                        <h3 class="feature-title">تتبع متقدم</h3>
                        <p class="feature-description">
                            تتبع تحويلاتك في الوقت الفعلي مع إشعارات فورية لكل مرحلة
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <h2 class="display-4 fw-bold mb-3">ابدأ التحويل الآن</h2>
            <p class="lead mb-4">انضم إلى آلاف العملاء الذين يثقون بنا في تحويلاتهم المالية</p>
            <div class="mt-4">
                <a href="create_transfer_fixed.php" class="btn btn-light btn-lg me-3">
                    <i class="bi bi-rocket-takeoff me-2"></i>
                    ابدأ الآن
                </a>
                <a href="track_transfer_fixed.php" class="btn btn-outline-light btn-lg">
                    <i class="bi bi-search me-2"></i>
                    تتبع التحويل
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?= SYSTEM_NAME ?></h5>
                    <p>نظام التحويلات المالية الأكثر تطوراً وأماناً</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; 2025 <?= SYSTEM_NAME ?>. جميع الحقوق محفوظة.</p>
                    <p>تطوير: <?= SYSTEM_AUTHOR ?> | الإصدار: <?= SYSTEM_VERSION ?></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Performance monitoring
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            console.log(`🚀 Page loaded in ${loadTime.toFixed(2)}ms`);
            
            // Update performance badge
            const badge = document.querySelector('.performance-badge');
            if (badge && loadTime < 1000) {
                badge.innerHTML = '<i class="bi bi-lightning-fill me-1"></i>v<?= SYSTEM_VERSION ?> - سريع جداً';
                badge.style.background = '#10b981';
            }
        });
        
        // Auto-refresh stats every 30 seconds
        setInterval(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
