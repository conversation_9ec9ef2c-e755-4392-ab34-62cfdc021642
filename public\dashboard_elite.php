<?php

/**
 * Elite Dashboard - Premium Design
 * Elite Transfer System - Luxury Dashboard Interface
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Check authentication
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = DatabaseManager::getInstance();
$userData = getUserData();

// Ensure countries table exists
try {
    $db->query("
        CREATE TABLE IF NOT EXISTS countries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            currency VARCHAR(3) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            status ENUM('active', 'inactive') DEFAULT 'active',
            flag_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP NULL,
            INDEX idx_code (code),
            INDEX idx_status (status)
        )
    ");
} catch (Exception $e) {
    // Table might already exist
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'get_dashboard_data':
                // Get comprehensive statistics
                try {
                    $stats = $db->getStatistics();
                } catch (Exception $e) {
                    $stats = [
                        'total_transfers' => 0,
                        'completed_transfers' => 0,
                        'pending_transfers' => 0,
                        'total_amount' => 0,
                        'today_transfers' => 0,
                        'today_amount' => 0
                    ];
                }
                
                try {
                    $recentTransfers = $db->getTransfers([], 5, 0);
                } catch (Exception $e) {
                    $recentTransfers = [];
                }
                
                try {
                    $recentUsers = $db->getUsers([], 3, 0);
                } catch (Exception $e) {
                    $recentUsers = [];
                }
                
                // Get detailed counts with error handling
                try {
                    $pendingCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'pending' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                    $processingCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'processing' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                    $completedToday = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'completed' AND DATE(created_at) = CURDATE() AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                    $failedCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'failed' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                } catch (Exception $e) {
                    $pendingCount = 0;
                    $processingCount = 0;
                    $completedToday = 0;
                    $failedCount = 0;
                }
                
                // Get revenue statistics with error handling
                try {
                    $todayRevenue = $db->selectOne("SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
                    $monthRevenue = $db->selectOne("SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE()) AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
                    $yearRevenue = $db->selectOne("SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE YEAR(created_at) = YEAR(CURDATE()) AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
                } catch (Exception $e) {
                    $todayRevenue = 0;
                    $monthRevenue = 0;
                    $yearRevenue = 0;
                }
                
                // Get user statistics with error handling
                try {
                    $totalUsers = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE (deleted_at IS NULL OR deleted_at = '')")['count'];
                    $activeUsers = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1 AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                    $newUsersToday = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE() AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                } catch (Exception $e) {
                    $totalUsers = 1; // At least admin user
                    $activeUsers = 1;
                    $newUsersToday = 0;
                }
                
                // Get chart data for last 7 days
                $chartData = [];
                for ($i = 6; $i >= 0; $i--) {
                    $date = date('Y-m-d', strtotime("-$i days"));
                    try {
                        $dayData = $db->selectOne("
                            SELECT 
                                COUNT(*) as total,
                                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
                                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
                            FROM transfers 
                            WHERE DATE(created_at) = :date
                            AND (deleted_at IS NULL OR deleted_at = '')
                        ", ['date' => $date]);
                    } catch (Exception $e) {
                        $dayData = ['total' => 0, 'completed' => 0, 'pending' => 0, 'failed' => 0];
                    }
                    
                    $chartData[] = [
                        'date' => $date,
                        'total' => intval($dayData['total']),
                        'completed' => intval($dayData['completed']),
                        'pending' => intval($dayData['pending']),
                        'failed' => intval($dayData['failed'])
                    ];
                }
                
                // Get top countries
                $topCountries = [];
                try {
                    $topCountries = $db->select("
                        SELECT 
                            COALESCE(c.name, 'غير محدد') as country_name,
                            COUNT(t.id) as transfer_count,
                            COALESCE(SUM(t.total_amount), 0) as total_amount
                        FROM transfers t
                        LEFT JOIN countries c ON t.sender_country_id = c.id
                        WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                        AND (t.deleted_at IS NULL OR t.deleted_at = '')
                        GROUP BY c.id, c.name
                        ORDER BY transfer_count DESC
                        LIMIT 5
                    ");
                } catch (Exception $e) {
                    $topCountries = [];
                }
                
                echo json_encode([
                    'success' => true,
                    'stats' => $stats,
                    'recent_transfers' => $recentTransfers,
                    'recent_users' => $recentUsers,
                    'counts' => [
                        'pending' => intval($pendingCount),
                        'processing' => intval($processingCount),
                        'completed_today' => intval($completedToday),
                        'failed' => intval($failedCount),
                        'total_users' => intval($totalUsers),
                        'active_users' => intval($activeUsers),
                        'new_users_today' => intval($newUsersToday)
                    ],
                    'revenue' => [
                        'today' => floatval($todayRevenue),
                        'month' => floatval($monthRevenue),
                        'year' => floatval($yearRevenue)
                    ],
                    'chart_data' => $chartData,
                    'top_countries' => $topCountries
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الفاخرة - <?= SYSTEM_NAME ?></title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Particles.js -->
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

    <style>
        /* ===== ELITE DESIGN SYSTEM ===== */
        :root {
            /* Elite Color Palette - Luxury & Professional */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --light-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);

            /* Sophisticated Neutrals */
            --elite-dark: #1a1d29;
            --elite-darker: #151821;
            --elite-light: #f8fafc;
            --elite-gray: #64748b;
            --elite-gray-light: #94a3b8;
            --elite-gray-dark: #475569;

            /* Glass Morphism - Premium */
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-bg-strong: rgba(255, 255, 255, 0.12);
            --glass-bg-light: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.15);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            --glass-shadow-strong: 0 12px 40px 0 rgba(31, 38, 135, 0.5);

            /* Spacing & Borders - Harmonious */
            --border-radius: 24px;
            --border-radius-small: 16px;
            --border-radius-large: 32px;
            --border-radius-xl: 40px;
            --spacing-xs: 8px;
            --spacing-sm: 16px;
            --spacing-md: 24px;
            --spacing-lg: 32px;
            --spacing-xl: 48px;
            --spacing-2xl: 64px;

            /* Typography - Elegant */
            --font-primary: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-secondary: 'Inter', 'Cairo', sans-serif;
            --font-weight-light: 300;
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
            --font-weight-black: 900;

            /* Transitions - Smooth */
            --transition-fast: 0.2s ease-out;
            --transition-normal: 0.3s ease-out;
            --transition-slow: 0.5s ease-out;

            /* Z-Index Layers */
            --z-background: -1;
            --z-base: 1;
            --z-elevated: 10;
            --z-floating: 100;
            --z-modal: 1000;
            --z-tooltip: 10000;
        }

        /* ===== GLOBAL RESET & BASE ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: var(--font-primary);
            font-weight: var(--font-weight-normal);
            line-height: 1.6;
            color: white;
            background: var(--primary-gradient);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* ===== PARTICLES BACKGROUND ===== */
        #particles-js {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: var(--z-background);
            opacity: 0.6;
        }

        /* ===== MAIN CONTAINER ===== */
        .dashboard-container {
            position: relative;
            z-index: var(--z-base);
            min-height: 100vh;
            padding: var(--spacing-md);
        }

        /* ===== HEADER - LUXURY DESIGN ===== */
        .dashboard-header {
            background: var(--glass-bg-strong);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-large);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--glass-shadow);
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-gradient);
            opacity: 0.8;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }

        /* ===== LOGO - PROFESSIONAL DESIGN ===== */
        .logo-section {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: var(--success-gradient);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
            box-shadow: var(--glass-shadow);
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: logoShine 3s infinite;
        }

        @keyframes logoShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-title {
            font-size: 1.8rem;
            font-weight: var(--font-weight-black);
            background: linear-gradient(135deg, #fff 0%, #e2e8f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2px;
        }

        .logo-subtitle {
            font-size: 0.9rem;
            color: var(--elite-gray-light);
            font-weight: var(--font-weight-medium);
        }

        /* ===== USER SECTION ===== */
        .user-section {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .user-info {
            text-align: left;
            margin-left: var(--spacing-sm);
        }

        .user-name {
            font-size: 1.1rem;
            font-weight: var(--font-weight-semibold);
            color: white;
            margin-bottom: 2px;
        }

        .user-role {
            font-size: 0.85rem;
            color: var(--elite-gray-light);
            font-weight: var(--font-weight-medium);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--info-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            font-weight: var(--font-weight-bold);
            box-shadow: var(--glass-shadow);
            border: 2px solid var(--glass-border);
        }

        /* ===== STATS GRID - ELEGANT CARDS ===== */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-large);
            padding: var(--spacing-lg);
            position: relative;
            overflow: hidden;
            transition: all var(--transition-normal);
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--glass-shadow-strong);
            border-color: rgba(255, 255, 255, 0.25);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
            opacity: 0;
            transition: opacity var(--transition-normal);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card.success::before { background: var(--success-gradient); }
        .stat-card.warning::before { background: var(--warning-gradient); }
        .stat-card.danger::before { background: var(--danger-gradient); }
        .stat-card.info::before { background: var(--info-gradient); }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-md);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            background: var(--primary-gradient);
            box-shadow: var(--glass-shadow);
        }

        .stat-card.success .stat-icon { background: var(--success-gradient); }
        .stat-card.warning .stat-icon { background: var(--warning-gradient); }
        .stat-card.danger .stat-icon { background: var(--danger-gradient); }
        .stat-card.info .stat-icon { background: var(--info-gradient); }

        .stat-value {
            font-size: 2.5rem;
            font-weight: var(--font-weight-black);
            color: white;
            line-height: 1;
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: 1rem;
            color: var(--elite-gray-light);
            font-weight: var(--font-weight-medium);
            margin-bottom: var(--spacing-sm);
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.85rem;
            font-weight: var(--font-weight-medium);
        }

        .stat-change.positive {
            color: #10b981;
        }

        .stat-change.negative {
            color: #ef4444;
        }

        .stat-change.neutral {
            color: var(--elite-gray-light);
        }

        /* ===== LOADING OVERLAY ===== */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(26, 29, 41, 0.9);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: var(--z-modal);
            opacity: 1;
            visibility: visible;
            transition: all var(--transition-normal);
        }

        .loading-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid var(--glass-border);
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto var(--spacing-md);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.1rem;
            font-weight: var(--font-weight-medium);
            color: var(--elite-gray-light);
        }

        /* ===== CONTENT GRID ===== */
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto auto;
            gap: var(--spacing-lg);
            grid-template-areas:
                "chart activity"
                "actions countries";
        }

        .chart-section { grid-area: chart; }
        .activity-section { grid-area: activity; }
        .actions-section { grid-area: actions; }
        .countries-section { grid-area: countries; }

        /* ===== CHART CARD ===== */
        .chart-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-large);
            padding: var(--spacing-lg);
            box-shadow: var(--glass-shadow);
            height: 400px;
            display: flex;
            flex-direction: column;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }

        .chart-header h3 {
            font-size: 1.3rem;
            font-weight: var(--font-weight-semibold);
            color: white;
        }

        .chart-controls {
            display: flex;
            gap: var(--spacing-xs);
        }

        .chart-btn {
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid var(--glass-border);
            background: transparent;
            color: var(--elite-gray-light);
            border-radius: var(--border-radius-small);
            font-size: 0.85rem;
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .chart-btn:hover,
        .chart-btn.active {
            background: var(--primary-gradient);
            color: white;
            border-color: transparent;
        }

        .chart-container {
            flex: 1;
            position: relative;
        }

        /* ===== ACTIVITY CARD ===== */
        .activity-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-large);
            padding: var(--spacing-lg);
            box-shadow: var(--glass-shadow);
            height: 400px;
            display: flex;
            flex-direction: column;
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }

        .activity-header h3 {
            font-size: 1.3rem;
            font-weight: var(--font-weight-semibold);
            color: white;
        }

        .refresh-btn {
            width: 40px;
            height: 40px;
            border: 1px solid var(--glass-border);
            background: transparent;
            color: var(--elite-gray-light);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .refresh-btn:hover {
            background: var(--success-gradient);
            color: white;
            border-color: transparent;
            transform: rotate(180deg);
        }

        .activity-content {
            flex: 1;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-xs);
            transition: all var(--transition-fast);
        }

        .activity-item:hover {
            background: var(--glass-bg-light);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            color: white;
            background: var(--info-gradient);
        }

        .activity-details {
            flex: 1;
        }

        .activity-title {
            font-size: 0.9rem;
            font-weight: var(--font-weight-medium);
            color: white;
            margin-bottom: 2px;
        }

        .activity-time {
            font-size: 0.8rem;
            color: var(--elite-gray-light);
        }

        /* ===== ACTIONS CARD ===== */
        .actions-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-large);
            padding: var(--spacing-lg);
            box-shadow: var(--glass-shadow);
        }

        .actions-header h3 {
            font-size: 1.3rem;
            font-weight: var(--font-weight-semibold);
            color: white;
            margin-bottom: var(--spacing-md);
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-md);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            background: transparent;
            color: white;
            cursor: pointer;
            transition: all var(--transition-normal);
            text-decoration: none;
        }

        .action-btn:hover {
            transform: translateY(-4px);
            box-shadow: var(--glass-shadow);
            color: white;
        }

        .action-btn.primary:hover { background: var(--primary-gradient); border-color: transparent; }
        .action-btn.success:hover { background: var(--success-gradient); border-color: transparent; }
        .action-btn.warning:hover { background: var(--warning-gradient); border-color: transparent; }
        .action-btn.info:hover { background: var(--info-gradient); border-color: transparent; }

        .action-btn i {
            font-size: 1.5rem;
        }

        .action-btn span {
            font-size: 0.9rem;
            font-weight: var(--font-weight-medium);
        }

        /* ===== COUNTRIES CARD ===== */
        .countries-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-large);
            padding: var(--spacing-lg);
            box-shadow: var(--glass-shadow);
        }

        .countries-header h3 {
            font-size: 1.3rem;
            font-weight: var(--font-weight-semibold);
            color: white;
            margin-bottom: var(--spacing-md);
        }

        .country-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-xs);
            transition: all var(--transition-fast);
        }

        .country-item:hover {
            background: var(--glass-bg-light);
        }

        .country-name {
            font-size: 0.9rem;
            font-weight: var(--font-weight-medium);
            color: white;
        }

        .country-count {
            font-size: 0.8rem;
            color: var(--elite-gray-light);
            background: var(--glass-bg-light);
            padding: 4px 8px;
            border-radius: var(--border-radius-small);
        }

        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 1200px) {
            .content-grid {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "chart"
                    "activity"
                    "actions"
                    "countries";
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: var(--spacing-sm);
            }

            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .chart-card,
            .activity-card {
                height: auto;
                min-height: 300px;
            }
        }

        /* ===== SCROLLBAR STYLING ===== */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--glass-bg-light);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--glass-border);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--elite-gray);
        }
    </style>
</head>
<body>
    <!-- Particles Background -->
    <div id="particles-js"></div>

    <!-- Main Dashboard Container -->
    <div class="dashboard-container">
        <!-- Header Section -->
        <header class="dashboard-header">
            <div class="header-content">
                <!-- Logo Section -->
                <div class="logo-section">
                    <div class="logo-icon">
                        <i class="fas fa-gem"></i>
                    </div>
                    <div class="logo-text">
                        <div class="logo-title">Elite Transfer</div>
                        <div class="logo-subtitle">نظام التحويلات الفاخر</div>
                    </div>
                </div>

                <!-- User Section -->
                <div class="user-section">
                    <div class="user-info">
                        <div class="user-name">مرحباً، <?= htmlspecialchars($userData['name'] ?? 'المدير') ?></div>
                        <div class="user-role">مدير النظام</div>
                    </div>
                    <div class="user-avatar">
                        <?= strtoupper(substr($userData['name'] ?? 'A', 0, 1)) ?>
                    </div>
                </div>
            </div>
        </header>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">جاري تحميل البيانات...</div>
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid" id="statsGrid">
            <!-- Stats will be loaded here -->
        </div>

        <!-- Main Content Grid -->
        <div class="content-grid">
            <!-- Chart Section -->
            <div class="chart-section">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>اتجاه التحويلات</h3>
                        <div class="chart-controls">
                            <button class="chart-btn active" data-period="7">7 أيام</button>
                            <button class="chart-btn" data-period="30">30 يوم</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="transfersChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="activity-section">
                <div class="activity-card">
                    <div class="activity-header">
                        <h3>النشاط الحديث</h3>
                        <button class="refresh-btn" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="activity-content" id="activityContent">
                        <!-- Activity will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="actions-section">
                <div class="actions-card">
                    <div class="actions-header">
                        <h3>إجراءات سريعة</h3>
                    </div>
                    <div class="actions-grid">
                        <button class="action-btn primary" onclick="window.location.href='countries_management.php'">
                            <i class="fas fa-globe"></i>
                            <span>إدارة البلدان</span>
                        </button>
                        <button class="action-btn success" onclick="window.location.href='analytics_advanced.php'">
                            <i class="fas fa-chart-line"></i>
                            <span>التحليلات</span>
                        </button>
                        <button class="action-btn warning" onclick="window.location.href='reports_advanced.php'">
                            <i class="fas fa-file-alt"></i>
                            <span>التقارير</span>
                        </button>
                        <button class="action-btn info" onclick="window.location.href='users_management_advanced.php'">
                            <i class="fas fa-users"></i>
                            <span>المستخدمون</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Top Countries -->
            <div class="countries-section">
                <div class="countries-card">
                    <div class="countries-header">
                        <h3>أهم البلدان</h3>
                    </div>
                    <div class="countries-list" id="countriesList">
                        <!-- Countries will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // ===== GLOBAL VARIABLES =====
        let dashboardData = {};
        let transfersChart = null;

        // ===== INITIALIZATION =====
        document.addEventListener('DOMContentLoaded', function() {
            initializeParticles();
            loadDashboardData();
            setupEventListeners();
        });

        // ===== PARTICLES BACKGROUND =====
        function initializeParticles() {
            particlesJS('particles-js', {
                particles: {
                    number: { value: 80, density: { enable: true, value_area: 800 } },
                    color: { value: '#ffffff' },
                    shape: { type: 'circle' },
                    opacity: { value: 0.1, random: false },
                    size: { value: 3, random: true },
                    line_linked: {
                        enable: true,
                        distance: 150,
                        color: '#ffffff',
                        opacity: 0.1,
                        width: 1
                    },
                    move: {
                        enable: true,
                        speed: 2,
                        direction: 'none',
                        random: false,
                        straight: false,
                        out_mode: 'out',
                        bounce: false
                    }
                },
                interactivity: {
                    detect_on: 'canvas',
                    events: {
                        onhover: { enable: true, mode: 'repulse' },
                        onclick: { enable: true, mode: 'push' },
                        resize: true
                    }
                },
                retina_detect: true
            });
        }

        // ===== EVENT LISTENERS =====
        function setupEventListeners() {
            // Chart period buttons
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    updateChart(this.dataset.period);
                });
            });
        }

        // ===== LOAD DASHBOARD DATA =====
        function loadDashboardData() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_dashboard_data' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        dashboardData = response;
                        renderStats(response.stats, response.counts, response.revenue);
                        renderChart(response.chart_data);
                        renderActivity(response.recent_transfers, response.recent_users);
                        renderCountries(response.top_countries);
                        hideLoading();
                    } else {
                        showError('فشل في تحميل البيانات: ' + (response.message || 'خطأ غير معروف'));
                        hideLoading();
                    }
                },
                error: function(xhr, status, error) {
                    showError('خطأ في الاتصال بالخادم: ' + error);
                    hideLoading();
                }
            });
        }

        // ===== RENDER STATS =====
        function renderStats(stats, counts, revenue) {
            const statsGrid = document.getElementById('statsGrid');

            const statsCards = [
                {
                    icon: 'fas fa-exchange-alt',
                    value: stats.total_transfers || 0,
                    label: 'إجمالي التحويلات',
                    change: '+' + (counts.completed_today || 0) + ' اليوم',
                    type: 'primary',
                    changeType: 'positive'
                },
                {
                    icon: 'fas fa-check-circle',
                    value: stats.completed_transfers || 0,
                    label: 'التحويلات المكتملة',
                    change: '+' + (counts.completed_today || 0) + ' اليوم',
                    type: 'success',
                    changeType: 'positive'
                },
                {
                    icon: 'fas fa-clock',
                    value: counts.pending || 0,
                    label: 'التحويلات المعلقة',
                    change: (counts.processing || 0) + ' قيد المعالجة',
                    type: 'warning',
                    changeType: 'neutral'
                },
                {
                    icon: 'fas fa-dollar-sign',
                    value: '$' + (revenue.today || 0).toLocaleString(),
                    label: 'إيرادات اليوم',
                    change: '$' + (revenue.month || 0).toLocaleString() + ' هذا الشهر',
                    type: 'info',
                    changeType: 'positive'
                },
                {
                    icon: 'fas fa-users',
                    value: counts.total_users || 0,
                    label: 'إجمالي المستخدمين',
                    change: '+' + (counts.new_users_today || 0) + ' جديد اليوم',
                    type: 'success',
                    changeType: 'positive'
                },
                {
                    icon: 'fas fa-times-circle',
                    value: counts.failed || 0,
                    label: 'التحويلات الفاشلة',
                    change: 'يحتاج مراجعة',
                    type: 'danger',
                    changeType: 'negative'
                }
            ];

            statsGrid.innerHTML = statsCards.map(card => `
                <div class="stat-card ${card.type}">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="${card.icon}"></i>
                        </div>
                    </div>
                    <div class="stat-value">${card.value}</div>
                    <div class="stat-label">${card.label}</div>
                    <div class="stat-change ${card.changeType}">
                        <i class="fas fa-${card.changeType === 'positive' ? 'arrow-up' : card.changeType === 'negative' ? 'arrow-down' : 'minus'}"></i>
                        <span>${card.change}</span>
                    </div>
                </div>
            `).join('');
        }

        // ===== RENDER CHART =====
        function renderChart(chartData) {
            const ctx = document.getElementById('transfersChart').getContext('2d');

            if (transfersChart) {
                transfersChart.destroy();
            }

            const labels = chartData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' });
            });

            transfersChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'مكتملة',
                            data: chartData.map(item => item.completed),
                            borderColor: '#4facfe',
                            backgroundColor: 'rgba(79, 172, 254, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'معلقة',
                            data: chartData.map(item => item.pending),
                            borderColor: '#43e97b',
                            backgroundColor: 'rgba(67, 233, 123, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'فاشلة',
                            data: chartData.map(item => item.failed),
                            borderColor: '#fa709a',
                            backgroundColor: 'rgba(250, 112, 154, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: 'white',
                                font: { family: 'Cairo', size: 12 },
                                usePointStyle: true,
                                padding: 20
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: 'rgba(255, 255, 255, 0.7)', font: { family: 'Cairo' } }
                        },
                        y: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: 'rgba(255, 255, 255, 0.7)', font: { family: 'Cairo' } }
                        }
                    },
                    elements: {
                        point: {
                            radius: 6,
                            hoverRadius: 8,
                            backgroundColor: 'white',
                            borderWidth: 2
                        }
                    }
                }
            });
        }

        // ===== RENDER ACTIVITY =====
        function renderActivity(transfers, users) {
            const activityContent = document.getElementById('activityContent');

            let activities = [];

            // Add recent transfers
            if (transfers && transfers.length > 0) {
                transfers.slice(0, 3).forEach(transfer => {
                    activities.push({
                        icon: 'fas fa-exchange-alt',
                        title: `تحويل جديد: ${transfer.sender_name} → ${transfer.recipient_name}`,
                        time: formatTime(transfer.created_at),
                        type: 'transfer'
                    });
                });
            }

            // Add recent users
            if (users && users.length > 0) {
                users.slice(0, 2).forEach(user => {
                    activities.push({
                        icon: 'fas fa-user-plus',
                        title: `مستخدم جديد: ${user.name}`,
                        time: formatTime(user.created_at),
                        type: 'user'
                    });
                });
            }

            if (activities.length === 0) {
                activityContent.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: var(--elite-gray-light);">
                        <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 16px; opacity: 0.5;"></i>
                        <p>لا توجد أنشطة حديثة</p>
                    </div>
                `;
                return;
            }

            activityContent.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="${activity.icon}"></i>
                    </div>
                    <div class="activity-details">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-time">${activity.time}</div>
                    </div>
                </div>
            `).join('');
        }

        // ===== RENDER COUNTRIES =====
        function renderCountries(countries) {
            const countriesList = document.getElementById('countriesList');

            if (!countries || countries.length === 0) {
                countriesList.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: var(--elite-gray-light);">
                        <i class="fas fa-globe" style="font-size: 2rem; margin-bottom: 16px; opacity: 0.5;"></i>
                        <p>لا توجد بيانات للبلدان</p>
                    </div>
                `;
                return;
            }

            countriesList.innerHTML = countries.map(country => `
                <div class="country-item">
                    <div class="country-name">${country.country_name}</div>
                    <div class="country-count">${country.transfer_count} تحويل</div>
                </div>
            `).join('');
        }

        // ===== UTILITY FUNCTIONS =====
        function formatTime(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));

            if (diffInHours < 1) return 'منذ قليل';
            if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;

            const diffInDays = Math.floor(diffInHours / 24);
            if (diffInDays < 7) return `منذ ${diffInDays} يوم`;

            return date.toLocaleDateString('ar-SA');
        }

        function hideLoading() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            loadingOverlay.classList.add('hidden');
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: message,
                confirmButtonText: 'حسناً',
                background: 'var(--elite-dark)',
                color: 'white'
            });
        }

        function refreshDashboard() {
            document.getElementById('loadingOverlay').classList.remove('hidden');
            loadDashboardData();
        }

        function updateChart(period) {
            // This would typically reload chart data for the selected period
            // For now, we'll just refresh the existing chart
            if (dashboardData.chart_data) {
                renderChart(dashboardData.chart_data);
            }
        }
    </script>
</body>
</html>
