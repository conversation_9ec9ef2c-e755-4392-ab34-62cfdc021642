<?php

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';
// Load session helper


header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Check database connection
    $db = new PDO('sqlite:' . __DIR__ . '/../database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get system statistics
    $stats = [];
    
    // Users count
    $stats['users_total'] = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $stats['users_active'] = $db->query("SELECT COUNT(*) FROM users WHERE is_active = 1")->fetchColumn();
    $stats['users_admin'] = $db->query("SELECT COUNT(*) FROM users WHERE role = 'admin'")->fetchColumn();
    
    // Transfers count
    $stats['transfers_total'] = $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn();
    $stats['transfers_completed'] = $db->query("SELECT COUNT(*) FROM transfers WHERE status = 'completed'")->fetchColumn();
    $stats['transfers_pending'] = $db->query("SELECT COUNT(*) FROM transfers WHERE status = 'pending'")->fetchColumn();
    $stats['transfers_today'] = $db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = DATE('now')")->fetchColumn();
    
    // Countries count
    $stats['countries_total'] = $db->query("SELECT COUNT(*) FROM countries")->fetchColumn();
    $stats['countries_active'] = $db->query("SELECT COUNT(*) FROM countries WHERE is_active = 1")->fetchColumn();
    
    // Exchange rates count
    $stats['exchange_rates'] = $db->query("SELECT COUNT(*) FROM exchange_rates")->fetchColumn();
    
    // System settings count
    $stats['system_settings'] = $db->query("SELECT COUNT(*) FROM system_settings")->fetchColumn();
    
    // Database size
    $dbPath = __DIR__ . '/../database/elite_transfer.db';
    $stats['database_size_mb'] = round(filesize($dbPath) / 1024 / 1024, 2);
    
    // Get current user info if logged in
    $currentUser = null;
    if (is_logged_in()) {
        $userData = get_user_data();
        $currentUser = [
            'id' => $userData['id'],
            'name' => $userData['name'],
            'role' => $userData['role']
        ];
    }
    
    $response = [
        'status' => 'healthy',
        'timestamp' => date('Y-m-d H:i:s'),
        'timezone' => date_default_timezone_get(),
        'database' => [
            'status' => 'connected',
            'type' => 'SQLite',
            'size_mb' => $stats['database_size_mb'],
            'path' => basename($dbPath)
        ],
        'statistics' => $stats,
        'system' => [
            'name' => 'Elite Transfer System',
            'version' => '7.0',
            'php_version' => PHP_VERSION,
            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'memory_peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2)
        ],
        'current_user' => $currentUser,
        'server' => [
            'software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'host' => $_SERVER['HTTP_HOST'] ?? 'localhost',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'GET',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
        ]
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'status' => 'unhealthy',
        'timestamp' => date('Y-m-d H:i:s'),
        'error' => [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => basename($e->getFile()),
            'line' => $e->getLine()
        ],
        'system' => [
            'name' => 'Elite Transfer System',
            'version' => '7.0',
            'php_version' => PHP_VERSION
        ]
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
