<?php

/**
 * Fix Transfers Table Structure
 * Elite Transfer System - Fix missing columns in transfers table
 */

require_once __DIR__ . '/includes/database_manager.php';

try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<title>إصلاح جدول التحويلات - Elite Transfer System</title>";
    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
    echo "<style>";
    echo "body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }";
    echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 20px; }";
    echo ".step { padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid; }";
    echo ".success { border-left-color: #28a745; background: #d4edda; color: #155724; }";
    echo ".error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }";
    echo ".warning { border-left-color: #ffc107; background: #fff3cd; color: #856404; }";
    echo ".info { border-left-color: #17a2b8; background: #d1ecf1; color: #0c5460; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";
    
    echo "<div class='container'>";
    echo "<h1 class='text-white text-center mb-4'>🔧 إصلاح جدول التحويلات</h1>";
    
    // Step 1: Check current table structure
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 1: فحص بنية الجدول الحالية</h3>";
    
    try {
        $columns = $db->query("DESCRIBE transfers")->fetchAll(PDO::FETCH_ASSOC);
        echo "<div class='step success'>✅ تم العثور على جدول transfers</div>";
        
        echo "<h5>الأعمدة الموجودة:</h5>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm table-striped'>";
        echo "<thead><tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
        echo "<tbody>";
        
        $existingColumns = [];
        foreach ($columns as $column) {
            $existingColumns[] = $column['Field'];
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='step error'>❌ خطأ في فحص الجدول: " . htmlspecialchars($e->getMessage()) . "</div>";
        
        // Create table if it doesn't exist
        echo "<div class='step info'>ℹ️ سيتم إنشاء جدول جديد...</div>";
        
        $createTableSQL = "
            CREATE TABLE transfers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                transfer_code VARCHAR(20) UNIQUE,
                pickup_code VARCHAR(20),
                user_id INT,
                sender_name VARCHAR(255),
                sender_phone VARCHAR(20),
                sender_country_id INT,
                sender_address TEXT,
                recipient_name VARCHAR(255),
                recipient_phone VARCHAR(20),
                recipient_country_id INT,
                recipient_address TEXT,
                amount DECIMAL(15,2),
                fee DECIMAL(15,2),
                exchange_rate DECIMAL(10,4),
                total_amount DECIMAL(15,2),
                currency_from VARCHAR(3) DEFAULT 'USD',
                currency_to VARCHAR(3) DEFAULT 'USD',
                payment_method VARCHAR(50),
                purpose TEXT,
                notes TEXT,
                status ENUM('pending', 'processing', 'completed', 'cancelled', 'failed') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                deleted_at TIMESTAMP NULL DEFAULT NULL,
                INDEX idx_user_id (user_id),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at),
                INDEX idx_transfer_code (transfer_code),
                INDEX idx_pickup_code (pickup_code)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $db->exec($createTableSQL);
        echo "<div class='step success'>✅ تم إنشاء جدول transfers بنجاح</div>";
        
        // Get columns again
        $columns = $db->query("DESCRIBE transfers")->fetchAll(PDO::FETCH_ASSOC);
        $existingColumns = array_column($columns, 'Field');
    }
    
    echo "</div>";
    
    // Step 2: Check for missing columns
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 2: فحص الأعمدة المطلوبة</h3>";
    
    $requiredColumns = [
        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'transfer_code' => 'VARCHAR(20)',
        'pickup_code' => 'VARCHAR(20)',
        'user_id' => 'INT',
        'sender_name' => 'VARCHAR(255)',
        'sender_phone' => 'VARCHAR(20)',
        'sender_country_id' => 'INT',
        'sender_address' => 'TEXT',
        'recipient_name' => 'VARCHAR(255)',
        'recipient_phone' => 'VARCHAR(20)',
        'recipient_country_id' => 'INT',
        'recipient_address' => 'TEXT',
        'amount' => 'DECIMAL(15,2)',
        'fee' => 'DECIMAL(15,2)',
        'exchange_rate' => 'DECIMAL(10,4)',
        'total_amount' => 'DECIMAL(15,2)',
        'currency_from' => 'VARCHAR(3) DEFAULT "USD"',
        'currency_to' => 'VARCHAR(3) DEFAULT "USD"',
        'payment_method' => 'VARCHAR(50)',
        'purpose' => 'TEXT',
        'notes' => 'TEXT',
        'status' => 'ENUM("pending", "processing", "completed", "cancelled", "failed") DEFAULT "pending"',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
        'deleted_at' => 'TIMESTAMP NULL DEFAULT NULL'
    ];
    
    $missingColumns = [];
    foreach ($requiredColumns as $column => $definition) {
        if (in_array($column, $existingColumns)) {
            echo "<div class='step success'>✅ العمود '$column' موجود</div>";
        } else {
            echo "<div class='step warning'>⚠️ العمود '$column' مفقود</div>";
            $missingColumns[$column] = $definition;
        }
    }
    
    echo "</div>";
    
    // Step 3: Add missing columns
    if (!empty($missingColumns)) {
        echo "<div class='fix-card'>";
        echo "<h3>الخطوة 3: إضافة الأعمدة المفقودة</h3>";
        
        foreach ($missingColumns as $column => $definition) {
            try {
                $alterSQL = "ALTER TABLE transfers ADD COLUMN $column $definition";
                $db->exec($alterSQL);
                echo "<div class='step success'>✅ تم إضافة العمود '$column'</div>";
            } catch (Exception $e) {
                echo "<div class='step error'>❌ فشل في إضافة العمود '$column': " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        }
        
        echo "</div>";
    } else {
        echo "<div class='fix-card'>";
        echo "<div class='step success'>✅ جميع الأعمدة المطلوبة موجودة</div>";
        echo "</div>";
    }
    
    // Step 4: Add indexes if missing
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 4: إضافة الفهارس</h3>";
    
    $indexes = [
        'idx_user_id' => 'user_id',
        'idx_status' => 'status',
        'idx_created_at' => 'created_at',
        'idx_transfer_code' => 'transfer_code',
        'idx_pickup_code' => 'pickup_code'
    ];
    
    foreach ($indexes as $indexName => $column) {
        try {
            $db->exec("CREATE INDEX $indexName ON transfers ($column)");
            echo "<div class='step success'>✅ تم إضافة فهرس '$indexName'</div>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<div class='step info'>ℹ️ الفهرس '$indexName' موجود بالفعل</div>";
            } else {
                echo "<div class='step warning'>⚠️ تحذير في إضافة الفهرس '$indexName': " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        }
    }
    
    echo "</div>";
    
    // Step 5: Add sample data if table is empty
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 5: فحص البيانات</h3>";
    
    $count = $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn();
    echo "<div class='step info'>ℹ️ عدد التحويلات الحالية: $count</div>";
    
    if ($count == 0) {
        echo "<div class='step warning'>⚠️ الجدول فارغ - سيتم إضافة بيانات تجريبية</div>";
        
        // Insert sample data
        $sampleData = [
            [
                'TRF001', 'PCK001', 1, 'أحمد محمد علي', '+************', 1, 'الرياض، المملكة العربية السعودية',
                'فاطمة أحمد', '+************', 2, 'القاهرة، مصر',
                1000.00, 25.00, 4.75, 1025.00, 'USD', 'EGP', 'bank_transfer', 'دعم الأسرة', 'تحويل شهري', 'completed'
            ],
            [
                'TRF002', 'PCK002', 1, 'سارة عبدالله', '+************', 1, 'جدة، المملكة العربية السعودية',
                'محمد حسن', '+************', 3, 'دبي، الإمارات العربية المتحدة',
                500.00, 15.00, 3.67, 515.00, 'USD', 'AED', 'cash', 'تعليم', 'رسوم دراسية', 'pending'
            ],
            [
                'TRF003', 'PCK003', 1, 'عبدالله سالم', '+************', 1, 'الدمام، المملكة العربية السعودية',
                'ليلى محمود', '+************', 4, 'عمان، الأردن',
                750.00, 20.00, 0.71, 770.00, 'USD', 'JOD', 'credit_card', 'علاج طبي', 'مساعدة طبية', 'processing'
            ]
        ];
        
        $insertSQL = "
            INSERT INTO transfers (
                transfer_code, pickup_code, user_id, sender_name, sender_phone, 
                sender_country_id, sender_address, recipient_name, recipient_phone,
                recipient_country_id, recipient_address, amount, fee, exchange_rate,
                total_amount, currency_from, currency_to, payment_method, purpose, notes, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ";
        
        $stmt = $db->prepare($insertSQL);
        
        foreach ($sampleData as $data) {
            try {
                $stmt->execute($data);
                echo "<div class='step success'>✅ تم إضافة تحويل تجريبي: {$data[0]}</div>";
            } catch (Exception $e) {
                echo "<div class='step error'>❌ فشل في إضافة تحويل {$data[0]}: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        }
    }
    
    echo "</div>";
    
    // Step 6: Test the fixed query
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 6: اختبار الاستعلام المُصلح</h3>";
    
    try {
        $testQuery = "
            SELECT t.*, 
                   COALESCE(u.name, 'غير محدد') as user_name,
                   COALESCE(sc.name, 'غير محدد') as sender_country,
                   COALESCE(rc.name, 'غير محدد') as recipient_country
            FROM transfers t
            LEFT JOIN users u ON t.user_id = u.id
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.recipient_country_id = rc.id
            WHERE t.deleted_at IS NULL 
            ORDER BY t.created_at DESC 
            LIMIT 5
        ";
        
        $stmt = $db->prepare($testQuery);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='step success'>✅ الاستعلام نجح! تم العثور على " . count($results) . " تحويل</div>";
        
        if (count($results) > 0) {
            echo "<h5>عينة من النتائج:</h5>";
            echo "<div class='table-responsive'>";
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>الرمز</th><th>المرسل</th><th>المستلم</th><th>المبلغ</th><th>الحالة</th></tr></thead>";
            echo "<tbody>";
            foreach ($results as $row) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['transfer_code']) . "</td>";
                echo "<td>" . htmlspecialchars($row['sender_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['recipient_name']) . "</td>";
                echo "<td>$" . number_format($row['amount'], 2) . "</td>";
                echo "<td>" . htmlspecialchars($row['status']) . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='step error'>❌ فشل الاستعلام: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Final step: Links to test
    echo "<div class='fix-card text-center'>";
    echo "<h3>🎉 تم الإصلاح بنجاح!</h3>";
    echo "<div class='d-grid gap-2 d-md-flex justify-content-center'>";
    echo "<a href='transfers_fixed.php' class='btn btn-success btn-lg'>اختبار الصفحة المُصلحة</a>";
    echo "<a href='debug_ajax.php' class='btn btn-info'>اختبار AJAX</a>";
    echo "<a href='admin_transfers_enhanced.php' class='btn btn-primary'>الصفحة الأصلية</a>";
    echo "<a href='dashboard.php' class='btn btn-secondary'>لوحة التحكم</a>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    echo "</body>";
    echo "</html>";
    
} catch (Exception $e) {
    echo "<!DOCTYPE html>";
    echo "<html><head><meta charset='UTF-8'><title>خطأ</title></head><body>";
    echo "<div class='container mt-5'>";
    echo "<div class='alert alert-danger'>";
    echo "<h4>خطأ في قاعدة البيانات</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    echo "</div>";
    echo "</body></html>";
}

?>
