<?php

namespace App\Http\Controllers;

use App\Models\Transfer;
use App\Models\User;
use App\Models\Country;
use App\Models\Currency;
use App\Models\TransferFee;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class TransferController extends Controller
{
    /**
     * Show transfer form
     */
    public function create()
    {
        $countries = Country::where('is_active', true)->get();
        $currencies = Currency::where('is_active', true)->get();
        
        return view('transfers.create', compact('countries', 'currencies'));
    }

    /**
     * Calculate transfer fees and exchange rate
     */
    public function calculateFees(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sender_country_id' => 'required|exists:countries,id',
            'receiver_country_id' => 'required|exists:countries,id',
            'sender_currency_id' => 'required|exists:currencies,id',
            'receiver_currency_id' => 'required|exists:currencies,id',
            'amount' => 'required|numeric|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $senderCurrency = Currency::findOrFail($request->sender_currency_id);
        $receiverCurrency = Currency::findOrFail($request->receiver_currency_id);
        $amount = $request->amount;

        // Get exchange rate
        $exchangeRate = $this->getExchangeRate($senderCurrency, $receiverCurrency);
        $convertedAmount = $amount * $exchangeRate;

        // Calculate fees
        $feeStructure = TransferFee::where('sender_country_id', $request->sender_country_id)
            ->where('receiver_country_id', $request->receiver_country_id)
            ->where('sender_currency_id', $request->sender_currency_id)
            ->where('receiver_currency_id', $request->receiver_currency_id)
            ->where('min_amount', '<=', $amount)
            ->where('max_amount', '>=', $amount)
            ->where('is_active', true)
            ->first();

        $feeAmount = 0;
        if ($feeStructure) {
            $feeAmount = $feeStructure->fixed_fee + ($amount * $feeStructure->percentage_fee / 100);
        } else {
            // Default fee calculation
            $feeAmount = max(5, $amount * 0.025); // Minimum $5 or 2.5%
        }

        $totalAmount = $amount + $feeAmount;

        return response()->json([
            'success' => true,
            'data' => [
                'amount' => $amount,
                'exchange_rate' => $exchangeRate,
                'converted_amount' => round($convertedAmount, 2),
                'fee_amount' => round($feeAmount, 2),
                'total_amount' => round($totalAmount, 2),
                'sender_currency' => $senderCurrency->code,
                'receiver_currency' => $receiverCurrency->code,
                'estimated_delivery' => $this->getEstimatedDeliveryTime($request->receiver_country_id),
            ]
        ]);
    }

    /**
     * Store a new transfer
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sender_country_id' => 'required|exists:countries,id',
            'receiver_country_id' => 'required|exists:countries,id',
            'sender_currency_id' => 'required|exists:currencies,id',
            'receiver_currency_id' => 'required|exists:currencies,id',
            'amount' => 'required|numeric|min:1',
            'receiver_name' => 'required|string|max:255',
            'receiver_phone' => 'required|string|max:20',
            'receiver_address' => 'nullable|string|max:500',
            'receiver_id_number' => 'nullable|string|max:50',
            'purpose' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
            'type' => 'required|in:individual,bulk,scheduled,anonymous,business',
            'priority' => 'required|in:low,normal,high,urgent',
            'scheduled_at' => 'nullable|date|after:now',
            'is_anonymous' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $amount = $request->amount;

        // Check if user can send this amount
        if (!$user->canSendTransfer($amount)) {
            return response()->json([
                'success' => false,
                'message' => 'Transfer amount exceeds your daily or monthly limit'
            ], 403);
        }

        DB::beginTransaction();
        
        try {
            // Get currencies and calculate amounts
            $senderCurrency = Currency::findOrFail($request->sender_currency_id);
            $receiverCurrency = Currency::findOrFail($request->receiver_currency_id);
            
            $exchangeRate = $this->getExchangeRate($senderCurrency, $receiverCurrency);
            $convertedAmount = $amount * $exchangeRate;
            
            // Calculate fees
            $feeCalculation = $this->calculateTransferFee($request->all());
            $feeAmount = $feeCalculation['fee_amount'];
            $totalAmount = $amount + $feeAmount;

            // Create transfer
            $transfer = Transfer::create([
                'sender_id' => $user->id,
                'sender_country_id' => $request->sender_country_id,
                'receiver_country_id' => $request->receiver_country_id,
                'sender_currency_id' => $request->sender_currency_id,
                'receiver_currency_id' => $request->receiver_currency_id,
                'amount' => $amount,
                'exchange_rate' => $exchangeRate,
                'converted_amount' => $convertedAmount,
                'fee_amount' => $feeAmount,
                'total_amount' => $totalAmount,
                'receiver_name' => $request->receiver_name,
                'receiver_phone' => $request->receiver_phone,
                'receiver_address' => $request->receiver_address,
                'receiver_id_number' => $request->receiver_id_number,
                'sender_name' => $user->name,
                'sender_phone' => $user->phone,
                'sender_address' => $user->address,
                'sender_id_number' => $user->national_id,
                'purpose' => $request->purpose,
                'notes' => $request->notes,
                'type' => $request->type,
                'priority' => $request->priority,
                'scheduled_at' => $request->scheduled_at,
                'is_anonymous' => $request->boolean('is_anonymous'),
                'status' => $request->scheduled_at ? 'scheduled' : 'pending',
                'branch_id' => $user->branch_id,
            ]);

            // Update user statistics
            $user->increment('total_transfers_sent');
            $user->increment('total_amount_sent', $amount);

            // Log the transfer creation
            AuditLog::create([
                'user_id' => $user->id,
                'action' => 'transfer_created',
                'model_type' => Transfer::class,
                'model_id' => $transfer->id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'severity' => 'medium',
                'description' => "Transfer created: {$transfer->transfer_code}",
                'metadata' => [
                    'amount' => $amount,
                    'currency' => $senderCurrency->code,
                    'receiver' => $request->receiver_name,
                ],
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Transfer created successfully',
                'data' => [
                    'transfer_code' => $transfer->transfer_code,
                    'pickup_code' => $transfer->pickup_code,
                    'amount' => $transfer->amount,
                    'converted_amount' => $transfer->converted_amount,
                    'fee_amount' => $transfer->fee_amount,
                    'total_amount' => $transfer->total_amount,
                    'status' => $transfer->status,
                    'estimated_delivery' => $this->getEstimatedDeliveryTime($request->receiver_country_id),
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create transfer. Please try again.'
            ], 500);
        }
    }

    /**
     * Track transfer by code
     */
    public function track(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'transfer_code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $transfer = Transfer::with([
            'sender:id,name,phone',
            'senderCountry:id,name,name_ar',
            'receiverCountry:id,name,name_ar',
            'senderCurrency:id,code,symbol',
            'receiverCurrency:id,code,symbol',
            'statusHistory.updatedBy:id,name'
        ])->where('transfer_code', $request->transfer_code)->first();

        if (!$transfer) {
            return response()->json([
                'success' => false,
                'message' => 'Transfer not found'
            ], 404);
        }

        // Check if user has permission to view this transfer
        $user = Auth::user();
        if ($user && !$this->canViewTransfer($user, $transfer)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to view this transfer'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'transfer_code' => $transfer->transfer_code,
                'status' => $transfer->status,
                'amount' => $transfer->amount,
                'converted_amount' => $transfer->converted_amount,
                'sender_currency' => $transfer->senderCurrency->code,
                'receiver_currency' => $transfer->receiverCurrency->code,
                'sender_country' => $transfer->senderCountry->name,
                'receiver_country' => $transfer->receiverCountry->name,
                'receiver_name' => $transfer->receiver_name,
                'created_at' => $transfer->created_at,
                'completed_at' => $transfer->completed_at,
                'timeline' => $transfer->getTrackingTimeline(),
            ]
        ]);
    }

    /**
     * Get exchange rate between currencies
     */
    private function getExchangeRate(Currency $from, Currency $to): float
    {
        if ($from->id === $to->id) {
            return 1.0;
        }

        return $from->getExchangeRateTo($to);
    }

    /**
     * Calculate transfer fee
     */
    private function calculateTransferFee(array $data): array
    {
        $amount = $data['amount'];
        
        $feeStructure = TransferFee::where('sender_country_id', $data['sender_country_id'])
            ->where('receiver_country_id', $data['receiver_country_id'])
            ->where('sender_currency_id', $data['sender_currency_id'])
            ->where('receiver_currency_id', $data['receiver_currency_id'])
            ->where('min_amount', '<=', $amount)
            ->where('max_amount', '>=', $amount)
            ->where('is_active', true)
            ->first();

        if ($feeStructure) {
            $feeAmount = $feeStructure->fixed_fee + ($amount * $feeStructure->percentage_fee / 100);
        } else {
            $feeAmount = max(5, $amount * 0.025); // Default: minimum $5 or 2.5%
        }

        return [
            'fee_amount' => round($feeAmount, 2),
            'fee_structure' => $feeStructure,
        ];
    }

    /**
     * Get estimated delivery time
     */
    private function getEstimatedDeliveryTime(int $countryId): string
    {
        $country = Country::find($countryId);
        return $country ? $country->getEstimatedProcessingTime() : '24 hours';
    }

    /**
     * Check if user can view transfer
     */
    private function canViewTransfer(User $user, Transfer $transfer): bool
    {
        // Admin can view all transfers
        if ($user->isAdmin()) {
            return true;
        }

        // Agent can view transfers in their branch
        if ($user->isAgent() && $user->branch_id === $transfer->branch_id) {
            return true;
        }

        // User can view their own transfers
        return $user->id === $transfer->sender_id || $user->id === $transfer->receiver_id;
    }
}
