/* ===== ELITE TRANSFER SYSTEM - UNIFIED THEME ===== */

:root {
    /* Elite Color Palette - Luxury & Professional */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --light-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    
    /* Sophisticated Neutrals */
    --elite-dark: #1a1d29;
    --elite-darker: #151821;
    --elite-light: #f8fafc;
    --elite-gray: #64748b;
    --elite-gray-light: #94a3b8;
    --elite-gray-dark: #475569;
    
    /* Glass Morphism - Premium */
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-bg-strong: rgba(255, 255, 255, 0.12);
    --glass-bg-light: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.15);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --glass-shadow-strong: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
    
    /* Spacing & Borders - Harmonious */
    --border-radius: 24px;
    --border-radius-small: 16px;
    --border-radius-large: 32px;
    --border-radius-xl: 40px;
    --spacing-xs: 8px;
    --spacing-sm: 16px;
    --spacing-md: 24px;
    --spacing-lg: 32px;
    --spacing-xl: 48px;
    --spacing-2xl: 64px;
    
    /* Typography - Elegant */
    --font-primary: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-secondary: 'Inter', 'Cairo', sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-black: 900;
    
    /* Transitions - Smooth */
    --transition-fast: 0.2s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
    
    /* Z-Index Layers */
    --z-background: -1;
    --z-base: 1;
    --z-elevated: 10;
    --z-floating: 100;
    --z-modal: 1000;
    --z-tooltip: 10000;
}

/* ===== GLOBAL RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
    color: white;
    background: var(--primary-gradient);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== ELITE COMPONENTS ===== */

/* Glass Cards */
.elite-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-large);
    padding: var(--spacing-lg);
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-normal);
}

.elite-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--glass-shadow-strong);
    border-color: rgba(255, 255, 255, 0.25);
}

.elite-card-strong {
    background: var(--glass-bg-strong);
}

/* Elite Buttons */
.elite-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    background: transparent;
    color: white;
    font-family: var(--font-primary);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.elite-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow);
    color: white;
    text-decoration: none;
}

.elite-btn-primary { background: var(--primary-gradient); border-color: transparent; }
.elite-btn-success { background: var(--success-gradient); border-color: transparent; }
.elite-btn-warning { background: var(--warning-gradient); border-color: transparent; }
.elite-btn-danger { background: var(--danger-gradient); border-color: transparent; }
.elite-btn-info { background: var(--info-gradient); border-color: transparent; }

.elite-btn-primary:hover,
.elite-btn-success:hover,
.elite-btn-warning:hover,
.elite-btn-danger:hover,
.elite-btn-info:hover {
    opacity: 0.9;
    color: white;
}

/* Elite Forms */
.elite-form-control {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    color: white;
    padding: var(--spacing-sm);
    font-family: var(--font-primary);
    transition: all var(--transition-fast);
}

.elite-form-control:focus {
    background: var(--glass-bg-strong);
    border-color: var(--success-gradient);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
    outline: none;
}

.elite-form-control::placeholder {
    color: var(--elite-gray-light);
}

/* Elite Tables */
.elite-table {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-large);
    overflow: hidden;
}

.elite-table table {
    width: 100%;
    color: white;
    border-collapse: collapse;
}

.elite-table th {
    background: var(--glass-bg-strong);
    padding: var(--spacing-md);
    font-weight: var(--font-weight-semibold);
    border-bottom: 1px solid var(--glass-border);
}

.elite-table td {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--glass-border);
}

.elite-table tr:hover {
    background: var(--glass-bg-light);
}

/* Elite Badges */
.elite-badge {
    padding: 4px 12px;
    border-radius: var(--border-radius-small);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.elite-badge-primary { background: var(--primary-gradient); color: white; }
.elite-badge-success { background: var(--success-gradient); color: white; }
.elite-badge-warning { background: var(--warning-gradient); color: white; }
.elite-badge-danger { background: var(--danger-gradient); color: white; }
.elite-badge-info { background: var(--info-gradient); color: white; }

/* Elite Headers */
.elite-header {
    background: var(--glass-bg-strong);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-large);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--glass-shadow);
    position: relative;
    overflow: hidden;
}

.elite-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-gradient);
    opacity: 0.8;
}

/* Elite Grid */
.elite-grid {
    display: grid;
    gap: var(--spacing-lg);
}

.elite-grid-2 { grid-template-columns: repeat(2, 1fr); }
.elite-grid-3 { grid-template-columns: repeat(3, 1fr); }
.elite-grid-4 { grid-template-columns: repeat(4, 1fr); }
.elite-grid-auto { grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); }

/* Elite Animations */
@keyframes eliteSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes eliteFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes eliteShine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.elite-animate-slide { animation: eliteSlideIn 0.6s ease-out; }
.elite-animate-fade { animation: eliteFadeIn 0.4s ease-out; }

/* Elite Responsive */
@media (max-width: 1200px) {
    .elite-grid-4 { grid-template-columns: repeat(2, 1fr); }
    .elite-grid-3 { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 768px) {
    .elite-grid-4,
    .elite-grid-3,
    .elite-grid-2 { 
        grid-template-columns: 1fr; 
    }
    
    .elite-card {
        padding: var(--spacing-md);
    }
    
    .elite-header {
        padding: var(--spacing-md);
    }
}

/* Elite Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--glass-bg-light);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--glass-border);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--elite-gray);
}
