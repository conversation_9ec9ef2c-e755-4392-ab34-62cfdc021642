<?php

/**
 * Test Functions
 * Elite Transfer System - Test session and database functions
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار الوظائف - Elite Transfer System</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }";
echo ".test-card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 20px; }";
echo ".test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }";
echo ".success { background: #d1e7dd; color: #0f5132; }";
echo ".error { background: #f8d7da; color: #842029; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 class='text-white text-center mb-4'>🧪 اختبار وظائف النظام</h1>";

echo "<div class='test-card'>";
echo "<h3>1. اختبار تحميل ملفات النظام</h3>";

// Test session helper
echo "<div class='test-result'>";
try {
    require_once __DIR__ . '/includes/session_helper.php';
    echo "<span class='success'>✅ تم تحميل session_helper.php بنجاح</span>";
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في تحميل session_helper.php: " . $e->getMessage() . "</span>";
}
echo "</div>";

// Test database manager
echo "<div class='test-result'>";
try {
    require_once __DIR__ . '/includes/database_manager.php';
    echo "<span class='success'>✅ تم تحميل database_manager.php بنجاح</span>";
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في تحميل database_manager.php: " . $e->getMessage() . "</span>";
}
echo "</div>";

echo "</div>";

echo "<div class='test-card'>";
echo "<h3>2. اختبار الوظائف</h3>";

// Test functions
$functions = ['isLoggedIn', 'getUserData', 'isAdmin', 'requireLogin', 'requireAdmin'];

foreach ($functions as $func) {
    echo "<div class='test-result'>";
    if (function_exists($func)) {
        echo "<span class='success'>✅ الوظيفة $func() موجودة</span>";
    } else {
        echo "<span class='error'>❌ الوظيفة $func() غير موجودة</span>";
    }
    echo "</div>";
}

echo "</div>";

echo "<div class='test-card'>";
echo "<h3>3. اختبار قاعدة البيانات</h3>";

echo "<div class='test-result'>";
try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    echo "<span class='success'>✅ الاتصال بقاعدة البيانات نجح</span>";
    
    $connectionInfo = $dbManager->getConnectionInfo();
    echo "<br><strong>نوع قاعدة البيانات:</strong> " . $connectionInfo['type'];
    echo "<br><strong>قاعدة البيانات:</strong> " . $connectionInfo['database'];
    echo "<br><strong>الخادم:</strong> " . $connectionInfo['host'];
    
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</span>";
}
echo "</div>";

echo "</div>";

echo "<div class='test-card'>";
echo "<h3>4. اختبار الجلسة</h3>";

echo "<div class='test-result'>";
if (function_exists('isLoggedIn')) {
    if (isLoggedIn()) {
        echo "<span class='success'>✅ المستخدم مسجل دخول</span>";
        
        if (function_exists('getUserData')) {
            $userData = getUserData();
            echo "<br><strong>الاسم:</strong> " . ($userData['name'] ?? 'غير محدد');
            echo "<br><strong>البريد:</strong> " . ($userData['email'] ?? 'غير محدد');
            echo "<br><strong>الدور:</strong> " . ($userData['role'] ?? 'غير محدد');
        }
        
        if (function_exists('isAdmin')) {
            if (isAdmin()) {
                echo "<br><span class='success'>✅ المستخدم مدير</span>";
            } else {
                echo "<br><span class='error'>❌ المستخدم ليس مدير</span>";
            }
        }
    } else {
        echo "<span class='error'>❌ المستخدم غير مسجل دخول</span>";
        echo "<br><a href='auto_login.php' class='btn btn-primary mt-2'>تسجيل دخول تلقائي</a>";
    }
} else {
    echo "<span class='error'>❌ وظيفة isLoggedIn غير متاحة</span>";
}
echo "</div>";

echo "</div>";

echo "<div class='test-card'>";
echo "<h3>5. اختبار المستخدمين في قاعدة البيانات</h3>";

echo "<div class='test-result'>";
try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    $stmt = $db->query("SELECT id, name, email, role, status FROM users WHERE deleted_at IS NULL");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<span class='success'>✅ تم العثور على " . count($users) . " مستخدم</span>";
    
    echo "<table class='table table-sm mt-3'>";
    echo "<thead><tr><th>ID</th><th>الاسم</th><th>البريد</th><th>الدور</th><th>الحالة</th></tr></thead>";
    echo "<tbody>";
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . htmlspecialchars($user['name']) . "</td>";
        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td>" . htmlspecialchars($user['role']) . "</td>";
        echo "<td>" . htmlspecialchars($user['status']) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في استعلام المستخدمين: " . $e->getMessage() . "</span>";
}
echo "</div>";

echo "</div>";

echo "<div class='test-card text-center'>";
echo "<h3>🔗 روابط الاختبار</h3>";
echo "<div class='d-grid gap-2'>";
echo "<a href='auto_login.php' class='btn btn-primary'>تسجيل دخول تلقائي</a>";
echo "<a href='dashboard.php' class='btn btn-success'>لوحة التحكم</a>";
echo "<a href='admin_users_enhanced.php' class='btn btn-info'>إدارة المستخدمين</a>";
echo "<a href='database_status.php' class='btn btn-warning'>حالة قاعدة البيانات</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

?>
