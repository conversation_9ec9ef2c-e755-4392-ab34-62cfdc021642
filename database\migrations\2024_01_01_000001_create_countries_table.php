<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_ar')->nullable();
            $table->string('code', 3)->unique();
            $table->string('iso_code', 2)->unique();
            $table->string('phone_code', 10);
            $table->foreignId('currency_id')->nullable()->constrained()->nullOnDelete();
            $table->string('flag_url')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_sender_country')->default(true);
            $table->boolean('is_receiver_country')->default(true);
            $table->string('timezone')->default('UTC');
            $table->decimal('exchange_rate_margin', 5, 4)->default(0.0250); // 2.5%
            $table->decimal('min_transfer_amount', 15, 2)->default(1.00);
            $table->decimal('max_transfer_amount', 15, 2)->default(50000.00);
            $table->decimal('daily_transfer_limit', 15, 2)->default(10000.00);
            $table->decimal('monthly_transfer_limit', 15, 2)->default(100000.00);
            $table->json('compliance_requirements')->nullable();
            $table->json('required_documents')->nullable();
            $table->integer('processing_time_hours')->default(24);
            $table->json('supported_languages')->nullable();
            $table->timestamps();

            $table->index(['is_active', 'is_sender_country']);
            $table->index(['is_active', 'is_receiver_country']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};
