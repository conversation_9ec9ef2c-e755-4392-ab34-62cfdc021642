<?php

/**
 * Elite Dashboard - Ultra Advanced Professional Control Panel
 * Elite Transfer System - Next Generation Dashboard
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Check authentication
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Handle logout
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    logout();
    header('Location: login.php');
    exit;
}

$db = DatabaseManager::getInstance();
$userData = getUserData();

// Ensure countries table exists
try {
    $db->execute("
        CREATE TABLE IF NOT EXISTS countries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            currency VARCHAR(3) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            status ENUM('active', 'inactive') DEFAULT 'active',
            flag_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP NULL,
            INDEX idx_code (code),
            INDEX idx_status (status),
            INDEX idx_deleted_at (deleted_at)
        )
    ");
} catch (Exception $e) {
    // Table might already exist
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'get_dashboard_data':
                // Get comprehensive statistics
                $stats = $db->getStatistics();
                $recentTransfers = $db->getTransfers([], 10, 0);
                $recentUsers = $db->getUsers([], 5, 0);
                
                // Get detailed counts
                $pendingCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'pending' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                $processingCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'processing' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                $completedToday = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'completed' AND DATE(created_at) = CURDATE() AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                $failedCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'failed' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                
                // Get revenue statistics
                $todayRevenue = $db->selectOne("SELECT COALESCE(SUM(fee), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
                $monthRevenue = $db->selectOne("SELECT COALESCE(SUM(fee), 0) as revenue FROM transfers WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE()) AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
                $yearRevenue = $db->selectOne("SELECT COALESCE(SUM(fee), 0) as revenue FROM transfers WHERE YEAR(created_at) = YEAR(CURDATE()) AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
                
                // Get user statistics
                $totalUsers = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE (deleted_at IS NULL OR deleted_at = '')")['count'];
                $activeUsers = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE status = 'active' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                $newUsersToday = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE() AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                
                // Get chart data for last 30 days
                $chartData = [];
                for ($i = 29; $i >= 0; $i--) {
                    $date = date('Y-m-d', strtotime("-$i days"));
                    $dayData = $db->selectOne("
                        SELECT 
                            COUNT(*) as transfers,
                            COALESCE(SUM(total_amount), 0) as amount,
                            COALESCE(SUM(fee), 0) as revenue,
                            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
                        FROM transfers 
                        WHERE DATE(created_at) = '$date' 
                        AND (deleted_at IS NULL OR deleted_at = '')
                    ");
                    
                    $chartData[] = [
                        'date' => $date,
                        'day' => date('D', strtotime($date)),
                        'transfers' => intval($dayData['transfers']),
                        'amount' => floatval($dayData['amount']),
                        'revenue' => floatval($dayData['revenue']),
                        'completed' => intval($dayData['completed']),
                        'failed' => intval($dayData['failed'])
                    ];
                }
                
                // Get top countries
                $topCountries = [];
                try {
                    $topCountries = $db->select("
                        SELECT
                            COALESCE(c.name, 'غير محدد') as country_name,
                            COUNT(t.id) as transfer_count,
                            COALESCE(SUM(t.total_amount), 0) as total_amount
                        FROM transfers t
                        LEFT JOIN countries c ON t.sender_country_id = c.id
                        WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                        AND (t.deleted_at IS NULL OR t.deleted_at = '')
                        GROUP BY c.id, c.name
                        ORDER BY transfer_count DESC
                        LIMIT 5
                    ");
                } catch (Exception $e) {
                    // If countries table doesn't exist, return empty array
                    $topCountries = [];
                }
                
                // Get performance metrics
                $avgProcessingTime = $db->selectOne("
                    SELECT AVG(TIMESTAMPDIFF(HOUR, created_at, updated_at)) as avg_hours
                    FROM transfers 
                    WHERE status = 'completed' 
                    AND updated_at IS NOT NULL
                    AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                ")['avg_hours'] ?? 0;
                
                echo json_encode([
                    'success' => true,
                    'stats' => $stats,
                    'recent_transfers' => $recentTransfers,
                    'recent_users' => $recentUsers,
                    'counts' => [
                        'pending' => intval($pendingCount),
                        'processing' => intval($processingCount),
                        'completed_today' => intval($completedToday),
                        'failed' => intval($failedCount),
                        'total_users' => intval($totalUsers),
                        'active_users' => intval($activeUsers),
                        'new_users_today' => intval($newUsersToday)
                    ],
                    'revenue' => [
                        'today' => floatval($todayRevenue),
                        'month' => floatval($monthRevenue),
                        'year' => floatval($yearRevenue)
                    ],
                    'chart_data' => $chartData,
                    'top_countries' => $topCountries,
                    'performance' => [
                        'avg_processing_time' => round(floatval($avgProcessingTime), 2)
                    ]
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'get_notifications':
                // Get system notifications
                $notifications = [
                    [
                        'id' => 1,
                        'type' => 'warning',
                        'title' => 'تحويلات معلقة',
                        'message' => "يوجد {$pendingCount} تحويل في انتظار الموافقة",
                        'time' => 'منذ دقائق',
                        'icon' => 'bi-clock'
                    ],
                    [
                        'id' => 2,
                        'type' => 'success',
                        'title' => 'إيرادات اليوم',
                        'message' => "تم تحقيق $" . number_format($todayRevenue, 2) . " اليوم",
                        'time' => 'منذ ساعة',
                        'icon' => 'bi-currency-dollar'
                    ],
                    [
                        'id' => 3,
                        'type' => 'info',
                        'title' => 'مستخدمون جدد',
                        'message' => "انضم {$newUsersToday} مستخدم جديد اليوم",
                        'time' => 'منذ ساعتين',
                        'icon' => 'bi-person-plus'
                    ]
                ];
                
                echo json_encode([
                    'success' => true,
                    'notifications' => $notifications
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'quick_action':
                $actionType = $_POST['type'] ?? '';
                $result = false;
                $message = '';
                
                switch ($actionType) {
                    case 'approve_all_pending':
                        $result = $db->update('transfers', 
                            ['status' => 'processing', 'updated_at' => date('Y-m-d H:i:s')], 
                            "status = 'pending' AND (deleted_at IS NULL OR deleted_at = '')"
                        );
                        $message = 'تم الموافقة على جميع التحويلات المعلقة';
                        break;
                        
                    case 'backup_database':
                        // Simulate backup process
                        sleep(2);
                        $result = true;
                        $message = 'تم إنشاء نسخة احتياطية من قاعدة البيانات';
                        break;
                        
                    case 'send_notifications':
                        // Simulate sending notifications
                        $result = true;
                        $message = 'تم إرسال الإشعارات للمستخدمين';
                        break;
                }
                
                echo json_encode([
                    'success' => $result,
                    'message' => $message
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode([
                    'success' => false, 
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في الخادم: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المتقدمة - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- CountUp.js -->
    <script src="https://cdn.jsdelivr.net/npm/countup@1.8.2/dist/countUp.min.js"></script>
    
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Particles.js -->
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <style>
        :root {
            /* Ultra Advanced Color System */
            --primary: #667eea;
            --primary-dark: #5a67d8;
            --secondary: #f093fb;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --dark: #1f2937;
            --light: #f8fafc;

            /* Gradient Collection */
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --gradient-warning: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --gradient-danger: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --gradient-purple: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-orange: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            --gradient-blue: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-green: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --gradient-pink: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --gradient-cosmic: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            --gradient-sunset: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #ff416c 100%);
            --gradient-ocean: linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #43e97b 100%);

            /* Glass Morphism System */
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-bg-strong: rgba(255, 255, 255, 0.15);
            --glass-bg-dark: rgba(0, 0, 0, 0.1);
            --glass-border: rgba(255, 255, 255, 0.18);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            --glass-shadow-strong: 0 15px 35px 0 rgba(31, 38, 135, 0.5);
            --glass-blur: blur(20px);
            --glass-blur-strong: blur(40px);

            /* Layout System */
            --sidebar-width: 320px;
            --sidebar-collapsed: 80px;
            --header-height: 80px;
            --footer-height: 60px;
            --border-radius: 20px;
            --border-radius-sm: 12px;
            --border-radius-lg: 25px;
            --border-radius-xl: 30px;

            /* Animation System */
            --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            --transition-elastic: 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);

            /* Typography System */
            --font-family: 'Cairo', sans-serif;
            --font-weight-light: 300;
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
            --font-weight-extrabold: 800;
            --font-weight-black: 900;

            /* Spacing System */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            --space-24: 6rem;
            --space-32: 8rem;

            /* Z-Index System */
            --z-base: 0;
            --z-dropdown: 1000;
            --z-sticky: 1020;
            --z-fixed: 1030;
            --z-modal-backdrop: 1040;
            --z-modal: 1050;
            --z-popover: 1060;
            --z-tooltip: 1070;
            --z-toast: 1080;
            --z-max: 9999;
        }

        /* Global Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-family);
            background: var(--gradient-cosmic);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Particles Background */
        #particles-js {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: var(--z-base);
            opacity: 0.3;
        }

        /* Main Layout */
        .dashboard-container {
            position: relative;
            z-index: var(--z-base);
            min-height: 100vh;
            display: flex;
        }

        /* Advanced Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: var(--glass-bg);
            backdrop-filter: var(--glass-blur);
            border-left: 1px solid var(--glass-border);
            transition: all var(--transition-normal);
            z-index: var(--z-fixed);
            overflow-y: auto;
            overflow-x: hidden;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: var(--glass-border);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: var(--glass-bg-strong);
        }

        /* Sidebar Header */
        .sidebar-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--glass-border);
            position: relative;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            color: white;
            text-decoration: none;
            transition: all var(--transition-normal);
        }

        .sidebar-logo:hover {
            color: white;
            transform: scale(1.05);
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: var(--glass-shadow);
        }

        .logo-text {
            font-size: 1.2rem;
            font-weight: var(--font-weight-bold);
            opacity: 1;
            transition: all var(--transition-normal);
        }

        .sidebar.collapsed .logo-text {
            opacity: 0;
            width: 0;
        }

        .sidebar-toggle {
            position: absolute;
            top: 50%;
            left: -15px;
            transform: translateY(-50%);
            width: 30px;
            height: 30px;
            background: var(--gradient-primary);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            transition: all var(--transition-normal);
            box-shadow: var(--glass-shadow);
            z-index: var(--z-tooltip);
        }

        .sidebar-toggle:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: var(--glass-shadow-strong);
        }

        /* Sidebar Menu */
        .sidebar-menu {
            padding: var(--space-4) 0;
        }

        .menu-section {
            margin-bottom: var(--space-6);
        }

        .menu-section-title {
            padding: 0 var(--space-6) var(--space-2);
            font-size: 0.75rem;
            font-weight: var(--font-weight-semibold);
            text-transform: uppercase;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.6);
            opacity: 1;
            transition: all var(--transition-normal);
        }

        .sidebar.collapsed .menu-section-title {
            opacity: 0;
            height: 0;
            padding: 0;
            margin: 0;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: var(--space-4) var(--space-6);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all var(--transition-normal);
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: var(--glass-bg-strong);
            transition: width var(--transition-normal);
            z-index: -1;
        }

        .menu-item:hover::before,
        .menu-item.active::before {
            width: 100%;
        }

        .menu-item:hover,
        .menu-item.active {
            color: white;
            border-left-color: var(--primary);
            transform: translateX(-5px);
        }

        .menu-item i {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: var(--space-3);
            font-size: 1.1rem;
            transition: all var(--transition-normal);
        }

        .menu-item:hover i {
            transform: scale(1.2);
        }

        .menu-text {
            font-weight: var(--font-weight-medium);
            opacity: 1;
            transition: all var(--transition-normal);
        }

        .sidebar.collapsed .menu-text {
            opacity: 0;
            width: 0;
        }

        .menu-badge {
            margin-right: auto;
            background: var(--gradient-danger);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: var(--font-weight-semibold);
            opacity: 1;
            transition: all var(--transition-normal);
        }

        .sidebar.collapsed .menu-badge {
            opacity: 0;
            width: 0;
        }

        /* Main Content */
        .main-content {
            margin-right: var(--sidebar-width);
            transition: all var(--transition-normal);
            min-height: 100vh;
            position: relative;
        }

        .main-content.expanded {
            margin-right: var(--sidebar-collapsed);
        }

        /* Top Header */
        .top-header {
            background: var(--glass-bg);
            backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            margin: var(--space-6);
            padding: var(--space-6);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--glass-shadow);
            position: sticky;
            top: var(--space-6);
            z-index: var(--z-sticky);
        }

        .welcome-section {
            color: white;
        }

        .welcome-title {
            font-size: 2rem;
            font-weight: var(--font-weight-bold);
            margin-bottom: var(--space-2);
            background: linear-gradient(45deg, #fff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .welcome-subtitle {
            font-size: 1rem;
            opacity: 0.8;
            margin: 0;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .action-btn {
            width: 45px;
            height: 45px;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            color: white;
            cursor: pointer;
            transition: all var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            position: relative;
        }

        .action-btn:hover {
            background: var(--glass-bg-strong);
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow);
        }

        .notification-dot {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            background: var(--gradient-danger);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        /* User Menu */
        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            border: 2px solid var(--glass-border);
            background: none;
            cursor: pointer;
            transition: all var(--transition-normal);
            overflow: hidden;
        }

        .user-avatar:hover {
            transform: scale(1.05);
            border-color: var(--primary);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            margin-top: var(--space-2);
            background: var(--glass-bg-strong);
            backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--glass-shadow-strong);
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all var(--transition-normal);
            z-index: var(--z-dropdown);
        }

        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-info {
            padding: var(--space-4);
            border-bottom: 1px solid var(--glass-border);
        }

        .user-name {
            font-weight: var(--font-weight-semibold);
            color: white;
            margin-bottom: var(--space-1);
        }

        .user-role {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.7);
            text-transform: capitalize;
        }

        .dropdown-divider {
            height: 1px;
            background: var(--glass-border);
            margin: var(--space-2) 0;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all var(--transition-fast);
        }

        .dropdown-item:hover {
            background: var(--glass-bg);
            color: white;
        }

        .dropdown-item.text-danger {
            color: #ff6b6b;
        }

        .dropdown-item.text-danger:hover {
            background: rgba(255, 107, 107, 0.1);
            color: #ff6b6b;
        }

        /* Dashboard Content */
        .dashboard-content {
            padding: 0 var(--space-6) var(--space-6);
        }

        /* Loading State */
        .loading-state {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 400px;
            color: white;
        }

        .loading-spinner {
            text-align: center;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto var(--space-4);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .stat-card {
            background: var(--glass-bg);
            backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: var(--space-6);
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left var(--transition-slow);
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--glass-shadow-strong);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--space-4);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            position: relative;
            z-index: 1;
        }

        .stat-icon.primary { background: var(--gradient-primary); }
        .stat-icon.success { background: var(--gradient-success); }
        .stat-icon.warning { background: var(--gradient-warning); }
        .stat-icon.danger { background: var(--gradient-danger); }
        .stat-icon.info { background: var(--gradient-info); }
        .stat-icon.purple { background: var(--gradient-purple); }
        .stat-icon.orange { background: var(--gradient-orange); }
        .stat-icon.blue { background: var(--gradient-blue); }
        .stat-icon.green { background: var(--gradient-green); }
        .stat-icon.pink { background: var(--gradient-pink); }

        .stat-change {
            background: rgba(255, 255, 255, 0.1);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--border-radius-sm);
            font-size: 0.75rem;
            font-weight: var(--font-weight-semibold);
            color: white;
        }

        .stat-change.positive {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
        }

        .stat-change.negative {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: var(--font-weight-extrabold);
            color: white;
            margin-bottom: var(--space-2);
            position: relative;
            z-index: 1;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            font-weight: var(--font-weight-medium);
            position: relative;
            z-index: 1;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            margin-top: var(--space-3);
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .trend-icon {
            font-size: 0.75rem;
        }

        .trend-up {
            color: #4caf50;
        }

        .trend-down {
            color: #f44336;
        }

        /* Charts Section */
        .chart-card {
            background: var(--glass-bg);
            backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
            height: 400px;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-4);
        }

        .chart-header h3 {
            color: white;
            font-size: 1.2rem;
            font-weight: var(--font-weight-semibold);
            margin: 0;
        }

        .chart-controls {
            display: flex;
            gap: var(--space-2);
        }

        .chart-controls .btn {
            padding: var(--space-1) var(--space-3);
            border-radius: var(--border-radius-sm);
            font-size: 0.8rem;
        }

        /* Activity Cards */
        .activity-card {
            background: var(--glass-bg);
            backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
            height: 500px;
            overflow: hidden;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-4);
            padding-bottom: var(--space-3);
            border-bottom: 1px solid var(--glass-border);
        }

        .card-header h3 {
            color: white;
            font-size: 1.2rem;
            font-weight: var(--font-weight-semibold);
            margin: 0;
        }

        .activity-list {
            max-height: 400px;
            overflow-y: auto;
            padding-right: var(--space-2);
        }

        .activity-list::-webkit-scrollbar {
            width: 6px;
        }

        .activity-list::-webkit-scrollbar-track {
            background: transparent;
        }

        .activity-list::-webkit-scrollbar-thumb {
            background: var(--glass-border);
            border-radius: 10px;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: var(--space-4);
            margin-bottom: var(--space-3);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--border-radius-sm);
            transition: all var(--transition-fast);
        }

        .activity-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-5px);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: var(--space-3);
            font-size: 1rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            color: white;
            font-weight: var(--font-weight-medium);
            margin-bottom: var(--space-1);
        }

        .activity-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
            margin-bottom: var(--space-1);
        }

        .activity-time {
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.75rem;
        }

        .activity-amount {
            text-align: left;
        }

        .amount {
            color: white;
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--space-1);
        }

        .status {
            padding: var(--space-1) var(--space-2);
            border-radius: var(--border-radius-sm);
            font-size: 0.75rem;
            font-weight: var(--font-weight-medium);
            text-align: center;
        }

        .status-pending { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
        .status-processing { background: rgba(0, 123, 255, 0.2); color: #007bff; }
        .status-completed { background: rgba(40, 167, 69, 0.2); color: #28a745; }
        .status-failed { background: rgba(220, 53, 69, 0.2); color: #dc3545; }
        .status-cancelled { background: rgba(108, 117, 125, 0.2); color: #6c757d; }

        /* Countries List */
        .countries-list {
            max-height: 400px;
            overflow-y: auto;
            padding-right: var(--space-2);
        }

        .country-item {
            display: flex;
            align-items: center;
            padding: var(--space-3);
            margin-bottom: var(--space-2);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--border-radius-sm);
            transition: all var(--transition-fast);
        }

        .country-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .country-rank {
            width: 30px;
            height: 30px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: var(--font-weight-semibold);
            font-size: 0.875rem;
            margin-left: var(--space-3);
        }

        .country-info {
            flex: 1;
        }

        .country-name {
            color: white;
            font-weight: var(--font-weight-medium);
            margin-bottom: var(--space-1);
        }

        .country-stats {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
        }

        .country-amount {
            color: white;
            font-weight: var(--font-weight-semibold);
        }

        /* Quick Actions */
        .quick-actions {
            margin-top: var(--space-8);
        }

        .quick-actions h3 {
            color: white;
            font-size: 1.5rem;
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--space-6);
            text-align: center;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
        }

        .action-card {
            background: var(--glass-bg);
            backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: var(--space-6);
            text-align: center;
            color: white;
            cursor: pointer;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left var(--transition-slow);
        }

        .action-card:hover::before {
            left: 100%;
        }

        .action-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: var(--glass-shadow-strong);
        }

        .action-card i {
            font-size: 2rem;
            margin-bottom: var(--space-3);
            display: block;
        }

        .action-card span {
            font-weight: var(--font-weight-medium);
        }

        /* No Data State */
        .no-data {
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
            padding: var(--space-8);
            font-style: italic;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-right: 280px;
            }
        }

        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(100%);
                z-index: var(--z-modal);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }

            .welcome-title {
                font-size: 1.5rem;
            }

            .top-header {
                flex-direction: column;
                gap: var(--space-4);
                text-align: center;
            }
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .chart-card {
                height: 300px;
            }

            .activity-card {
                height: auto;
                max-height: 400px;
            }
        }

        @media (max-width: 576px) {
            .actions-grid {
                grid-template-columns: 1fr;
            }

            .top-header {
                margin: var(--space-4);
                padding: var(--space-4);
            }

            .dashboard-content {
                padding: 0 var(--space-4) var(--space-4);
            }
        }

        /* Section Placeholder */
        .section-placeholder {
            text-align: center;
            color: white;
            padding: var(--space-20);
            background: var(--glass-bg);
            backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            margin: var(--space-6);
        }

        .section-placeholder i {
            opacity: 0.5;
            margin-bottom: var(--space-4);
        }

        .section-placeholder h2 {
            font-size: 2rem;
            font-weight: var(--font-weight-bold);
            margin-bottom: var(--space-3);
        }

        .section-placeholder p {
            font-size: 1.1rem;
            opacity: 0.7;
        }

        /* Notifications Popup */
        .notifications-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-item {
            display: flex;
            align-items: flex-start;
            padding: var(--space-4);
            margin-bottom: var(--space-3);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--border-radius-sm);
            transition: all var(--transition-fast);
        }

        .notification-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: var(--space-3);
            font-size: 1.1rem;
        }

        .notification-icon.success { background: var(--gradient-success); }
        .notification-icon.warning { background: var(--gradient-warning); }
        .notification-icon.info { background: var(--gradient-info); }
        .notification-icon.danger { background: var(--gradient-danger); }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--space-1);
            color: #333;
        }

        .notification-message {
            color: #666;
            margin-bottom: var(--space-1);
            line-height: 1.4;
        }

        .notification-time {
            font-size: 0.8rem;
            color: #999;
        }

        /* Custom SweetAlert Styles */
        .notifications-popup {
            background: var(--glass-bg-strong) !important;
            backdrop-filter: var(--glass-blur) !important;
            border: 1px solid var(--glass-border) !important;
        }

        .notifications-popup .swal2-title {
            color: white !important;
        }

        /* Loading Enhancements */
        .loading-state {
            background: var(--glass-bg);
            backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            margin: var(--space-6);
        }

        .loading-spinner p {
            margin-top: var(--space-4);
            font-size: 1.1rem;
            font-weight: var(--font-weight-medium);
        }

        /* Enhanced Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .slide-in-right {
            animation: slideInRight 0.6s ease-out;
        }

        .scale-in {
            animation: scaleIn 0.6s ease-out;
        }

        /* Hover Effects */
        .hover-lift {
            transition: all var(--transition-normal);
        }

        .hover-lift:hover {
            transform: translateY(-5px);
            box-shadow: var(--glass-shadow-strong);
        }

        .hover-glow {
            transition: all var(--transition-normal);
        }

        .hover-glow:hover {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--glass-border);
            border-radius: 10px;
            transition: all var(--transition-fast);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--glass-bg-strong);
        }

        /* Selection Styling */
        ::selection {
            background: rgba(102, 126, 234, 0.3);
            color: white;
        }

        ::-moz-selection {
            background: rgba(102, 126, 234, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Particles Background -->
    <div id="particles-js"></div>

    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Advanced Sidebar -->
        <div class="sidebar" id="sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <a href="#" class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="bi bi-bank2"></i>
                    </div>
                    <div class="logo-text"><?= SYSTEM_NAME ?></div>
                </a>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="bi bi-chevron-left" id="toggleIcon"></i>
                </button>
            </div>

            <!-- Sidebar Menu -->
            <div class="sidebar-menu">
                <!-- Dashboard Section -->
                <div class="menu-section">
                    <div class="menu-section-title">لوحة التحكم</div>
                    <a href="#" class="menu-item active" data-section="dashboard">
                        <i class="bi bi-speedometer2"></i>
                        <span class="menu-text">الرئيسية</span>
                    </a>
                    <a href="#" class="menu-item" data-section="analytics">
                        <i class="bi bi-graph-up"></i>
                        <span class="menu-text">التحليلات</span>
                    </a>
                    <a href="#" class="menu-item" data-section="reports">
                        <i class="bi bi-file-earmark-bar-graph"></i>
                        <span class="menu-text">التقارير</span>
                    </a>
                </div>

                <!-- Operations Section -->
                <div class="menu-section">
                    <div class="menu-section-title">العمليات</div>
                    <a href="transfers_safe.php" class="menu-item">
                        <i class="bi bi-arrow-left-right"></i>
                        <span class="menu-text">إدارة التحويلات</span>
                        <span class="menu-badge" id="pendingBadge">0</span>
                    </a>
                    <a href="create_transfer_fixed.php" class="menu-item">
                        <i class="bi bi-plus-circle"></i>
                        <span class="menu-text">تحويل جديد</span>
                    </a>
                    <a href="track_transfer_fixed.php" class="menu-item">
                        <i class="bi bi-search"></i>
                        <span class="menu-text">تتبع التحويل</span>
                    </a>
                </div>

                <!-- Management Section -->
                <div class="menu-section">
                    <div class="menu-section-title">الإدارة</div>
                    <a href="#" class="menu-item" data-section="users">
                        <i class="bi bi-people"></i>
                        <span class="menu-text">إدارة المستخدمين</span>
                    </a>
                    <a href="#" class="menu-item" data-section="countries">
                        <i class="bi bi-globe"></i>
                        <span class="menu-text">إدارة البلدان</span>
                    </a>
                    <a href="#" class="menu-item" data-section="settings">
                        <i class="bi bi-gear"></i>
                        <span class="menu-text">الإعدادات</span>
                    </a>
                </div>

                <!-- Tools Section -->
                <div class="menu-section">
                    <div class="menu-section-title">الأدوات</div>
                    <a href="#" class="menu-item" data-section="backup">
                        <i class="bi bi-cloud-download"></i>
                        <span class="menu-text">النسخ الاحتياطي</span>
                    </a>
                    <a href="#" class="menu-item" data-section="logs">
                        <i class="bi bi-journal-text"></i>
                        <span class="menu-text">سجل النظام</span>
                    </a>
                    <a href="#" class="menu-item" data-section="maintenance">
                        <i class="bi bi-tools"></i>
                        <span class="menu-text">الصيانة</span>
                    </a>
                </div>

                <!-- Account Section -->
                <div class="menu-section">
                    <div class="menu-section-title">الحساب</div>
                    <a href="#" class="menu-item" data-section="profile">
                        <i class="bi bi-person-circle"></i>
                        <span class="menu-text">الملف الشخصي</span>
                    </a>
                    <a href="#" class="menu-item" data-section="notifications">
                        <i class="bi bi-bell"></i>
                        <span class="menu-text">الإشعارات</span>
                        <span class="menu-badge" id="notificationBadge">3</span>
                    </a>
                    <a href="?action=logout" class="menu-item logout-btn" onclick="return confirmLogout()">
                        <i class="bi bi-box-arrow-left"></i>
                        <span class="menu-text">تسجيل الخروج</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <!-- Top Header -->
            <div class="top-header">
                <div class="header-left">
                    <div class="welcome-section">
                        <h1 class="welcome-title">مرحباً، <?= htmlspecialchars($userData['name']) ?></h1>
                        <p class="welcome-subtitle">إليك نظرة عامة على أداء النظام اليوم</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn" onclick="refreshDashboard()" title="تحديث البيانات">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        <button class="action-btn" onclick="showNotifications()" title="الإشعارات">
                            <i class="bi bi-bell"></i>
                            <span class="notification-dot"></span>
                        </button>
                        <button class="action-btn" onclick="toggleFullscreen()" title="ملء الشاشة">
                            <i class="bi bi-fullscreen"></i>
                        </button>
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img src="https://ui-avatars.com/api/?name=<?= urlencode($userData['name']) ?>&background=667eea&color=fff&size=40" alt="User Avatar">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <div class="user-info">
                                    <div class="user-name"><?= htmlspecialchars($userData['name']) ?></div>
                                    <div class="user-role"><?= htmlspecialchars($userData['role']) ?></div>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a href="#" class="dropdown-item">
                                    <i class="bi bi-person"></i>
                                    الملف الشخصي
                                </a>
                                <a href="#" class="dropdown-item">
                                    <i class="bi bi-gear"></i>
                                    الإعدادات
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="?action=logout" class="dropdown-item text-danger" onclick="return confirmLogout()">
                                    <i class="bi bi-box-arrow-left"></i>
                                    تسجيل الخروج
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Content -->
            <div class="dashboard-content" id="dashboardContent">
                <!-- Loading State -->
                <div class="loading-state" id="loadingState">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                        <p>جاري تحميل البيانات...</p>
                    </div>
                </div>

                <!-- Main Dashboard Content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global Variables
        let dashboardData = {};
        let charts = {};
        let refreshInterval;
        let sidebarCollapsed = false;

        // Initialize Dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeParticles();
            initializeAOS();
            loadDashboardData();
            setupEventListeners();
            startAutoRefresh();
        });

        // Initialize Particles.js
        function initializeParticles() {
            particlesJS('particles-js', {
                particles: {
                    number: { value: 80, density: { enable: true, value_area: 800 } },
                    color: { value: '#ffffff' },
                    shape: { type: 'circle' },
                    opacity: { value: 0.5, random: false },
                    size: { value: 3, random: true },
                    line_linked: {
                        enable: true,
                        distance: 150,
                        color: '#ffffff',
                        opacity: 0.4,
                        width: 1
                    },
                    move: {
                        enable: true,
                        speed: 6,
                        direction: 'none',
                        random: false,
                        straight: false,
                        out_mode: 'out',
                        bounce: false
                    }
                },
                interactivity: {
                    detect_on: 'canvas',
                    events: {
                        onhover: { enable: true, mode: 'repulse' },
                        onclick: { enable: true, mode: 'push' },
                        resize: true
                    }
                },
                retina_detect: true
            });
        }

        // Initialize AOS Animation
        function initializeAOS() {
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                mirror: false
            });
        }

        // Setup Event Listeners
        function setupEventListeners() {
            // Sidebar menu items
            document.querySelectorAll('.menu-item[data-section]').forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.dataset.section;
                    switchSection(section);
                    setActiveMenuItem(this);
                });
            });

            // Close user dropdown when clicking outside
            document.addEventListener('click', function(e) {
                const userMenu = document.querySelector('.user-menu');
                const userDropdown = document.getElementById('userDropdown');

                if (!userMenu.contains(e.target)) {
                    userDropdown.classList.remove('show');
                }
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'r':
                            e.preventDefault();
                            refreshDashboard();
                            break;
                        case 'b':
                            e.preventDefault();
                            toggleSidebar();
                            break;
                        case 'f':
                            e.preventDefault();
                            toggleFullscreen();
                            break;
                    }
                }
            });
        }

        // Toggle Sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const toggleIcon = document.getElementById('toggleIcon');

            sidebarCollapsed = !sidebarCollapsed;

            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
                toggleIcon.className = 'bi bi-chevron-right';
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
                toggleIcon.className = 'bi bi-chevron-left';
            }

            // Resize charts after sidebar toggle
            setTimeout(() => {
                Object.values(charts).forEach(chart => {
                    if (chart && chart.resize) {
                        chart.resize();
                    }
                });
            }, 300);
        }

        // Toggle User Menu
        function toggleUserMenu() {
            const userDropdown = document.getElementById('userDropdown');
            userDropdown.classList.toggle('show');
        }

        // Toggle Fullscreen
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log('Error attempting to enable fullscreen:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }

        // Set Active Menu Item
        function setActiveMenuItem(activeItem) {
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });
            activeItem.classList.add('active');
        }

        // Switch Section
        function switchSection(section) {
            const content = document.getElementById('dashboardContent');

            switch(section) {
                case 'dashboard':
                    loadDashboardData();
                    break;
                case 'analytics':
                    loadAnalyticsSection();
                    break;
                case 'reports':
                    loadReportsSection();
                    break;
                case 'users':
                    loadUsersSection();
                    break;
                case 'countries':
                    loadCountriesSection();
                    break;
                case 'settings':
                    loadSettingsSection();
                    break;
                case 'backup':
                    loadBackupSection();
                    break;
                case 'logs':
                    loadLogsSection();
                    break;
                case 'maintenance':
                    loadMaintenanceSection();
                    break;
                case 'profile':
                    loadProfileSection();
                    break;
                case 'notifications':
                    loadNotificationsSection();
                    break;
                default:
                    loadDashboardData();
            }
        }

        // Load Dashboard Data
        function loadDashboardData() {
            showLoading();

            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_dashboard_data' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        dashboardData = response;
                        renderDashboard();
                        updateBadges();
                    } else {
                        showError('فشل في تحميل بيانات لوحة التحكم');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                },
                complete: function() {
                    hideLoading();
                }
            });
        }

        // Render Dashboard
        function renderDashboard() {
            const content = document.getElementById('dashboardContent');

            content.innerHTML = `
                <!-- Stats Grid -->
                <div class="stats-grid" data-aos="fade-up">
                    ${renderStatsCards()}
                </div>

                <!-- Charts Section -->
                <div class="row mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="col-lg-8">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>إحصائيات التحويلات (آخر 30 يوم)</h3>
                                <div class="chart-controls">
                                    <button class="btn btn-sm btn-outline-light" onclick="changeChartType('line')">خطي</button>
                                    <button class="btn btn-sm btn-outline-light" onclick="changeChartType('bar')">أعمدة</button>
                                </div>
                            </div>
                            <canvas id="transfersChart"></canvas>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>توزيع الحالات</h3>
                            </div>
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="row" data-aos="fade-up" data-aos-delay="400">
                    <div class="col-lg-8">
                        <div class="activity-card">
                            <div class="card-header">
                                <h3>التحويلات الأخيرة</h3>
                                <a href="transfers_safe.php" class="btn btn-sm btn-outline-light">عرض الكل</a>
                            </div>
                            <div class="activity-list">
                                ${renderRecentTransfers()}
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="activity-card">
                            <div class="card-header">
                                <h3>أهم البلدان</h3>
                            </div>
                            <div class="countries-list">
                                ${renderTopCountries()}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions" data-aos="fade-up" data-aos-delay="600">
                    <h3>إجراءات سريعة</h3>
                    <div class="actions-grid">
                        <button class="action-card" onclick="quickAction('approve_all_pending')">
                            <i class="bi bi-check-circle"></i>
                            <span>الموافقة على الكل</span>
                        </button>
                        <button class="action-card" onclick="quickAction('backup_database')">
                            <i class="bi bi-cloud-download"></i>
                            <span>نسخ احتياطي</span>
                        </button>
                        <button class="action-card" onclick="quickAction('send_notifications')">
                            <i class="bi bi-bell"></i>
                            <span>إرسال إشعارات</span>
                        </button>
                        <button class="action-card" onclick="window.open('create_transfer_fixed.php')">
                            <i class="bi bi-plus-circle"></i>
                            <span>تحويل جديد</span>
                        </button>
                    </div>
                </div>
            `;

            // Initialize charts after content is rendered
            setTimeout(() => {
                initializeCharts();
                AOS.refresh();
            }, 100);
        }

        // Render Stats Cards
        function renderStatsCards() {
            const data = dashboardData;
            const cards = [
                {
                    icon: 'bi-arrow-left-right',
                    iconClass: 'primary',
                    value: data.stats?.total_transfers || 0,
                    label: 'إجمالي التحويلات',
                    change: '+12%',
                    changeType: 'positive',
                    trend: 'up'
                },
                {
                    icon: 'bi-currency-dollar',
                    iconClass: 'success',
                    value: '$' + (data.revenue?.month || 0).toLocaleString(),
                    label: 'إيرادات الشهر',
                    change: '+8%',
                    changeType: 'positive',
                    trend: 'up'
                },
                {
                    icon: 'bi-people',
                    iconClass: 'info',
                    value: data.counts?.active_users || 0,
                    label: 'المستخدمون النشطون',
                    change: '+5%',
                    changeType: 'positive',
                    trend: 'up'
                },
                {
                    icon: 'bi-clock',
                    iconClass: 'warning',
                    value: data.counts?.pending || 0,
                    label: 'تحويلات معلقة',
                    change: '-3%',
                    changeType: 'negative',
                    trend: 'down'
                },
                {
                    icon: 'bi-check-circle',
                    iconClass: 'green',
                    value: data.counts?.completed_today || 0,
                    label: 'مكتملة اليوم',
                    change: '+15%',
                    changeType: 'positive',
                    trend: 'up'
                },
                {
                    icon: 'bi-graph-up',
                    iconClass: 'purple',
                    value: '$' + (data.revenue?.today || 0).toLocaleString(),
                    label: 'إيرادات اليوم',
                    change: '+22%',
                    changeType: 'positive',
                    trend: 'up'
                }
            ];

            return cards.map(card => `
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon ${card.iconClass}">
                            <i class="${card.icon}"></i>
                        </div>
                        <div class="stat-change ${card.changeType}">
                            ${card.change}
                        </div>
                    </div>
                    <div class="stat-value" data-countup="${card.value.replace(/[^0-9.]/g, '')}">${card.value}</div>
                    <div class="stat-label">${card.label}</div>
                    <div class="stat-trend">
                        <i class="bi bi-arrow-${card.trend} trend-${card.trend} trend-icon"></i>
                        <span>مقارنة بالشهر الماضي</span>
                    </div>
                </div>
            `).join('');
        }

        // Render Recent Transfers
        function renderRecentTransfers() {
            const transfers = dashboardData.recent_transfers || [];

            if (transfers.length === 0) {
                return '<div class="no-data">لا توجد تحويلات حديثة</div>';
            }

            return transfers.map(transfer => `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="bi bi-arrow-left-right"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${transfer.sender_name} → ${transfer.recipient_name}</div>
                        <div class="activity-subtitle">رمز التحويل: ${transfer.transfer_code}</div>
                        <div class="activity-time">${formatDate(transfer.created_at)}</div>
                    </div>
                    <div class="activity-amount">
                        <div class="amount">$${parseFloat(transfer.total_amount).toLocaleString()}</div>
                        <div class="status status-${transfer.status}">${getStatusLabel(transfer.status)}</div>
                    </div>
                </div>
            `).join('');
        }

        // Render Top Countries
        function renderTopCountries() {
            const countries = dashboardData.top_countries || [];

            if (countries.length === 0) {
                return '<div class="no-data">لا توجد بيانات</div>';
            }

            return countries.map((country, index) => `
                <div class="country-item">
                    <div class="country-rank">${index + 1}</div>
                    <div class="country-info">
                        <div class="country-name">${country.country_name || 'غير محدد'}</div>
                        <div class="country-stats">${country.transfer_count} تحويل</div>
                    </div>
                    <div class="country-amount">$${parseFloat(country.total_amount || 0).toLocaleString()}</div>
                </div>
            `).join('');
        }

        // Initialize Charts
        function initializeCharts() {
            initializeTransfersChart();
            initializeStatusChart();
            initializeCountUpAnimations();
        }

        // Initialize Transfers Chart
        function initializeTransfersChart() {
            const ctx = document.getElementById('transfersChart');
            if (!ctx) return;

            const chartData = dashboardData.chart_data || [];

            charts.transfersChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: chartData.map(item => item.day),
                    datasets: [
                        {
                            label: 'التحويلات',
                            data: chartData.map(item => item.transfers),
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'الإيرادات',
                            data: chartData.map(item => item.revenue),
                            borderColor: '#38ef7d',
                            backgroundColor: 'rgba(56, 239, 125, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: 'white',
                                usePointStyle: true,
                                padding: 20
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' },
                            grid: { drawOnChartArea: false }
                        }
                    }
                }
            });
        }

        // Initialize Status Chart
        function initializeStatusChart() {
            const ctx = document.getElementById('statusChart');
            if (!ctx) return;

            const counts = dashboardData.counts || {};

            charts.statusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['معلقة', 'قيد المعالجة', 'مكتملة', 'فاشلة'],
                    datasets: [{
                        data: [
                            counts.pending || 0,
                            counts.processing || 0,
                            counts.completed_today || 0,
                            counts.failed || 0
                        ],
                        backgroundColor: [
                            '#ffc107',
                            '#007bff',
                            '#28a745',
                            '#dc3545'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: 'white',
                                usePointStyle: true,
                                padding: 15
                            }
                        }
                    }
                }
            });
        }

        // Initialize CountUp Animations
        function initializeCountUpAnimations() {
            document.querySelectorAll('[data-countup]').forEach(element => {
                const value = parseFloat(element.dataset.countup) || 0;
                const countUp = new CountUp(element, value, {
                    duration: 2,
                    useEasing: true,
                    useGrouping: true
                });

                if (!countUp.error) {
                    countUp.start();
                }
            });
        }

        // Utility Functions
        function showLoading() {
            document.getElementById('loadingState').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: message,
                confirmButtonText: 'حسناً',
                confirmButtonColor: '#dc3545'
            });
        }

        function showSuccess(message) {
            Swal.fire({
                icon: 'success',
                title: 'نجح!',
                text: message,
                confirmButtonText: 'ممتاز',
                confirmButtonColor: '#28a745'
            });
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function getStatusLabel(status) {
            const labels = {
                'pending': 'معلق',
                'processing': 'قيد المعالجة',
                'completed': 'مكتمل',
                'failed': 'فاشل',
                'cancelled': 'ملغي'
            };
            return labels[status] || status;
        }

        function updateBadges() {
            const pendingBadge = document.getElementById('pendingBadge');
            const notificationBadge = document.getElementById('notificationBadge');

            if (pendingBadge && dashboardData.counts) {
                pendingBadge.textContent = dashboardData.counts.pending || 0;
            }

            if (notificationBadge) {
                notificationBadge.textContent = '3'; // Static for now
            }
        }

        function refreshDashboard() {
            showLoading();
            loadDashboardData();

            // Show success message
            setTimeout(() => {
                const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 2000,
                    timerProgressBar: true
                });

                Toast.fire({
                    icon: 'success',
                    title: 'تم تحديث البيانات'
                });
            }, 1000);
        }

        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                loadDashboardData();
            }, 30000); // Refresh every 30 seconds
        }

        function showNotifications() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_notifications' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        const notifications = response.notifications;
                        let notificationsHtml = notifications.map(notif => `
                            <div class="notification-item">
                                <div class="notification-icon ${notif.type}">
                                    <i class="${notif.icon}"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title">${notif.title}</div>
                                    <div class="notification-message">${notif.message}</div>
                                    <div class="notification-time">${notif.time}</div>
                                </div>
                            </div>
                        `).join('');

                        Swal.fire({
                            title: 'الإشعارات',
                            html: `<div class="notifications-list">${notificationsHtml}</div>`,
                            width: 600,
                            confirmButtonText: 'إغلاق',
                            customClass: {
                                popup: 'notifications-popup'
                            }
                        });
                    }
                }
            });
        }

        function quickAction(type) {
            let title, text, confirmText;

            switch(type) {
                case 'approve_all_pending':
                    title = 'الموافقة على جميع التحويلات المعلقة';
                    text = 'هل أنت متأكد من الموافقة على جميع التحويلات المعلقة؟';
                    confirmText = 'نعم، وافق على الكل';
                    break;
                case 'backup_database':
                    title = 'إنشاء نسخة احتياطية';
                    text = 'سيتم إنشاء نسخة احتياطية من قاعدة البيانات';
                    confirmText = 'نعم، أنشئ النسخة';
                    break;
                case 'send_notifications':
                    title = 'إرسال إشعارات';
                    text = 'سيتم إرسال إشعارات لجميع المستخدمين';
                    confirmText = 'نعم، أرسل الإشعارات';
                    break;
            }

            Swal.fire({
                title: title,
                text: text,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: confirmText,
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#007bff'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'جاري التنفيذ...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    $.ajax({
                        url: '',
                        method: 'POST',
                        data: { action: 'quick_action', type: type },
                        dataType: 'json',
                        success: function(response) {
                            if (response && response.success) {
                                showSuccess(response.message);
                                loadDashboardData(); // Refresh data
                            } else {
                                showError(response.message || 'فشل في تنفيذ العملية');
                            }
                        },
                        error: function() {
                            showError('خطأ في الاتصال بالخادم');
                        }
                    });
                }
            });
        }

        function changeChartType(type) {
            if (charts.transfersChart) {
                charts.transfersChart.config.type = type;
                charts.transfersChart.update();
            }
        }

        function confirmLogout() {
            Swal.fire({
                title: 'تسجيل الخروج',
                text: 'هل أنت متأكد من تسجيل الخروج من النظام؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، تسجيل الخروج',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'جاري تسجيل الخروج...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Redirect to logout
                    setTimeout(() => {
                        window.location.href = '?action=logout';
                    }, 1000);
                }
            });

            return false;
        }

        // Section Loading Functions
        function loadAnalyticsSection() {
            window.location.href = 'analytics_advanced.php';
        }

        function loadReportsSection() {
            window.location.href = 'reports_advanced.php';
        }

        function loadUsersSection() {
            window.location.href = 'users_management_advanced.php';
        }

        function loadCountriesSection() {
            window.location.href = 'countries_management.php';
        }

        function loadSettingsSection() {
            window.location.href = 'settings_management.php';
        }

        function loadBackupSection() {
            document.getElementById('dashboardContent').innerHTML = `
                <div class="section-placeholder" data-aos="fade-up">
                    <i class="bi bi-cloud-download display-1 mb-4"></i>
                    <h2>النسخ الاحتياطي</h2>
                    <p>سيتم تطوير هذا القسم قريباً</p>
                </div>
            `;
            AOS.refresh();
        }

        function loadLogsSection() {
            document.getElementById('dashboardContent').innerHTML = `
                <div class="section-placeholder" data-aos="fade-up">
                    <i class="bi bi-journal-text display-1 mb-4"></i>
                    <h2>سجل النظام</h2>
                    <p>سيتم تطوير هذا القسم قريباً</p>
                </div>
            `;
            AOS.refresh();
        }

        function loadMaintenanceSection() {
            document.getElementById('dashboardContent').innerHTML = `
                <div class="section-placeholder" data-aos="fade-up">
                    <i class="bi bi-tools display-1 mb-4"></i>
                    <h2>الصيانة</h2>
                    <p>سيتم تطوير هذا القسم قريباً</p>
                </div>
            `;
            AOS.refresh();
        }

        function loadProfileSection() {
            document.getElementById('dashboardContent').innerHTML = `
                <div class="section-placeholder" data-aos="fade-up">
                    <i class="bi bi-person-circle display-1 mb-4"></i>
                    <h2>الملف الشخصي</h2>
                    <p>سيتم تطوير هذا القسم قريباً</p>
                </div>
            `;
            AOS.refresh();
        }

        function loadNotificationsSection() {
            showNotifications();
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
