<?php

/**
 * Create Transfer - Fixed Version
 * Elite Transfer System - Create new transfer with proper database connection
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

$userData = getUserData();
$userName = $userData['name'] ?? 'مستخدم';
$userRole = $userData['role'] ?? 'user';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $db = DatabaseManager::getInstance();
        
        if ($_POST['action'] === 'create_transfer') {
            // Get form data
            $senderName = trim($_POST['sender_name'] ?? '');
            $senderPhone = trim($_POST['sender_phone'] ?? '');
            $senderCountry = intval($_POST['sender_country'] ?? 0);
            $senderAddress = trim($_POST['sender_address'] ?? '');
            
            $receiverName = trim($_POST['receiver_name'] ?? '');
            $receiverPhone = trim($_POST['receiver_phone'] ?? '');
            $receiverCountry = intval($_POST['receiver_country'] ?? 0);
            $receiverAddress = trim($_POST['receiver_address'] ?? '');
            
            $amount = floatval($_POST['amount'] ?? 0);
            $purpose = trim($_POST['purpose'] ?? '');
            $pickupMethod = trim($_POST['pickup_method'] ?? '');
            $paymentMethod = trim($_POST['payment_method'] ?? '');
            $bankAccount = trim($_POST['bank_account'] ?? '');
            
            // Validation
            if (empty($senderName) || empty($receiverName) || $amount <= 0) {
                echo json_encode(['success' => false, 'message' => 'يرجى ملء جميع الحقول المطلوبة'], JSON_UNESCAPED_UNICODE);
                exit;
            }
            
            // Generate codes
            $transferCode = 'TRF' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
            $pickupCode = 'PCK' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
            
            // Calculate fees and exchange rate (simplified)
            $fee = $amount * 0.025; // 2.5% fee
            $exchangeRate = 1.0; // Default rate
            $totalAmount = $amount + $fee;
            
            // Get user ID
            $userId = $_SESSION['user_id'] ?? null;
            
            // Prepare transfer data
            $notes = "طريقة الاستلام: $pickupMethod";
            if (!empty($bankAccount)) {
                $notes .= " | حساب بنكي: $bankAccount";
            }

            $transferData = [
                'transfer_code' => $transferCode,
                'pickup_code' => $pickupCode,
                'user_id' => $userId,
                'sender_name' => $senderName,
                'sender_phone' => $senderPhone,
                'sender_country_id' => $senderCountry,
                'sender_address' => $senderAddress,
                'recipient_name' => $receiverName,
                'recipient_phone' => $receiverPhone,
                'recipient_country_id' => $receiverCountry,
                'recipient_address' => $receiverAddress,
                'amount' => $amount,
                'fee' => $fee,
                'exchange_rate' => $exchangeRate,
                'total_amount' => $totalAmount,
                'currency_from' => 'USD',
                'currency_to' => 'USD',
                'payment_method' => $paymentMethod,
                'purpose' => $purpose,
                'notes' => $notes,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ];

            // Insert transfer
            $transferId = $db->insert('transfers', $transferData);
            
            if ($transferId) {
                logMessage('INFO', 'Transfer created', [
                    'transfer_id' => $transferId,
                    'transfer_code' => $transferCode,
                    'user_id' => $userId
                ]);

                echo json_encode([
                    'success' => true,
                    'message' => 'تم إنشاء التحويل بنجاح',
                    'transfer_id' => $transferId,
                    'transfer_code' => $transferCode,
                    'pickup_code' => $pickupCode,
                    'amount' => $amount,
                    'fee' => $fee,
                    'total_amount' => $totalAmount
                ], JSON_UNESCAPED_UNICODE);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في إنشاء التحويل'], JSON_UNESCAPED_UNICODE);
            }
            exit;
        }
        
        if ($_POST['action'] === 'get_countries') {
            $countries = $db->getCountries();

            echo json_encode(['success' => true, 'countries' => $countries], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في الخادم: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء تحويل جديد - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 30px;
        }
        
        .form-container {
            padding: 40px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e5e7eb;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 10px;
        }
        
        .step.active {
            background: #10b981;
            color: white;
        }
        
        .step.completed {
            background: #059669;
            color: white;
        }
        
        .step-line {
            width: 50px;
            height: 2px;
            background: #e5e7eb;
        }
        
        .step-line.completed {
            background: #10b981;
        }
        
        .form-step {
            display: none;
        }
        
        .form-step.active {
            display: block;
        }
        
        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid #e5e7eb;
            padding: 12px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
        }
        
        .btn {
            border-radius: 15px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        
        .summary-card {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .amount-display {
            font-size: 2em;
            font-weight: bold;
            color: #10b981;
            text-align: center;
            margin: 20px 0;
        }
        
        .success-animation {
            text-align: center;
            padding: 40px;
        }
        
        .success-icon {
            font-size: 4em;
            color: #10b981;
            margin-bottom: 20px;
        }
        
        .navbar {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5em;
        }
        
        .alert-fixed {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="index.php">
                <i class="bi bi-bank me-2"></i>
                Elite Transfer
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="index.php">الرئيسية</a>
                <a class="nav-link text-white" href="track-transfer.php">تتبع التحويل</a>
                <?php if (isLoggedIn()): ?>
                    <a class="nav-link text-white" href="dashboard.php">لوحة التحكم</a>
                    <span class="nav-link text-white">مرحباً <?= htmlspecialchars($userName) ?></span>
                <?php else: ?>
                    <a class="nav-link text-white" href="login.php">تسجيل الدخول</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="main-container">
            <!-- Header -->
            <div class="header-section">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-2">
                            <i class="bi bi-plus-circle me-3"></i>
                            إنشاء تحويل جديد
                        </h1>
                        <p class="mb-0 opacity-75">أرسل الأموال بسرعة وأمان إلى أي مكان في العالم</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="text-white">
                            <i class="bi bi-shield-check display-4"></i>
                            <p class="mb-0">آمن ومضمون</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Form Container -->
            <div class="form-container">
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step active" id="step1">1</div>
                    <div class="step-line" id="line1"></div>
                    <div class="step" id="step2">2</div>
                    <div class="step-line" id="line2"></div>
                    <div class="step" id="step3">3</div>
                    <div class="step-line" id="line3"></div>
                    <div class="step" id="step4">4</div>
                </div>

                <!-- Transfer Form -->
                <form id="transferForm">
                    <!-- Step 1: Transfer Details -->
                    <div class="form-step active" id="formStep1">
                        <h4 class="mb-4">
                            <i class="bi bi-info-circle me-2"></i>
                            تفاصيل التحويل
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="senderCountry" class="form-label">
                                    <i class="bi bi-geo-alt me-1"></i>
                                    البلد المرسل
                                </label>
                                <select class="form-select" id="senderCountry" required>
                                    <option value="">اختر البلد</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="receiverCountry" class="form-label">
                                    <i class="bi bi-geo-alt me-1"></i>
                                    البلد المستقبل
                                </label>
                                <select class="form-select" id="receiverCountry" required>
                                    <option value="">اختر البلد</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">
                                    <i class="bi bi-currency-dollar me-1"></i>
                                    المبلغ المرسل
                                </label>
                                <input type="number" class="form-control" id="amount" 
                                       placeholder="أدخل المبلغ" min="1" max="50000" step="0.01" required>
                                <div class="form-text">الحد الأدنى: 1 | الحد الأقصى: 50,000</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="purpose" class="form-label">
                                    <i class="bi bi-tag me-1"></i>
                                    الغرض من التحويل
                                </label>
                                <select class="form-select" id="purpose" required>
                                    <option value="">اختر الغرض</option>
                                    <option value="family_support">دعم الأسرة</option>
                                    <option value="education">تعليم</option>
                                    <option value="medical">علاج طبي</option>
                                    <option value="business">أعمال تجارية</option>
                                    <option value="personal">شخصي</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-primary" onclick="nextStep(1)">
                                التالي
                                <i class="bi bi-arrow-left ms-2"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Sender Information -->
                    <div class="form-step" id="formStep2">
                        <h4 class="mb-4">
                            <i class="bi bi-person me-2"></i>
                            معلومات المرسل
                        </h4>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="senderName" class="form-label">
                                    <i class="bi bi-person me-1"></i>
                                    الاسم الكامل
                                </label>
                                <input type="text" class="form-control" id="senderName"
                                       placeholder="أدخل الاسم الكامل" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="senderPhone" class="form-label">
                                    <i class="bi bi-telephone me-1"></i>
                                    رقم الهاتف
                                </label>
                                <input type="tel" class="form-control" id="senderPhone"
                                       placeholder="+966xxxxxxxxx" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="senderAddress" class="form-label">
                                <i class="bi bi-geo-alt me-1"></i>
                                العنوان الكامل
                            </label>
                            <textarea class="form-control" id="senderAddress" rows="3"
                                      placeholder="أدخل العنوان الكامل" required></textarea>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="prevStep(1)">
                                <i class="bi bi-arrow-right me-2"></i>
                                السابق
                            </button>
                            <button type="button" class="btn btn-primary flex-fill" onclick="nextStep(2)">
                                التالي
                                <i class="bi bi-arrow-left ms-2"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 3: Receiver Information -->
                    <div class="form-step" id="formStep3">
                        <h4 class="mb-4">
                            <i class="bi bi-person-check me-2"></i>
                            معلومات المستقبل
                        </h4>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="receiverName" class="form-label">
                                    <i class="bi bi-person me-1"></i>
                                    اسم المستقبل الكامل
                                </label>
                                <input type="text" class="form-control" id="receiverName"
                                       placeholder="أدخل الاسم الكامل" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="receiverPhone" class="form-label">
                                    <i class="bi bi-telephone me-1"></i>
                                    رقم الهاتف
                                </label>
                                <input type="tel" class="form-control" id="receiverPhone"
                                       placeholder="+20xxxxxxxxx" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="receiverAddress" class="form-label">
                                <i class="bi bi-geo-alt me-1"></i>
                                العنوان الكامل
                            </label>
                            <textarea class="form-control" id="receiverAddress" rows="3"
                                      placeholder="أدخل العنوان الكامل" required></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="pickupMethod" class="form-label">
                                    <i class="bi bi-credit-card me-1"></i>
                                    طريقة الاستلام
                                </label>
                                <select class="form-select" id="pickupMethod" required onchange="toggleBankDetails()">
                                    <option value="">اختر طريقة الاستلام</option>
                                    <option value="cash">استلام نقدي</option>
                                    <option value="bank_deposit">إيداع بنكي</option>
                                    <option value="mobile_wallet">محفظة إلكترونية</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3" id="bankDetailsDiv" style="display: none;">
                                <label for="bankAccount" class="form-label">
                                    <i class="bi bi-bank me-1"></i>
                                    رقم الحساب البنكي
                                </label>
                                <input type="text" class="form-control" id="bankAccount"
                                       placeholder="أدخل رقم الحساب">
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="prevStep(2)">
                                <i class="bi bi-arrow-right me-2"></i>
                                السابق
                            </button>
                            <button type="button" class="btn btn-primary flex-fill" onclick="nextStep(3)">
                                التالي
                                <i class="bi bi-arrow-left ms-2"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 4: Review and Payment -->
                    <div class="form-step" id="formStep4">
                        <h4 class="mb-4">
                            <i class="bi bi-check-circle me-2"></i>
                            مراجعة وتأكيد التحويل
                        </h4>

                        <div class="summary-card" id="reviewSummary">
                            <!-- Summary will be generated here -->
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">
                                    <i class="bi bi-credit-card me-1"></i>
                                    طريقة الدفع
                                </label>
                                <div class="row">
                                    <div class="col-md-4 mb-2">
                                        <div class="card payment-method" onclick="selectPaymentMethod('credit_card')">
                                            <div class="card-body text-center">
                                                <i class="bi bi-credit-card display-6 text-primary"></i>
                                                <p class="mt-2 mb-0">بطاقة ائتمان</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <div class="card payment-method" onclick="selectPaymentMethod('bank_transfer')">
                                            <div class="card-body text-center">
                                                <i class="bi bi-bank display-6 text-success"></i>
                                                <p class="mt-2 mb-0">تحويل بنكي</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <div class="card payment-method" onclick="selectPaymentMethod('cash')">
                                            <div class="card-body text-center">
                                                <i class="bi bi-cash display-6 text-warning"></i>
                                                <p class="mt-2 mb-0">نقداً</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" id="selectedPaymentMethod" required>

                        <div class="form-check mt-3">
                            <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                أوافق على <a href="#" class="text-decoration-none">الشروط والأحكام</a>
                                و <a href="#" class="text-decoration-none">سياسة الخصوصية</a>
                            </label>
                        </div>

                        <div class="d-flex gap-2 mt-4">
                            <button type="button" class="btn btn-outline-secondary" onclick="prevStep(3)">
                                <i class="bi bi-arrow-right me-2"></i>
                                السابق
                            </button>
                            <button type="button" class="btn btn-success flex-fill btn-lg" onclick="submitTransfer()">
                                <i class="bi bi-send me-2"></i>
                                تأكيد وإرسال التحويل
                            </button>
                        </div>
                    </div>

                    <!-- Success Step -->
                    <div class="form-step" id="formStep5">
                        <div class="success-animation">
                            <i class="bi bi-check-circle success-icon"></i>
                            <h3 class="text-success mb-3">تم إنشاء التحويل بنجاح!</h3>
                            <div class="alert alert-success" id="successDetails">
                                <!-- Success details will be shown here -->
                            </div>

                            <div class="d-flex gap-2 justify-content-center mt-4">
                                <a href="track-transfer.php" class="btn btn-outline-primary">
                                    <i class="bi bi-search me-2"></i>
                                    تتبع التحويل
                                </a>
                                <button type="button" class="btn btn-success" onclick="createNewTransfer()">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    تحويل جديد
                                </button>
                                <a href="dashboard.php" class="btn btn-primary">
                                    <i class="bi bi-speedometer2 me-2"></i>
                                    لوحة التحكم
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        let currentStep = 1;
        const totalSteps = 4;

        $(document).ready(function() {
            console.log('🚀 Create Transfer Fixed Page Loaded');
            loadCountries();
        });

        function loadCountries() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_countries' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        const countries = response.countries;
                        let options = '<option value="">اختر البلد</option>';

                        countries.forEach(country => {
                            options += `<option value="${country.id}" data-currency="${country.currency}">${country.name} (${country.currency})</option>`;
                        });

                        $('#senderCountry, #receiverCountry').html(options);
                    }
                },
                error: function() {
                    console.error('فشل في تحميل الدول');
                }
            });
        }

        function nextStep(step) {
            if (!validateCurrentStep()) {
                return;
            }

            if (step < totalSteps) {
                // Hide current step
                $(`#formStep${step}`).removeClass('active');
                $(`#step${step}`).removeClass('active').addClass('completed');

                // Show next step
                const nextStep = step + 1;
                $(`#formStep${nextStep}`).addClass('active');
                $(`#step${nextStep}`).addClass('active');
                $(`#line${step}`).addClass('completed');

                currentStep = nextStep;

                // Generate review summary if moving to step 4
                if (nextStep === 4) {
                    generateReviewSummary();
                }
            }
        }

        function prevStep(step) {
            if (step > 1) {
                // Hide current step
                $(`#formStep${step}`).removeClass('active');
                $(`#step${step}`).removeClass('active completed');

                // Show previous step
                const prevStep = step - 1;
                $(`#formStep${prevStep}`).addClass('active');
                $(`#step${prevStep}`).addClass('active').removeClass('completed');
                $(`#line${prevStep}`).removeClass('completed');

                currentStep = prevStep;
            }
        }

        function validateCurrentStep() {
            const currentForm = document.getElementById(`formStep${currentStep}`);
            const requiredFields = currentForm.querySelectorAll('[required]');

            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    field.focus();
                    showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                    return false;
                }
            }

            // Additional validation for amount
            if (currentStep === 1) {
                const amount = parseFloat($('#amount').val());
                if (amount < 1 || amount > 50000) {
                    showAlert('المبلغ يجب أن يكون بين 1 و 50,000', 'warning');
                    return false;
                }
            }

            return true;
        }

        function toggleBankDetails() {
            const pickupMethod = document.getElementById('pickupMethod').value;
            const bankDetailsDiv = document.getElementById('bankDetailsDiv');

            if (pickupMethod === 'bank_deposit') {
                bankDetailsDiv.style.display = 'block';
                document.getElementById('bankAccount').required = true;
            } else {
                bankDetailsDiv.style.display = 'none';
                document.getElementById('bankAccount').required = false;
            }
        }

        function generateReviewSummary() {
            const amount = parseFloat($('#amount').val());
            const fee = amount * 0.025; // 2.5% fee
            const total = amount + fee;

            const senderCountry = $('#senderCountry option:selected').text();
            const receiverCountry = $('#receiverCountry option:selected').text();
            const purpose = $('#purpose option:selected').text();

            const summary = `
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="bi bi-person me-2"></i>المرسل</h6>
                        <p><strong>الاسم:</strong> ${$('#senderName').val()}</p>
                        <p><strong>الهاتف:</strong> ${$('#senderPhone').val()}</p>
                        <p><strong>البلد:</strong> ${senderCountry}</p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-person-check me-2"></i>المستقبل</h6>
                        <p><strong>الاسم:</strong> ${$('#receiverName').val()}</p>
                        <p><strong>الهاتف:</strong> ${$('#receiverPhone').val()}</p>
                        <p><strong>البلد:</strong> ${receiverCountry}</p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="bi bi-currency-dollar me-2"></i>التفاصيل المالية</h6>
                        <p><strong>المبلغ:</strong> $${amount.toFixed(2)}</p>
                        <p><strong>الرسوم:</strong> $${fee.toFixed(2)}</p>
                        <p><strong>الغرض:</strong> ${purpose}</p>
                    </div>
                    <div class="col-md-6">
                        <div class="amount-display">
                            المجموع: $${total.toFixed(2)}
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('reviewSummary').innerHTML = summary;
        }

        function selectPaymentMethod(method) {
            // Remove active class from all payment methods
            $('.payment-method').removeClass('border-primary');

            // Add active class to selected method
            event.currentTarget.classList.add('border-primary');

            // Set hidden field value
            document.getElementById('selectedPaymentMethod').value = method;
        }

        function submitTransfer() {
            if (!document.getElementById('agreeTerms').checked) {
                showAlert('يرجى الموافقة على الشروط والأحكام', 'warning');
                return;
            }

            if (!document.getElementById('selectedPaymentMethod').value) {
                showAlert('يرجى اختيار طريقة الدفع', 'warning');
                return;
            }

            const formData = {
                action: 'create_transfer',
                sender_name: $('#senderName').val(),
                sender_phone: $('#senderPhone').val(),
                sender_country: $('#senderCountry').val(),
                sender_address: $('#senderAddress').val(),
                receiver_name: $('#receiverName').val(),
                receiver_phone: $('#receiverPhone').val(),
                receiver_country: $('#receiverCountry').val(),
                receiver_address: $('#receiverAddress').val(),
                amount: $('#amount').val(),
                purpose: $('#purpose').val(),
                pickup_method: $('#pickupMethod').val(),
                payment_method: $('#selectedPaymentMethod').val(),
                bank_account: $('#bankAccount').val()
            };

            // Show loading
            const submitBtn = event.target;
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الإرسال...';
            submitBtn.disabled = true;

            $.ajax({
                url: '',
                method: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        // Show success step
                        $('#formStep4').removeClass('active');
                        $('#formStep5').addClass('active');
                        $('#step4').removeClass('active').addClass('completed');

                        // Show success details
                        const successDetails = `
                            <h5>تفاصيل التحويل:</h5>
                            <p><strong>رمز التحويل:</strong> <span class="text-primary">${response.transfer_code}</span></p>
                            <p><strong>رمز الاستلام:</strong> <span class="text-success">${response.pickup_code}</span></p>
                            <p><strong>المبلغ:</strong> $${response.amount}</p>
                            <p><strong>الرسوم:</strong> $${response.fee}</p>
                            <p><strong>المجموع:</strong> $${response.total_amount}</p>
                            <p class="text-muted">احتفظ برمز التحويل ورمز الاستلام للمتابعة</p>
                        `;
                        $('#successDetails').html(successDetails);

                        showAlert('تم إنشاء التحويل بنجاح!', 'success');
                    } else {
                        showAlert(response.message || 'فشل في إنشاء التحويل', 'danger');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', {status, error, response: xhr.responseText});
                    showAlert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'danger');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            });
        }

        function createNewTransfer() {
            location.reload();
        }

        function showAlert(message, type) {
            $('.alert-fixed').remove();

            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show alert-fixed" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);

            setTimeout(function() {
                $('.alert-fixed').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    </script>
</body>
</html>
