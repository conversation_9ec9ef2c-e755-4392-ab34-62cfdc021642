<?php

/**
 * Database Connection Manager
 * Elite Transfer System - Interactive Database Connection Tool
 */

class DatabaseConnectionManager {
    
    private $config = [];
    
    public function __construct() {
        $this->loadEnvironment();
    }
    
    private function loadEnvironment() {
        $envFile = __DIR__ . '/.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value, '"\'');
                }
            }
        }
    }
    
    public function displayMenu() {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "🔗 DATABASE CONNECTION MANAGER\n";
        echo "Elite Transfer System\n";
        echo str_repeat("=", 60) . "\n\n";
        
        echo "📋 Available Options:\n";
        echo "1. Test Current Connection\n";
        echo "2. Test SQLite Connection\n";
        echo "3. Test MySQL Connection\n";
        echo "4. Switch to SQLite\n";
        echo "5. Switch to MySQL\n";
        echo "6. View Current Configuration\n";
        echo "7. Create MySQL Database\n";
        echo "8. Backup Current Database\n";
        echo "9. Import Sample Data\n";
        echo "0. Exit\n\n";
        
        echo "Current DB: " . ($_ENV['DB_CONNECTION'] ?? 'sqlite') . "\n";
        echo "Status: " . $this->getConnectionStatus() . "\n\n";
    }
    
    private function getConnectionStatus() {
        try {
            require_once 'public/includes/database_manager.php';
            $dbManager = DatabaseManager::getInstance();
            $db = $dbManager->getConnection();
            return "🟢 Connected";
        } catch (Exception $e) {
            return "🔴 Disconnected";
        }
    }
    
    public function testCurrentConnection() {
        echo "\n🔄 Testing Current Connection...\n";
        echo str_repeat("-", 40) . "\n";
        
        try {
            require_once 'public/includes/database_manager.php';
            $dbManager = DatabaseManager::getInstance();
            $db = $dbManager->getConnection();
            
            $connectionInfo = $dbManager->getConnectionInfo();
            echo "✅ Connection: SUCCESS\n";
            echo "   Type: {$connectionInfo['type']}\n";
            echo "   Database: {$connectionInfo['database']}\n";
            echo "   Host: {$connectionInfo['host']}\n";
            
            if ($connectionInfo['type'] === 'sqlite') {
                $version = $db->query("SELECT sqlite_version()")->fetchColumn();
                echo "   Version: SQLite $version\n";
            } else {
                $version = $db->query("SELECT VERSION()")->fetchColumn();
                echo "   Version: MySQL $version\n";
            }
            
            // Test basic operations
            $userCount = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
            echo "   Users: $userCount\n";
            
            $countryCount = $db->query("SELECT COUNT(*) FROM countries")->fetchColumn();
            echo "   Countries: $countryCount\n";
            
            echo "\n✅ Connection test passed!\n";
            
        } catch (Exception $e) {
            echo "❌ Connection: FAILED\n";
            echo "   Error: " . $e->getMessage() . "\n";
        }
    }
    
    public function testSQLiteConnection() {
        echo "\n🔄 Testing SQLite Connection...\n";
        echo str_repeat("-", 40) . "\n";
        
        $database = 'database/elite_transfer_production.db';
        $fullPath = __DIR__ . '/' . $database;
        
        if (!file_exists($fullPath)) {
            echo "❌ SQLite database file not found: $database\n";
            echo "💡 Run 'php setup_production_database.php' to create it\n";
            return false;
        }
        
        try {
            $dsn = "sqlite:$fullPath";
            $pdo = new PDO($dsn, null, null, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            
            $version = $pdo->query("SELECT sqlite_version()")->fetchColumn();
            $fileSize = filesize($fullPath);
            
            echo "✅ SQLite Connection: SUCCESS\n";
            echo "   Version: $version\n";
            echo "   File: $database\n";
            echo "   Size: " . number_format($fileSize) . " bytes\n";
            
            // Test tables
            $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
            echo "   Tables: " . count($tables) . "\n";
            
            return true;
            
        } catch (PDOException $e) {
            echo "❌ SQLite Connection: FAILED\n";
            echo "   Error: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    public function testMySQLConnection() {
        echo "\n🔄 Testing MySQL Connection...\n";
        echo str_repeat("-", 40) . "\n";
        
        $host = 'localhost';
        $port = '3306';
        $database = 'elite_transfer';
        $username = 'root';
        $password = '';
        
        try {
            // First test server connection
            $dsn = "mysql:host=$host;port=$port;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            $version = $pdo->query("SELECT VERSION()")->fetchColumn();
            echo "✅ MySQL Server: CONNECTED\n";
            echo "   Version: $version\n";
            echo "   Host: $host:$port\n";
            
            // Test database existence
            $databases = $pdo->query("SHOW DATABASES LIKE '$database'")->fetchAll();
            if (empty($databases)) {
                echo "⚠️  Database '$database' does not exist\n";
                echo "💡 Use option 7 to create it\n";
                return false;
            }
            
            // Connect to specific database
            $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            echo "✅ Database '$database': CONNECTED\n";
            
            // Test tables
            $tables = $pdo->query("SHOW TABLES")->fetchAll();
            echo "   Tables: " . count($tables) . "\n";
            
            return true;
            
        } catch (PDOException $e) {
            echo "❌ MySQL Connection: FAILED\n";
            echo "   Error: " . $e->getMessage() . "\n";
            echo "💡 Make sure MySQL is running in XAMPP\n";
            return false;
        }
    }
    
    public function switchToSQLite() {
        echo "\n🔄 Switching to SQLite...\n";
        echo str_repeat("-", 40) . "\n";
        
        if ($this->testSQLiteConnection()) {
            $this->updateEnvFile([
                'DB_CONNECTION' => 'sqlite',
                'DB_DATABASE' => 'database/elite_transfer_production.db'
            ]);
            echo "✅ Successfully switched to SQLite\n";
        } else {
            echo "❌ Cannot switch to SQLite - database not ready\n";
        }
    }
    
    public function switchToMySQL() {
        echo "\n🔄 Switching to MySQL...\n";
        echo str_repeat("-", 40) . "\n";
        
        if ($this->testMySQLConnection()) {
            $this->updateEnvFile([
                'DB_CONNECTION' => 'mysql',
                'DB_HOST' => 'localhost',
                'DB_PORT' => '3306',
                'DB_DATABASE' => 'elite_transfer',
                'DB_USERNAME' => 'root',
                'DB_PASSWORD' => ''
            ]);
            echo "✅ Successfully switched to MySQL\n";
        } else {
            echo "❌ Cannot switch to MySQL - database not ready\n";
        }
    }
    
    private function updateEnvFile($updates) {
        $envFile = __DIR__ . '/.env';
        $lines = file($envFile, FILE_IGNORE_NEW_LINES);
        
        foreach ($updates as $key => $value) {
            $found = false;
            for ($i = 0; $i < count($lines); $i++) {
                if (strpos($lines[$i], $key . '=') === 0) {
                    $lines[$i] = $key . '=' . $value;
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $lines[] = $key . '=' . $value;
            }
            $_ENV[$key] = $value;
        }
        
        file_put_contents($envFile, implode("\n", $lines));
    }
    
    public function viewConfiguration() {
        echo "\n📊 Current Configuration:\n";
        echo str_repeat("-", 40) . "\n";
        
        $config = [
            'DB_CONNECTION' => $_ENV['DB_CONNECTION'] ?? 'not set',
            'DB_DATABASE' => $_ENV['DB_DATABASE'] ?? 'not set',
            'DB_HOST' => $_ENV['DB_HOST'] ?? 'not set',
            'DB_PORT' => $_ENV['DB_PORT'] ?? 'not set',
            'DB_USERNAME' => $_ENV['DB_USERNAME'] ?? 'not set',
            'APP_ENV' => $_ENV['APP_ENV'] ?? 'not set',
            'APP_DEBUG' => $_ENV['APP_DEBUG'] ?? 'not set'
        ];
        
        foreach ($config as $key => $value) {
            echo sprintf("%-15s: %s\n", $key, $value);
        }
    }
    
    public function createMySQLDatabase() {
        echo "\n🔄 Creating MySQL Database...\n";
        echo str_repeat("-", 40) . "\n";
        
        try {
            $pdo = new PDO("mysql:host=localhost;port=3306", 'root', '', [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            $pdo->exec("CREATE DATABASE IF NOT EXISTS elite_transfer CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "✅ Database 'elite_transfer' created successfully\n";
            
            echo "💡 Now run: php setup_production_database.php\n";
            
        } catch (PDOException $e) {
            echo "❌ Failed to create database: " . $e->getMessage() . "\n";
        }
    }
    
    public function run() {
        while (true) {
            $this->displayMenu();
            echo "Enter your choice (0-9): ";
            $choice = trim(fgets(STDIN));
            
            switch ($choice) {
                case '1':
                    $this->testCurrentConnection();
                    break;
                case '2':
                    $this->testSQLiteConnection();
                    break;
                case '3':
                    $this->testMySQLConnection();
                    break;
                case '4':
                    $this->switchToSQLite();
                    break;
                case '5':
                    $this->switchToMySQL();
                    break;
                case '6':
                    $this->viewConfiguration();
                    break;
                case '7':
                    $this->createMySQLDatabase();
                    break;
                case '8':
                    echo "\n💾 Backup feature coming soon...\n";
                    break;
                case '9':
                    echo "\n📊 Import feature coming soon...\n";
                    break;
                case '0':
                    echo "\n👋 Goodbye!\n";
                    exit(0);
                default:
                    echo "\n❌ Invalid choice. Please try again.\n";
            }
            
            echo "\nPress Enter to continue...";
            fgets(STDIN);
        }
    }
}

// Run the manager if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $manager = new DatabaseConnectionManager();
    $manager->run();
}

?>
