<?php

/**
 * MySQL Database Setup
 * Elite Transfer System - Setup MySQL Database Structure
 */

echo "🐬 Setting up MySQL Database - Elite Transfer System\n";
echo str_repeat("=", 60) . "\n\n";

try {
    // Connect to MySQL
    $host = 'localhost';
    $port = '3306';
    $database = 'elite_transfer';
    $username = 'root';
    $password = '';
    
    echo "📋 Connecting to MySQL...\n";
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $db = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);
    
    echo "✅ Connected to MySQL database: $database\n\n";
    
    // Check and update users table structure
    echo "🔧 Updating users table structure...\n";
    
    // Add missing columns to users table
    $alterQueries = [
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL DEFAULT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP NULL DEFAULT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_login_attempts INT DEFAULT 0",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS account_locked_until TIMESTAMP NULL DEFAULT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified_at TIMESTAMP NULL DEFAULT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_verified_at TIMESTAMP NULL DEFAULT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN DEFAULT FALSE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS two_factor_secret TEXT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_image TEXT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS date_of_birth DATE NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS address TEXT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS city TEXT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS postal_code TEXT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS country_id INT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS identification_number TEXT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS identification_type ENUM('passport', 'national_id', 'driving_license') NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending'",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_verified_at TIMESTAMP NULL DEFAULT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS daily_limit DECIMAL(15,2) DEFAULT 10000.00",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS monthly_limit DECIMAL(15,2) DEFAULT 100000.00",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS total_sent DECIMAL(15,2) DEFAULT 0.00",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS total_received DECIMAL(15,2) DEFAULT 0.00",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS preferred_language VARCHAR(5) DEFAULT 'en'",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) DEFAULT 'UTC'",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS notification_preferences JSON NULL"
    ];
    
    foreach ($alterQueries as $query) {
        try {
            $db->exec($query);
            echo "  ✅ Updated users table structure\n";
        } catch (PDOException $e) {
            // Column might already exist, continue
            if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                echo "  ⚠️  " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Update countries table
    echo "\n🔧 Updating countries table structure...\n";
    $countryAlters = [
        "ALTER TABLE countries ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL DEFAULT NULL",
        "ALTER TABLE countries ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        "ALTER TABLE countries ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ];
    
    foreach ($countryAlters as $query) {
        try {
            $db->exec($query);
            echo "  ✅ Updated countries table structure\n";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                echo "  ⚠️  " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Update transfers table
    echo "\n🔧 Updating transfers table structure...\n";
    $transferAlters = [
        "ALTER TABLE transfers ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL DEFAULT NULL",
        "ALTER TABLE transfers ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        "ALTER TABLE transfers ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ];
    
    foreach ($transferAlters as $query) {
        try {
            $db->exec($query);
            echo "  ✅ Updated transfers table structure\n";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                echo "  ⚠️  " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Create missing tables if they don't exist
    echo "\n📋 Creating missing tables...\n";
    
    // Transfer status history table
    $db->exec("
        CREATE TABLE IF NOT EXISTS transfer_status_histories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            transfer_id INT NOT NULL,
            status ENUM('pending', 'processing', 'completed', 'cancelled', 'failed') NOT NULL,
            notes TEXT,
            changed_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE CASCADE,
            FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "  ✅ Created transfer_status_histories table\n";
    
    // Notifications table
    $db->exec("
        CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at TIMESTAMP NULL DEFAULT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "  ✅ Created notifications table\n";
    
    // Audit logs table
    $db->exec("
        CREATE TABLE IF NOT EXISTS audit_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            table_name VARCHAR(50),
            record_id INT,
            old_values JSON,
            new_values JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "  ✅ Created audit_logs table\n";
    
    // OTP codes table
    $db->exec("
        CREATE TABLE IF NOT EXISTS otp_codes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            code VARCHAR(10) NOT NULL,
            type ENUM('email_verification', 'phone_verification', 'password_reset', 'two_factor') NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            used_at TIMESTAMP NULL DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "  ✅ Created otp_codes table\n";
    
    // Insert sample data if tables are empty
    echo "\n📊 Checking and inserting sample data...\n";
    
    // Check if users table has data
    $userCount = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
    if ($userCount == 0) {
        echo "  📥 Inserting sample users...\n";
        
        $sampleUsers = [
            [
                'user_code' => 'ADMIN001',
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'phone' => '+966501234567',
                'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
                'role' => 'admin',
                'status' => 'active',
                'country_id' => 1
            ],
            [
                'user_code' => 'MGR001',
                'name' => 'Ahmed Al-Rashid',
                'email' => '<EMAIL>',
                'phone' => '+966502345678',
                'password_hash' => password_hash('manager123', PASSWORD_DEFAULT),
                'role' => 'manager',
                'status' => 'active',
                'country_id' => 1
            ],
            [
                'user_code' => 'AGT001',
                'name' => 'Fatima Al-Zahra',
                'email' => '<EMAIL>',
                'phone' => '+966503456789',
                'password_hash' => password_hash('agent123', PASSWORD_DEFAULT),
                'role' => 'agent',
                'status' => 'active',
                'country_id' => 1
            ]
        ];
        
        $stmt = $db->prepare("
            INSERT INTO users (user_code, name, email, phone, password_hash, role, status, country_id, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        foreach ($sampleUsers as $user) {
            $stmt->execute([
                $user['user_code'],
                $user['name'],
                $user['email'],
                $user['phone'],
                $user['password_hash'],
                $user['role'],
                $user['status'],
                $user['country_id']
            ]);
        }
        
        echo "  ✅ Inserted " . count($sampleUsers) . " sample users\n";
    } else {
        echo "  ✅ Users table already has data ($userCount users)\n";
    }
    
    echo "\n🎉 MySQL database setup completed successfully!\n";
    echo str_repeat("=", 60) . "\n";
    
    // Final verification
    echo "\n📊 Database Verification:\n";
    $tables = $db->query("SHOW TABLES")->fetchAll();
    echo "  Tables: " . count($tables) . "\n";
    
    $userCount = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $countryCount = $db->query("SELECT COUNT(*) FROM countries")->fetchColumn();
    
    echo "  Users: $userCount\n";
    echo "  Countries: $countryCount\n";
    
    echo "\n✅ MySQL database is ready for production use!\n";
    
} catch (PDOException $e) {
    echo "❌ Database setup failed!\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "\n🔧 Troubleshooting:\n";
    echo "1. Make sure MySQL is running in XAMPP\n";
    echo "2. Verify database 'elite_transfer' exists\n";
    echo "3. Check MySQL credentials\n";
}

?>
