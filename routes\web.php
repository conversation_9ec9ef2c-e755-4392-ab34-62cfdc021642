<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\TransferController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\AgentController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// Public routes
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Authentication routes
Route::prefix('auth')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/verify-otp', [AuthController::class, 'verifyOtp'])->name('verify.otp');
    
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
    
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
});

// Public transfer tracking
Route::get('/track', function () {
    return view('transfers.track');
})->name('track.form');

Route::post('/track', [TransferController::class, 'track'])->name('track.transfer');

// Protected routes
Route::middleware(['auth'])->group(function () {
    
    // Dashboard routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Transfer routes
    Route::prefix('transfers')->group(function () {
        Route::get('/create', [TransferController::class, 'create'])->name('transfers.create');
        Route::post('/calculate-fees', [TransferController::class, 'calculateFees'])->name('transfers.calculate-fees');
        Route::post('/store', [TransferController::class, 'store'])->name('transfers.store');
        Route::get('/my-transfers', [TransferController::class, 'myTransfers'])->name('transfers.my');
        Route::get('/{transfer}', [TransferController::class, 'show'])->name('transfers.show');
        Route::post('/{transfer}/cancel', [TransferController::class, 'cancel'])->name('transfers.cancel');
    });
    
    // Profile routes
    Route::prefix('profile')->group(function () {
        Route::get('/', [ProfileController::class, 'show'])->name('profile.show');
        Route::put('/', [ProfileController::class, 'update'])->name('profile.update');
        Route::post('/upload-photo', [ProfileController::class, 'uploadPhoto'])->name('profile.upload-photo');
        Route::post('/enable-2fa', [ProfileController::class, 'enable2FA'])->name('profile.enable-2fa');
        Route::post('/disable-2fa', [ProfileController::class, 'disable2FA'])->name('profile.disable-2fa');
    });
});

// Admin routes
Route::middleware(['auth', 'role:admin,super_admin'])->prefix('admin')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('admin.dashboard');
    
    // User management
    Route::prefix('users')->group(function () {
        Route::get('/', [AdminController::class, 'users'])->name('admin.users');
        Route::get('/create', [AdminController::class, 'createUser'])->name('admin.users.create');
        Route::post('/store', [AdminController::class, 'storeUser'])->name('admin.users.store');
        Route::get('/{user}/edit', [AdminController::class, 'editUser'])->name('admin.users.edit');
        Route::put('/{user}', [AdminController::class, 'updateUser'])->name('admin.users.update');
        Route::delete('/{user}', [AdminController::class, 'deleteUser'])->name('admin.users.delete');
        Route::post('/{user}/toggle-status', [AdminController::class, 'toggleUserStatus'])->name('admin.users.toggle-status');
    });
    
    // Transfer management
    Route::prefix('transfers')->group(function () {
        Route::get('/', [AdminController::class, 'transfers'])->name('admin.transfers');
        Route::get('/{transfer}', [AdminController::class, 'showTransfer'])->name('admin.transfers.show');
        Route::post('/{transfer}/update-status', [AdminController::class, 'updateTransferStatus'])->name('admin.transfers.update-status');
        Route::post('/{transfer}/assign-agent', [AdminController::class, 'assignAgent'])->name('admin.transfers.assign-agent');
    });
    
    // Branch management
    Route::prefix('branches')->group(function () {
        Route::get('/', [AdminController::class, 'branches'])->name('admin.branches');
        Route::get('/create', [AdminController::class, 'createBranch'])->name('admin.branches.create');
        Route::post('/store', [AdminController::class, 'storeBranch'])->name('admin.branches.store');
        Route::get('/{branch}/edit', [AdminController::class, 'editBranch'])->name('admin.branches.edit');
        Route::put('/{branch}', [AdminController::class, 'updateBranch'])->name('admin.branches.update');
    });
    
    // Reports
    Route::prefix('reports')->group(function () {
        Route::get('/transfers', [AdminController::class, 'transferReports'])->name('admin.reports.transfers');
        Route::get('/financial', [AdminController::class, 'financialReports'])->name('admin.reports.financial');
        Route::get('/agents', [AdminController::class, 'agentReports'])->name('admin.reports.agents');
        Route::get('/suspicious', [AdminController::class, 'suspiciousReports'])->name('admin.reports.suspicious');
    });
    
    // System settings
    Route::prefix('settings')->group(function () {
        Route::get('/', [AdminController::class, 'settings'])->name('admin.settings');
        Route::put('/update', [AdminController::class, 'updateSettings'])->name('admin.settings.update');
        Route::get('/currencies', [AdminController::class, 'currencies'])->name('admin.settings.currencies');
        Route::get('/countries', [AdminController::class, 'countries'])->name('admin.settings.countries');
        Route::get('/fees', [AdminController::class, 'fees'])->name('admin.settings.fees');
    });
    
    // Audit logs
    Route::get('/audit-logs', [AdminController::class, 'auditLogs'])->name('admin.audit-logs');
});

// Agent routes
Route::middleware(['auth', 'role:agent'])->prefix('agent')->group(function () {
    Route::get('/dashboard', [AgentController::class, 'dashboard'])->name('agent.dashboard');
    
    // Transfer management for agents
    Route::prefix('transfers')->group(function () {
        Route::get('/', [AgentController::class, 'transfers'])->name('agent.transfers');
        Route::get('/pending', [AgentController::class, 'pendingTransfers'])->name('agent.transfers.pending');
        Route::get('/ready-for-pickup', [AgentController::class, 'readyForPickup'])->name('agent.transfers.ready-pickup');
        Route::get('/{transfer}', [AgentController::class, 'showTransfer'])->name('agent.transfers.show');
        Route::post('/{transfer}/process', [AgentController::class, 'processTransfer'])->name('agent.transfers.process');
        Route::post('/{transfer}/complete', [AgentController::class, 'completeTransfer'])->name('agent.transfers.complete');
        Route::post('/{transfer}/verify-pickup', [AgentController::class, 'verifyPickup'])->name('agent.transfers.verify-pickup');
    });
    
    // Customer management
    Route::prefix('customers')->group(function () {
        Route::get('/', [AgentController::class, 'customers'])->name('agent.customers');
        Route::get('/create', [AgentController::class, 'createCustomer'])->name('agent.customers.create');
        Route::post('/store', [AgentController::class, 'storeCustomer'])->name('agent.customers.store');
        Route::get('/{customer}', [AgentController::class, 'showCustomer'])->name('agent.customers.show');
    });
    
    // Reports for agents
    Route::prefix('reports')->group(function () {
        Route::get('/daily', [AgentController::class, 'dailyReport'])->name('agent.reports.daily');
        Route::get('/monthly', [AgentController::class, 'monthlyReport'])->name('agent.reports.monthly');
        Route::get('/commissions', [AgentController::class, 'commissionReport'])->name('agent.reports.commissions');
    });
});

// API routes for AJAX calls
Route::prefix('api')->middleware(['auth'])->group(function () {
    Route::get('/countries', function () {
        return response()->json(App\Models\Country::where('is_active', true)->get());
    });
    
    Route::get('/currencies', function () {
        return response()->json(App\Models\Currency::where('is_active', true)->get());
    });
    
    Route::get('/branches/{country}', function (App\Models\Country $country) {
        return response()->json($country->branches()->where('is_active', true)->get());
    });
    
    Route::get('/exchange-rate/{from}/{to}', function (App\Models\Currency $from, App\Models\Currency $to) {
        return response()->json([
            'rate' => $from->getExchangeRateTo($to),
            'from' => $from->code,
            'to' => $to->code,
        ]);
    });
});
