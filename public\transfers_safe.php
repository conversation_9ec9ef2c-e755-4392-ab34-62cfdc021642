<?php

/**
 * Safe Transfers Management
 * Elite Transfer System - Fixed and Working Version
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $db = DatabaseManager::getInstance();
        
        switch ($_POST['action']) {
            case 'get_transfers':
                $page = intval($_POST['page'] ?? 1);
                $limit = intval($_POST['limit'] ?? 25);
                $search = $_POST['search'] ?? '';
                $status = $_POST['status'] ?? '';
                $dateFrom = $_POST['date_from'] ?? '';
                $dateTo = $_POST['date_to'] ?? '';
                
                // Build query with filters
                $whereConditions = ["(deleted_at IS NULL OR deleted_at = '')"];
                $params = [];
                
                if (!empty($search)) {
                    $whereConditions[] = "(transfer_code LIKE :search OR pickup_code LIKE :search OR sender_name LIKE :search OR recipient_name LIKE :search)";
                    $params['search'] = "%$search%";
                }
                
                if (!empty($status)) {
                    $whereConditions[] = "status = :status";
                    $params['status'] = $status;
                }
                
                if (!empty($dateFrom)) {
                    $whereConditions[] = "DATE(created_at) >= :date_from";
                    $params['date_from'] = $dateFrom;
                }
                
                if (!empty($dateTo)) {
                    $whereConditions[] = "DATE(created_at) <= :date_to";
                    $params['date_to'] = $dateTo;
                }
                
                $whereClause = implode(' AND ', $whereConditions);
                $offset = ($page - 1) * $limit;
                
                // Get transfers
                $transfers = $db->select("
                    SELECT * FROM transfers 
                    WHERE $whereClause 
                    ORDER BY created_at DESC 
                    LIMIT $limit OFFSET $offset
                ", $params);
                
                // Get total count
                $totalResult = $db->selectOne("
                    SELECT COUNT(*) as count FROM transfers 
                    WHERE $whereClause
                ", $params);
                $total = $totalResult['count'] ?? 0;
                
                echo json_encode([
                    'success' => true,
                    'transfers' => $transfers,
                    'total' => intval($total),
                    'page' => $page,
                    'pages' => ceil($total / $limit)
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'approve_transfer':
                $transferId = intval($_POST['transfer_id']);
                
                $updated = $db->update('transfers', 
                    [
                        'status' => 'processing',
                        'approved_by' => getUserId(),
                        'approved_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 
                    'id = :id', 
                    ['id' => $transferId]
                );
                
                if ($updated) {
                    logMessage('INFO', 'Transfer approved', [
                        'transfer_id' => $transferId,
                        'user_id' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true, 
                        'message' => 'تم الموافقة على التحويل بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode([
                        'success' => false, 
                        'message' => 'فشل في الموافقة على التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            case 'complete_transfer':
                $transferId = intval($_POST['transfer_id']);
                
                $updated = $db->update('transfers', 
                    [
                        'status' => 'completed',
                        'completed_by' => getUserId(),
                        'completed_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 
                    'id = :id', 
                    ['id' => $transferId]
                );
                
                if ($updated) {
                    logMessage('INFO', 'Transfer completed', [
                        'transfer_id' => $transferId,
                        'user_id' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true, 
                        'message' => 'تم إكمال التحويل بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode([
                        'success' => false, 
                        'message' => 'فشل في إكمال التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            case 'reject_transfer':
                $transferId = intval($_POST['transfer_id']);
                $reason = $_POST['reason'] ?? 'لم يتم تحديد السبب';
                
                $updated = $db->update('transfers', 
                    [
                        'status' => 'cancelled',
                        'notes' => $reason,
                        'cancelled_by' => getUserId(),
                        'cancelled_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 
                    'id = :id', 
                    ['id' => $transferId]
                );
                
                if ($updated) {
                    logMessage('INFO', 'Transfer rejected', [
                        'transfer_id' => $transferId,
                        'reason' => $reason,
                        'user_id' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true, 
                        'message' => 'تم رفض التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode([
                        'success' => false, 
                        'message' => 'فشل في رفض التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            case 'delete_transfer':
                $transferId = intval($_POST['transfer_id']);
                
                $updated = $db->update('transfers', 
                    ['deleted_at' => date('Y-m-d H:i:s')], 
                    'id = :id', 
                    ['id' => $transferId]
                );
                
                if ($updated) {
                    logMessage('INFO', 'Transfer deleted', [
                        'transfer_id' => $transferId,
                        'user_id' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true, 
                        'message' => 'تم حذف التحويل بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode([
                        'success' => false, 
                        'message' => 'فشل في حذف التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            case 'bulk_action':
                $action = $_POST['bulk_action'];
                $transferIds = json_decode($_POST['transfer_ids'], true);
                
                if (empty($transferIds)) {
                    echo json_encode([
                        'success' => false,
                        'message' => 'لم يتم تحديد أي تحويلات'
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                }
                
                $successCount = 0;
                $totalCount = count($transferIds);
                
                foreach ($transferIds as $transferId) {
                    $updateData = ['updated_at' => date('Y-m-d H:i:s')];
                    
                    switch ($action) {
                        case 'approve':
                            $updateData['status'] = 'processing';
                            $updateData['approved_by'] = getUserId();
                            $updateData['approved_at'] = date('Y-m-d H:i:s');
                            break;
                        case 'complete':
                            $updateData['status'] = 'completed';
                            $updateData['completed_by'] = getUserId();
                            $updateData['completed_at'] = date('Y-m-d H:i:s');
                            break;
                        case 'cancel':
                            $updateData['status'] = 'cancelled';
                            $updateData['cancelled_by'] = getUserId();
                            $updateData['cancelled_at'] = date('Y-m-d H:i:s');
                            break;
                        case 'delete':
                            $updateData = ['deleted_at' => date('Y-m-d H:i:s')];
                            break;
                    }
                    
                    if ($db->update('transfers', $updateData, 'id = :id', ['id' => $transferId])) {
                        $successCount++;
                    }
                }
                
                logMessage('INFO', 'Bulk action performed', [
                    'action' => $action,
                    'total_count' => $totalCount,
                    'success_count' => $successCount,
                    'user_id' => getUserId()
                ]);
                
                echo json_encode([
                    'success' => true,
                    'message' => "تم تنفيذ العملية على $successCount من أصل $totalCount تحويل"
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode([
                    'success' => false, 
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
        
    } catch (Exception $e) {
        logMessage('ERROR', 'Transfers management error', ['error' => $e->getMessage()]);
        echo json_encode([
            'success' => false, 
            'message' => 'خطأ في الخادم: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    
    exit;
}

// Get initial data
try {
    $db = DatabaseManager::getInstance();
    $stats = $db->getStatistics();
    
    // Get recent transfers
    $recentTransfers = $db->select("
        SELECT * FROM transfers 
        WHERE (deleted_at IS NULL OR deleted_at = '') 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
} catch (Exception $e) {
    logMessage('ERROR', 'Failed to load transfers data', ['error' => $e->getMessage()]);
    $stats = [
        'total_transfers' => 0,
        'pending_transfers' => 0,
        'completed_transfers' => 0,
        'total_amount' => 0
    ];
    $recentTransfers = [];
}

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';

// Helper function to get status label
function getStatusLabel($status) {
    $labels = [
        'pending' => 'في الانتظار',
        'processing' => 'قيد المعالجة',
        'completed' => 'مكتمل',
        'cancelled' => 'ملغي',
        'failed' => 'فاشل'
    ];
    return $labels[$status] ?? $status;
}

// Helper function to format date
function formatDate($date, $format = 'Y-m-d H:i') {
    return date($format, strtotime($date));
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التحويلات - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
        }
        
        .page-header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 30px 0;
            margin-bottom: 30px;
        }
        
        .page-title {
            color: white;
            font-size: 2.5rem;
            font-weight: 800;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .page-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
            margin: 0;
        }
        
        .main-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 25px;
            overflow: hidden;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid var(--glass-border);
            padding: 25px 30px;
        }
        
        .card-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            color: white;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .filters-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 12px 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.95);
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
            transform: translateY(-2px);
        }
        
        .btn {
            border-radius: 12px;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary { background: var(--primary-gradient); }
        .btn-success { background: var(--success-gradient); }
        .btn-warning { background: var(--warning-gradient); color: #8b4513; }
        .btn-danger { background: var(--danger-gradient); color: #8b0000; }
        .btn-info { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
        
        .table-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .table {
            margin: 0;
            font-size: 0.95rem;
        }
        
        .table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            font-weight: 700;
            color: #495057;
            padding: 18px 15px;
        }
        
        .table td {
            border: none;
            padding: 15px;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: middle;
        }
        
        .table tbody tr:hover {
            background: rgba(79, 172, 254, 0.05);
        }
        
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .status-pending {
            background: rgba(255, 193, 7, 0.2);
            color: #ff8f00;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }
        
        .status-processing {
            background: rgba(13, 110, 253, 0.2);
            color: #0d6efd;
            border: 1px solid rgba(13, 110, 253, 0.3);
        }
        
        .status-completed {
            background: rgba(25, 135, 84, 0.2);
            color: #198754;
            border: 1px solid rgba(25, 135, 84, 0.3);
        }
        
        .status-cancelled {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }
        
        .status-failed {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
            border-radius: 8px;
        }
        
        .transfer-code {
            font-family: 'Courier New', monospace;
            font-weight: 700;
            color: #495057;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.9rem;
        }
        
        .amount-display {
            font-weight: 700;
            font-size: 1.1rem;
            color: #198754;
        }
        
        .bulk-actions {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            display: none;
        }
        
        .bulk-actions.show {
            display: block;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn-sm {
                width: 100%;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center text-white">
            <div class="loading-spinner mb-3"></div>
            <h5>جاري التحميل...</h5>
        </div>
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="bi bi-arrow-left-right me-3"></i>
                        إدارة التحويلات
                    </h1>
                    <p class="page-subtitle">
                        إدارة شاملة لجميع التحويلات المالية مع جميع الوظائف
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-success btn-lg" onclick="createTransfer()">
                        <i class="bi bi-plus-circle me-2"></i>
                        تحويل جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalTransfers"><?= number_format($stats['total_transfers'] ?? 0) ?></div>
                <div class="stat-label">إجمالي التحويلات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingTransfers"><?= number_format($stats['pending_transfers'] ?? 0) ?></div>
                <div class="stat-label">في الانتظار</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="completedTransfers"><?= number_format($stats['completed_transfers'] ?? 0) ?></div>
                <div class="stat-label">مكتملة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalAmount">$<?= number_format($stats['total_amount'] ?? 0, 2) ?></div>
                <div class="stat-label">إجمالي المبلغ</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="main-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-funnel"></i>
                    البحث والفلترة
                </h3>
            </div>
            <div class="card-body">
                <div class="filters-section">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label text-white">البحث</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="رمز التحويل، اسم المرسل...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-white">الحالة</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="pending">في الانتظار</option>
                                <option value="processing">قيد المعالجة</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                                <option value="failed">فاشل</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-white">من تاريخ</label>
                            <input type="date" class="form-control" id="dateFromFilter">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-white">إلى تاريخ</label>
                            <input type="date" class="form-control" id="dateToFilter">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label text-white">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary flex-fill" onclick="loadTransfers()">
                                    <i class="bi bi-search me-1"></i>
                                    بحث
                                </button>
                                <button class="btn btn-secondary" onclick="clearFilters()">
                                    <i class="bi bi-x-circle me-1"></i>
                                    مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div class="bulk-actions" id="bulkActions">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <span class="text-white">
                        <i class="bi bi-check-square me-2"></i>
                        تم تحديد <span id="selectedCount">0</span> تحويل
                    </span>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group">
                        <button class="btn btn-success btn-sm" onclick="bulkAction('approve')">
                            <i class="bi bi-check me-1"></i>
                            موافقة
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="bulkAction('complete')">
                            <i class="bi bi-check-circle me-1"></i>
                            إكمال
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="bulkAction('cancel')">
                            <i class="bi bi-x me-1"></i>
                            إلغاء
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="bulkAction('delete')">
                            <i class="bi bi-trash me-1"></i>
                            حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfers Table -->
        <div class="main-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-table"></i>
                    قائمة التحويلات
                    <span class="badge bg-primary ms-2" id="transfersCount">0</span>
                </h3>
                <div class="d-flex gap-2">
                    <button class="btn btn-info btn-sm" onclick="loadTransfers()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        تحديث
                    </button>
                    <button class="btn btn-success btn-sm" onclick="exportData()">
                        <i class="bi bi-download me-1"></i>
                        تصدير
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="printTable()">
                        <i class="bi bi-printer me-1"></i>
                        طباعة
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-hover" id="transfersTable">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                </th>
                                <th>رمز التحويل</th>
                                <th>المرسل</th>
                                <th>المستلم</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th width="250">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transfersTableBody">
                            <?php foreach ($recentTransfers as $transfer): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input transfer-checkbox" value="<?= $transfer['id'] ?>">
                                </td>
                                <td>
                                    <div class="transfer-code"><?= htmlspecialchars($transfer['transfer_code']) ?></div>
                                    <small class="text-muted"><?= htmlspecialchars($transfer['pickup_code']) ?></small>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($transfer['sender_name']) ?></strong>
                                    <br>
                                    <small class="text-muted"><?= htmlspecialchars($transfer['sender_phone'] ?? '') ?></small>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($transfer['recipient_name']) ?></strong>
                                    <br>
                                    <small class="text-muted"><?= htmlspecialchars($transfer['recipient_phone'] ?? '') ?></small>
                                </td>
                                <td>
                                    <span class="amount-display">$<?= number_format($transfer['amount'], 2) ?></span>
                                </td>
                                <td>
                                    <span class="status-badge status-<?= $transfer['status'] ?>">
                                        <?= getStatusLabel($transfer['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <div><?= formatDate($transfer['created_at'], 'Y-m-d') ?></div>
                                    <small class="text-muted"><?= formatDate($transfer['created_at'], 'H:i') ?></small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <?php if ($transfer['status'] === 'pending'): ?>
                                            <button class="btn btn-success btn-sm" onclick="approveTransfer(<?= $transfer['id'] ?>)" title="موافقة">
                                                <i class="bi bi-check"></i>
                                            </button>
                                            <button class="btn btn-warning btn-sm" onclick="rejectTransfer(<?= $transfer['id'] ?>)" title="رفض">
                                                <i class="bi bi-x"></i>
                                            </button>
                                        <?php elseif ($transfer['status'] === 'processing'): ?>
                                            <button class="btn btn-primary btn-sm" onclick="completeTransfer(<?= $transfer['id'] ?>)" title="إكمال">
                                                <i class="bi bi-check-circle"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button class="btn btn-info btn-sm" onclick="printTransfer(<?= $transfer['id'] ?>)" title="طباعة">
                                            <i class="bi bi-printer"></i>
                                        </button>
                                        <button class="btn btn-secondary btn-sm" onclick="viewTransfer(<?= $transfer['id'] ?>)" title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" onclick="editTransfer(<?= $transfer['id'] ?>)" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteTransfer(<?= $transfer['id'] ?>)" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <nav aria-label="صفحات التحويلات">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- Pagination will be generated here -->
            </ul>
        </nav>

        <!-- Back to Dashboard -->
        <div class="text-center mt-4">
            <a href="dashboard.php" class="btn btn-outline-light btn-lg">
                <i class="bi bi-arrow-right me-2"></i>
                العودة إلى لوحة التحكم
            </a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        let currentPage = 1;
        let currentLimit = 25;
        let selectedTransfers = [];

        $(document).ready(function() {
            console.log('🚀 Safe Transfers Management Loaded - Elite Transfer System v<?= SYSTEM_VERSION ?>');

            // Set default date filters
            const today = new Date();
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

            $('#dateFromFilter').val(lastMonth.toISOString().split('T')[0]);
            $('#dateToFilter').val(today.toISOString().split('T')[0]);

            // Search on enter
            $('#searchInput').on('keypress', function(e) {
                if (e.which === 13) {
                    loadTransfers();
                }
            });

            // Select all checkbox
            $('#selectAll').change(function() {
                const isChecked = $(this).is(':checked');
                $('.transfer-checkbox').prop('checked', isChecked);
                updateSelectedTransfers();
            });

            // Update selected transfers when individual checkboxes change
            $(document).on('change', '.transfer-checkbox', function() {
                updateSelectedTransfers();
            });
        });

        function loadTransfers(page = 1) {
            currentPage = page;
            showLoading(true);

            const searchData = {
                action: 'get_transfers',
                page: page,
                limit: currentLimit,
                search: $('#searchInput').val(),
                status: $('#statusFilter').val(),
                date_from: $('#dateFromFilter').val(),
                date_to: $('#dateToFilter').val()
            };

            $.ajax({
                url: '',
                method: 'POST',
                data: searchData,
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        displayTransfers(response.transfers);
                        updatePagination(response.page, response.pages, response.total);
                        $('#transfersCount').text(response.total);
                    } else {
                        showError('فشل في تحميل البيانات');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        function displayTransfers(transfers) {
            const tbody = $('#transfersTableBody');
            tbody.empty();

            if (transfers.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="8" class="text-center py-5">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-3">لا توجد تحويلات</p>
                        </td>
                    </tr>
                `);
                return;
            }

            transfers.forEach(transfer => {
                const statusLabels = {
                    'pending': 'في الانتظار',
                    'processing': 'قيد المعالجة',
                    'completed': 'مكتمل',
                    'cancelled': 'ملغي',
                    'failed': 'فاشل'
                };

                let actions = '<div class="action-buttons">';

                if (transfer.status === 'pending') {
                    actions += `<button class="btn btn-success btn-sm" onclick="approveTransfer(${transfer.id})" title="موافقة">
                        <i class="bi bi-check"></i>
                    </button>`;
                    actions += `<button class="btn btn-warning btn-sm" onclick="rejectTransfer(${transfer.id})" title="رفض">
                        <i class="bi bi-x"></i>
                    </button>`;
                } else if (transfer.status === 'processing') {
                    actions += `<button class="btn btn-primary btn-sm" onclick="completeTransfer(${transfer.id})" title="إكمال">
                        <i class="bi bi-check-circle"></i>
                    </button>`;
                }

                actions += `<button class="btn btn-info btn-sm" onclick="printTransfer(${transfer.id})" title="طباعة">
                    <i class="bi bi-printer"></i>
                </button>`;
                actions += `<button class="btn btn-secondary btn-sm" onclick="viewTransfer(${transfer.id})" title="عرض">
                    <i class="bi bi-eye"></i>
                </button>`;
                actions += `<button class="btn btn-outline-primary btn-sm" onclick="editTransfer(${transfer.id})" title="تعديل">
                    <i class="bi bi-pencil"></i>
                </button>`;
                actions += `<button class="btn btn-danger btn-sm" onclick="deleteTransfer(${transfer.id})" title="حذف">
                    <i class="bi bi-trash"></i>
                </button>`;
                actions += '</div>';

                const row = `
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input transfer-checkbox" value="${transfer.id}">
                        </td>
                        <td>
                            <div class="transfer-code">${transfer.transfer_code}</div>
                            <small class="text-muted">${transfer.pickup_code}</small>
                        </td>
                        <td>
                            <strong>${transfer.sender_name}</strong>
                            <br>
                            <small class="text-muted">${transfer.sender_phone || ''}</small>
                        </td>
                        <td>
                            <strong>${transfer.recipient_name}</strong>
                            <br>
                            <small class="text-muted">${transfer.recipient_phone || ''}</small>
                        </td>
                        <td>
                            <span class="amount-display">$${new Intl.NumberFormat('ar-SA', {minimumFractionDigits: 2}).format(transfer.amount)}</span>
                        </td>
                        <td>
                            <span class="status-badge status-${transfer.status}">
                                ${statusLabels[transfer.status] || transfer.status}
                            </span>
                        </td>
                        <td>
                            <div>${new Date(transfer.created_at).toLocaleDateString('ar-SA')}</div>
                            <small class="text-muted">${new Date(transfer.created_at).toLocaleTimeString('ar-SA')}</small>
                        </td>
                        <td>${actions}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        function updatePagination(currentPage, totalPages, totalItems) {
            const pagination = $('#pagination');
            pagination.empty();

            if (totalPages <= 1) return;

            // Previous button
            if (currentPage > 1) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadTransfers(${currentPage - 1})">السابق</a>
                    </li>
                `);
            }

            // Page numbers
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const active = i === currentPage ? 'active' : '';
                pagination.append(`
                    <li class="page-item ${active}">
                        <a class="page-link" href="#" onclick="loadTransfers(${i})">${i}</a>
                    </li>
                `);
            }

            // Next button
            if (currentPage < totalPages) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadTransfers(${currentPage + 1})">التالي</a>
                    </li>
                `);
            }
        }

        function updateSelectedTransfers() {
            selectedTransfers = [];
            $('.transfer-checkbox:checked').each(function() {
                selectedTransfers.push($(this).val());
            });

            const count = selectedTransfers.length;
            $('#selectedCount').text(count);

            if (count > 0) {
                $('#bulkActions').addClass('show');
            } else {
                $('#bulkActions').removeClass('show');
            }

            // Update select all checkbox
            const totalCheckboxes = $('.transfer-checkbox').length;
            const checkedCheckboxes = $('.transfer-checkbox:checked').length;

            if (checkedCheckboxes === 0) {
                $('#selectAll').prop('indeterminate', false).prop('checked', false);
            } else if (checkedCheckboxes === totalCheckboxes) {
                $('#selectAll').prop('indeterminate', false).prop('checked', true);
            } else {
                $('#selectAll').prop('indeterminate', true);
            }
        }

        function clearFilters() {
            $('#searchInput').val('');
            $('#statusFilter').val('');
            $('#dateFromFilter').val('');
            $('#dateToFilter').val('');
            loadTransfers(1);
        }

        // Transfer Actions
        function approveTransfer(transferId) {
            Swal.fire({
                title: 'تأكيد الموافقة',
                text: 'هل أنت متأكد من الموافقة على هذا التحويل؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، موافق',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#28a745'
            }).then((result) => {
                if (result.isConfirmed) {
                    performTransferAction('approve_transfer', transferId);
                }
            });
        }

        function completeTransfer(transferId) {
            Swal.fire({
                title: 'إكمال التحويل',
                text: 'هل أنت متأكد من إكمال هذا التحويل؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، إكمال',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#007bff'
            }).then((result) => {
                if (result.isConfirmed) {
                    performTransferAction('complete_transfer', transferId);
                }
            });
        }

        function rejectTransfer(transferId) {
            Swal.fire({
                title: 'رفض التحويل',
                input: 'textarea',
                inputLabel: 'سبب الرفض',
                inputPlaceholder: 'اكتب سبب رفض التحويل...',
                showCancelButton: true,
                confirmButtonText: 'رفض',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545',
                inputValidator: (value) => {
                    if (!value) {
                        return 'يرجى كتابة سبب الرفض';
                    }
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    performTransferAction('reject_transfer', transferId, { reason: result.value });
                }
            });
        }

        function deleteTransfer(transferId) {
            Swal.fire({
                title: 'حذف التحويل',
                text: 'هل أنت متأكد من حذف هذا التحويل؟ لا يمكن التراجع عن هذا الإجراء.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545'
            }).then((result) => {
                if (result.isConfirmed) {
                    performTransferAction('delete_transfer', transferId);
                }
            });
        }

        function performTransferAction(action, transferId, extraData = {}) {
            const data = {
                action: action,
                transfer_id: transferId,
                ...extraData
            };

            $.ajax({
                url: '',
                method: 'POST',
                data: data,
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        showSuccess(response.message);
                        loadTransfers(currentPage);
                    } else {
                        showError(response.message || 'فشل في تنفيذ العملية');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                }
            });
        }

        function bulkAction(action) {
            if (selectedTransfers.length === 0) {
                showWarning('يرجى تحديد تحويلات أولاً');
                return;
            }

            const actionLabels = {
                'approve': 'الموافقة على',
                'complete': 'إكمال',
                'cancel': 'إلغاء',
                'delete': 'حذف'
            };

            Swal.fire({
                title: `${actionLabels[action]} التحويلات المحددة`,
                text: `هل أنت متأكد من ${actionLabels[action]} ${selectedTransfers.length} تحويل؟`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، تنفيذ',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: action === 'delete' ? '#dc3545' : '#28a745'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '',
                        method: 'POST',
                        data: {
                            action: 'bulk_action',
                            bulk_action: action,
                            transfer_ids: JSON.stringify(selectedTransfers)
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response && response.success) {
                                showSuccess(response.message);
                                selectedTransfers = [];
                                $('#bulkActions').removeClass('show');
                                loadTransfers(currentPage);
                            } else {
                                showError(response.message || 'فشل في تنفيذ العملية');
                            }
                        },
                        error: function() {
                            showError('خطأ في الاتصال بالخادم');
                        }
                    });
                }
            });
        }

        function viewTransfer(transferId) {
            window.open(`view_transfer.php?id=${transferId}`, '_blank');
        }

        function editTransfer(transferId) {
            window.location.href = `edit_transfer.php?id=${transferId}`;
        }

        function printTransfer(transferId) {
            window.open(`print_transfer.php?id=${transferId}`, '_blank');
        }

        function createTransfer() {
            window.location.href = 'create_transfer_fixed.php';
        }

        function exportData() {
            const filters = {
                search: $('#searchInput').val(),
                status: $('#statusFilter').val(),
                date_from: $('#dateFromFilter').val(),
                date_to: $('#dateToFilter').val()
            };

            const params = new URLSearchParams(filters).toString();
            window.open(`export_transfers.php?${params}`, '_blank');
        }

        function printTable() {
            window.print();
        }

        function showLoading(show) {
            if (show) {
                $('#loadingOverlay').show();
            } else {
                $('#loadingOverlay').hide();
            }
        }

        function showSuccess(message) {
            Swal.fire({
                icon: 'success',
                title: 'نجح!',
                text: message,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: message,
                confirmButtonText: 'حسناً'
            });
        }

        function showWarning(message) {
            Swal.fire({
                icon: 'warning',
                title: 'تنبيه!',
                text: message,
                confirmButtonText: 'حسناً'
            });
        }
    </script>
</body>
</html>
