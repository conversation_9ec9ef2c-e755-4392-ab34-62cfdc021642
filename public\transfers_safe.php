<?php

/**
 * Transfers Management - Safe Version
 * Elite Transfer System - Safe version that handles missing columns gracefully
 */

// Disable error output to prevent JSON corruption
error_reporting(0);
ini_set('display_errors', 0);

// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Auto login if not logged in
if (!isLoggedIn()) {
    require_once __DIR__ . '/includes/database_manager.php';
    try {
        $dbManager = DatabaseManager::getInstance();
        $db = $dbManager->getConnection();
        
        $stmt = $db->prepare("SELECT * FROM users WHERE role = 'admin' AND status = 'active' AND deleted_at IS NULL LIMIT 1");
        $stmt->execute();
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            $_SESSION['user_id'] = $admin['id'];
            $_SESSION['name'] = $admin['name'];
            $_SESSION['email'] = $admin['email'];
            $_SESSION['role'] = $admin['role'];
        }
    } catch (Exception $e) {
        // Continue without auto-login
    }
}

// Load database manager
require_once __DIR__ . '/includes/database_manager.php';

// Function to check if column exists
function columnExists($db, $table, $column) {
    try {
        $stmt = $db->prepare("SHOW COLUMNS FROM $table LIKE ?");
        $stmt->execute([$column]);
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Function to get safe column list
function getSafeColumns($db) {
    $baseColumns = [
        'id', 'transfer_code', 'pickup_code', 'sender_name', 'sender_phone',
        'recipient_name', 'recipient_phone', 'amount', 'status', 'created_at'
    ];
    
    $optionalColumns = [
        'user_id', 'sender_country_id', 'recipient_country_id', 'sender_address', 
        'recipient_address', 'fee', 'total_amount', 'exchange_rate', 'currency_from', 
        'currency_to', 'payment_method', 'purpose', 'notes', 'updated_at', 'deleted_at'
    ];
    
    $safeColumns = [];
    
    // Add base columns (these should always exist)
    foreach ($baseColumns as $col) {
        $safeColumns[] = "t.$col";
    }
    
    // Add optional columns if they exist
    foreach ($optionalColumns as $col) {
        if (columnExists($db, 'transfers', $col)) {
            $safeColumns[] = "t.$col";
        }
    }
    
    return implode(', ', $safeColumns);
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    // Clean output buffer and set JSON header
    while (ob_get_level()) {
        ob_end_clean();
    }
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $dbManager = DatabaseManager::getInstance();
        $db = $dbManager->getConnection();
        
        switch ($_POST['action']) {
            case 'get_transfers':
                $page = max(1, intval($_POST['page'] ?? 1));
                $limit = max(1, min(100, intval($_POST['limit'] ?? 10)));
                $search = trim($_POST['search'] ?? '');
                $status_filter = trim($_POST['status_filter'] ?? '');
                $date_from = trim($_POST['date_from'] ?? '');
                $date_to = trim($_POST['date_to'] ?? '');
                
                $offset = ($page - 1) * $limit;
                
                // Build WHERE clause safely
                $where = [];
                $params = [];
                
                // Only add deleted_at condition if column exists
                if (columnExists($db, 'transfers', 'deleted_at')) {
                    $where[] = "(t.deleted_at IS NULL OR t.deleted_at = '')";
                }
                
                if (!empty($search)) {
                    $searchConditions = [];
                    if (columnExists($db, 'transfers', 'transfer_code')) {
                        $searchConditions[] = "t.transfer_code LIKE ?";
                        $params[] = "%$search%";
                    }
                    if (columnExists($db, 'transfers', 'pickup_code')) {
                        $searchConditions[] = "t.pickup_code LIKE ?";
                        $params[] = "%$search%";
                    }
                    if (columnExists($db, 'transfers', 'sender_name')) {
                        $searchConditions[] = "t.sender_name LIKE ?";
                        $params[] = "%$search%";
                    }
                    if (columnExists($db, 'transfers', 'recipient_name')) {
                        $searchConditions[] = "t.recipient_name LIKE ?";
                        $params[] = "%$search%";
                    }
                    
                    if (!empty($searchConditions)) {
                        $where[] = "(" . implode(' OR ', $searchConditions) . ")";
                    }
                }
                
                if (!empty($status_filter) && columnExists($db, 'transfers', 'status')) {
                    $where[] = "t.status = ?";
                    $params[] = $status_filter;
                }
                
                if (!empty($date_from) && columnExists($db, 'transfers', 'created_at')) {
                    $where[] = "DATE(t.created_at) >= ?";
                    $params[] = $date_from;
                }
                
                if (!empty($date_to) && columnExists($db, 'transfers', 'created_at')) {
                    $where[] = "DATE(t.created_at) <= ?";
                    $params[] = $date_to;
                }
                
                $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
                
                // Get total count
                $countQuery = "SELECT COUNT(*) FROM transfers t $whereClause";
                $stmt = $db->prepare($countQuery);
                $stmt->execute($params);
                $total = intval($stmt->fetchColumn());
                
                // Get safe columns
                $safeColumns = getSafeColumns($db);
                
                // Build joins safely
                $joins = [];
                if (columnExists($db, 'transfers', 'user_id')) {
                    $joins[] = "LEFT JOIN users u ON t.user_id = u.id AND (u.deleted_at IS NULL OR u.deleted_at = '')";
                    $safeColumns .= ", COALESCE(u.name, 'غير محدد') as user_name";
                }
                
                if (columnExists($db, 'transfers', 'sender_country_id')) {
                    $joins[] = "LEFT JOIN countries sc ON t.sender_country_id = sc.id";
                    $safeColumns .= ", COALESCE(sc.name, 'غير محدد') as sender_country";
                }
                
                if (columnExists($db, 'transfers', 'recipient_country_id')) {
                    $joins[] = "LEFT JOIN countries rc ON t.recipient_country_id = rc.id";
                    $safeColumns .= ", COALESCE(rc.name, 'غير محدد') as recipient_country";
                }
                
                $joinClause = implode(' ', $joins);
                
                // Get transfers with safe query
                $query = "
                    SELECT $safeColumns
                    FROM transfers t
                    $joinClause
                    $whereClause 
                    ORDER BY t.created_at DESC 
                    LIMIT ? OFFSET ?
                ";
                
                $stmt = $db->prepare($query);
                $stmt->execute(array_merge($params, [$limit, $offset]));
                $transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Clean and format data
                foreach ($transfers as &$transfer) {
                    $transfer['amount'] = floatval($transfer['amount'] ?? 0);
                    $transfer['fee'] = floatval($transfer['fee'] ?? 0);
                    $transfer['total_amount'] = floatval($transfer['total_amount'] ?? $transfer['amount']);
                    $transfer['user_name'] = $transfer['user_name'] ?? 'غير محدد';
                    $transfer['sender_country'] = $transfer['sender_country'] ?? 'غير محدد';
                    $transfer['recipient_country'] = $transfer['recipient_country'] ?? 'غير محدد';
                }
                
                echo json_encode([
                    'success' => true,
                    'transfers' => $transfers,
                    'total' => $total,
                    'page' => $page,
                    'pages' => max(1, ceil($total / $limit)),
                    'limit' => $limit,
                    'debug' => [
                        'query' => $query,
                        'where' => $whereClause,
                        'columns_checked' => true
                    ]
                ], JSON_UNESCAPED_UNICODE);
                exit;
                
            case 'get_transfer':
                $id = intval($_POST['id'] ?? 0);
                if ($id <= 0) {
                    echo json_encode(['success' => false, 'message' => 'معرف التحويل غير صحيح'], JSON_UNESCAPED_UNICODE);
                    exit;
                }
                
                // Get safe columns for single transfer
                $safeColumns = getSafeColumns($db);
                
                // Build joins safely
                $joins = [];
                if (columnExists($db, 'transfers', 'user_id')) {
                    $joins[] = "LEFT JOIN users u ON t.user_id = u.id AND (u.deleted_at IS NULL OR u.deleted_at = '')";
                    $safeColumns .= ", COALESCE(u.name, 'غير محدد') as user_name, COALESCE(u.email, 'غير محدد') as user_email";
                }
                
                if (columnExists($db, 'transfers', 'sender_country_id')) {
                    $joins[] = "LEFT JOIN countries sc ON t.sender_country_id = sc.id";
                    $safeColumns .= ", COALESCE(sc.name, 'غير محدد') as sender_country";
                }
                
                if (columnExists($db, 'transfers', 'recipient_country_id')) {
                    $joins[] = "LEFT JOIN countries rc ON t.recipient_country_id = rc.id";
                    $safeColumns .= ", COALESCE(rc.name, 'غير محدد') as recipient_country";
                }
                
                $joinClause = implode(' ', $joins);
                
                $whereClause = "WHERE t.id = ?";
                if (columnExists($db, 'transfers', 'deleted_at')) {
                    $whereClause .= " AND (t.deleted_at IS NULL OR t.deleted_at = '')";
                }
                
                $stmt = $db->prepare("
                    SELECT $safeColumns
                    FROM transfers t
                    $joinClause
                    $whereClause
                ");
                $stmt->execute([$id]);
                $transfer = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($transfer) {
                    // Format numeric values safely
                    $transfer['amount'] = floatval($transfer['amount'] ?? 0);
                    $transfer['fee'] = floatval($transfer['fee'] ?? 0);
                    $transfer['total_amount'] = floatval($transfer['total_amount'] ?? $transfer['amount']);
                    $transfer['exchange_rate'] = floatval($transfer['exchange_rate'] ?? 1);
                    
                    echo json_encode(['success' => true, 'transfer' => $transfer], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode(['success' => false, 'message' => 'التحويل غير موجود'], JSON_UNESCAPED_UNICODE);
                }
                exit;
                
            case 'update_transfer_status':
                $id = intval($_POST['id'] ?? 0);
                $status = trim($_POST['status'] ?? '');
                
                if ($id <= 0) {
                    echo json_encode(['success' => false, 'message' => 'معرف التحويل غير صحيح'], JSON_UNESCAPED_UNICODE);
                    exit;
                }
                
                if (!columnExists($db, 'transfers', 'status')) {
                    echo json_encode(['success' => false, 'message' => 'عمود الحالة غير موجود في الجدول'], JSON_UNESCAPED_UNICODE);
                    exit;
                }
                
                $validStatuses = ['pending', 'processing', 'completed', 'cancelled', 'failed'];
                if (!in_array($status, $validStatuses)) {
                    echo json_encode(['success' => false, 'message' => 'حالة غير صحيحة'], JSON_UNESCAPED_UNICODE);
                    exit;
                }
                
                $updateFields = ["status = ?"];
                $params = [$status];
                
                if (columnExists($db, 'transfers', 'updated_at')) {
                    $updateFields[] = "updated_at = NOW()";
                }
                
                $whereClause = "WHERE id = ?";
                $params[] = $id;
                
                if (columnExists($db, 'transfers', 'deleted_at')) {
                    $whereClause .= " AND (deleted_at IS NULL OR deleted_at = '')";
                }
                
                $updateSQL = "UPDATE transfers SET " . implode(', ', $updateFields) . " $whereClause";
                $stmt = $db->prepare($updateSQL);
                $result = $stmt->execute($params);
                
                if ($result && $stmt->rowCount() > 0) {
                    echo json_encode(['success' => true, 'message' => 'تم تحديث حالة التحويل بنجاح'], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في تحديث حالة التحويل أو التحويل غير موجود'], JSON_UNESCAPED_UNICODE);
                }
                exit;
                
            case 'check_table_structure':
                $columns = $db->query("DESCRIBE transfers")->fetchAll(PDO::FETCH_ASSOC);
                $columnNames = array_column($columns, 'Field');
                
                echo json_encode([
                    'success' => true,
                    'columns' => $columnNames,
                    'total_columns' => count($columnNames),
                    'structure' => $columns
                ], JSON_UNESCAPED_UNICODE);
                exit;
                
            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير معروف'], JSON_UNESCAPED_UNICODE);
                exit;
        }
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false, 
            'message' => 'خطأ في الخادم: ' . $e->getMessage(),
            'error_details' => [
                'file' => basename($e->getFile()),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Get statistics safely
try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    $deletedCondition = columnExists($db, 'transfers', 'deleted_at') ? "AND deleted_at IS NULL" : "";
    
    $stats = [
        'total_transfers' => intval($db->query("SELECT COUNT(*) FROM transfers WHERE 1=1 $deletedCondition")->fetchColumn()),
        'pending_transfers' => columnExists($db, 'transfers', 'status') ? 
            intval($db->query("SELECT COUNT(*) FROM transfers WHERE status = 'pending' $deletedCondition")->fetchColumn()) : 0,
        'completed_transfers' => columnExists($db, 'transfers', 'status') ? 
            intval($db->query("SELECT COUNT(*) FROM transfers WHERE status = 'completed' $deletedCondition")->fetchColumn()) : 0,
        'total_amount' => columnExists($db, 'transfers', 'amount') && columnExists($db, 'transfers', 'status') ? 
            floatval($db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE status = 'completed' $deletedCondition")->fetchColumn()) : 0,
        'today_transfers' => columnExists($db, 'transfers', 'created_at') ? 
            intval($db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = CURDATE() $deletedCondition")->fetchColumn()) : 0,
        'today_amount' => columnExists($db, 'transfers', 'amount') && columnExists($db, 'transfers', 'status') && columnExists($db, 'transfers', 'created_at') ? 
            floatval($db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' $deletedCondition")->fetchColumn()) : 0
    ];
} catch (Exception $e) {
    $stats = [
        'total_transfers' => 0,
        'pending_transfers' => 0,
        'completed_transfers' => 0,
        'total_amount' => 0,
        'today_transfers' => 0,
        'today_amount' => 0
    ];
}

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التحويلات (آمنة) - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            color: white;
            padding: 30px;
        }
        
        .stats-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stats-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-pending { background: #fff3cd; color: #664d03; }
        .status-processing { background: #cff4fc; color: #055160; }
        .status-completed { background: #d1e7dd; color: #0f5132; }
        .status-cancelled { background: #f8d7da; color: #842029; }
        .status-failed { background: #f8d7da; color: #842029; }
        
        .transfer-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #0066cc;
        }
        
        .amount-display {
            font-weight: bold;
            color: #20c997;
        }
        
        .btn-action {
            margin: 2px;
            padding: 5px 10px;
            font-size: 0.8em;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            color: white;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box .bi-search {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        
        .search-box input {
            padding-left: 40px;
        }
        
        .alert-fixed {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
        }
        
        .safe-badge {
            background: linear-gradient(45deg, #20c997, #17a2b8);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-1">
                        <i class="bi bi-shield-check me-2"></i>
                        إدارة التحويلات (آمنة)
                        <span class="safe-badge ms-2">SAFE</span>
                    </h1>
                    <p class="mb-0 opacity-75">نسخة آمنة تتعامل مع الأعمدة المفقودة بذكاء</p>
                    <small class="opacity-50">مرحباً <?= htmlspecialchars($userName) ?></small>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light me-2" onclick="loadTransfers()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        تحديث
                    </button>
                    <button class="btn btn-info me-2" onclick="checkTableStructure()">
                        <i class="bi bi-table me-1"></i>
                        فحص الجدول
                    </button>
                    <a href="fix_transfers_table.php" class="btn btn-warning me-2">
                        <i class="bi bi-tools me-1"></i>
                        إصلاح الجدول
                    </a>
                    <a href="debug_ajax.php" class="btn btn-secondary">
                        <i class="bi bi-bug me-1"></i>
                        تشخيص
                    </a>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mt-4">
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['total_transfers']) ?></div>
                        <div class="stats-label">إجمالي التحويلات</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['pending_transfers']) ?></div>
                        <div class="stats-label">في الانتظار</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['completed_transfers']) ?></div>
                        <div class="stats-label">مكتملة</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number">$<?= number_format($stats['total_amount'], 0) ?></div>
                        <div class="stats-label">إجمالي المبلغ</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['today_transfers']) ?></div>
                        <div class="stats-label">تحويلات اليوم</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number">$<?= number_format($stats['today_amount'], 0) ?></div>
                        <div class="stats-label">مبلغ اليوم</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- System Status Alert -->
            <div id="systemAlert" class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="bi bi-info-circle me-2"></i>
                <strong>نظام آمن:</strong> هذه الصفحة تتحقق من وجود الأعمدة قبل الاستعلام وتتعامل مع الأعمدة المفقودة بأمان.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>

            <!-- Filters -->
            <div class="filter-section">
                <div class="row">
                    <div class="col-md-3">
                        <div class="search-box">
                            <i class="bi bi-search"></i>
                            <input type="text" class="form-control" id="searchInput" placeholder="البحث برمز التحويل أو الاسم...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="pending">في الانتظار</option>
                            <option value="processing">قيد المعالجة</option>
                            <option value="completed">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                            <option value="failed">فاشل</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="dateFrom" placeholder="من تاريخ">
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="dateTo" placeholder="إلى تاريخ">
                    </div>
                    <div class="col-md-1">
                        <select class="form-select" id="limitSelect">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary w-100" onclick="exportTransfers()">
                            <i class="bi bi-download me-1"></i>
                            تصدير
                        </button>
                    </div>
                </div>
            </div>

            <!-- Transfers Table -->
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>رمز التحويل</th>
                                <th>المرسل</th>
                                <th>المستلم</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transfersTableBody">
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <p class="mt-2">جاري تحميل التحويلات بأمان...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center p-3">
                    <div id="tableInfo" class="text-muted"></div>
                    <nav>
                        <ul class="pagination mb-0" id="pagination"></ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        let currentPage = 1;
        let currentLimit = 10;
        let isLoading = false;
        let tableStructure = null;

        $(document).ready(function() {
            console.log('🛡️ Safe Transfers Page Loaded');
            checkTableStructure();
            loadTransfers();

            // Event listeners
            $('#searchInput').on('input', debounce(function() {
                currentPage = 1;
                loadTransfers();
            }, 500));

            $('#statusFilter').on('change', function() {
                currentPage = 1;
                loadTransfers();
            });

            $('#dateFrom, #dateTo').on('change', function() {
                currentPage = 1;
                loadTransfers();
            });

            $('#limitSelect').on('change', function() {
                currentLimit = parseInt($(this).val());
                currentPage = 1;
                loadTransfers();
            });
        });

        function checkTableStructure() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'check_table_structure' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        tableStructure = response;
                        console.log('📋 Table structure:', response);

                        const missingColumns = checkMissingColumns(response.columns);
                        if (missingColumns.length > 0) {
                            showWarning(`تحذير: بعض الأعمدة مفقودة: ${missingColumns.join(', ')}`);
                        } else {
                            showSuccess('✅ جميع الأعمدة المطلوبة موجودة');
                        }
                    }
                },
                error: function() {
                    showError('فشل في فحص بنية الجدول');
                }
            });
        }

        function checkMissingColumns(existingColumns) {
            const requiredColumns = [
                'id', 'transfer_code', 'pickup_code', 'user_id', 'sender_name',
                'sender_phone', 'sender_country_id', 'recipient_name', 'recipient_phone',
                'recipient_country_id', 'amount', 'fee', 'status', 'created_at'
            ];

            return requiredColumns.filter(col => !existingColumns.includes(col));
        }

        function loadTransfers() {
            if (isLoading) return;

            isLoading = true;

            const requestData = {
                action: 'get_transfers',
                page: currentPage,
                limit: currentLimit,
                search: $('#searchInput').val().trim(),
                status_filter: $('#statusFilter').val(),
                date_from: $('#dateFrom').val(),
                date_to: $('#dateTo').val()
            };

            console.log('📤 Safe request:', requestData);

            $.ajax({
                url: '',
                method: 'POST',
                data: requestData,
                dataType: 'json',
                timeout: 30000,
                success: function(response) {
                    console.log('📥 Safe response:', response);

                    if (response && response.success) {
                        displayTransfers(response.transfers || []);
                        updatePagination(response.page || 1, response.pages || 1, response.total || 0);

                        if (response.debug) {
                            console.log('🔍 Debug info:', response.debug);
                        }
                    } else {
                        const errorMsg = response ? response.message : 'استجابة غير صحيحة من الخادم';
                        showError('خطأ في تحميل البيانات: ' + errorMsg);
                        displayError('فشل في تحميل البيانات: ' + errorMsg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Safe AJAX Error:', {status, error, response: xhr.responseText});

                    let errorMessage = 'خطأ في الاتصال بالخادم';
                    if (status === 'parsererror') {
                        errorMessage = 'خطأ في تحليل البيانات - الخادم لم يرسل JSON صحيح';
                    }

                    showError(errorMessage);
                    displayError(errorMessage, xhr.responseText);
                },
                complete: function() {
                    isLoading = false;
                }
            });
        }

        function displayTransfers(transfers) {
            const tbody = $('#transfersTableBody');
            tbody.empty();

            if (!transfers || transfers.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد تحويلات</p>
                            <button class="btn btn-outline-primary btn-sm" onclick="loadTransfers()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                إعادة المحاولة
                            </button>
                        </td>
                    </tr>
                `);
                return;
            }

            transfers.forEach(transfer => {
                const row = `
                    <tr>
                        <td>
                            <span class="transfer-code">${escapeHtml(transfer.transfer_code || 'غير محدد')}</span>
                            <br>
                            <small class="text-muted">${escapeHtml(transfer.pickup_code || '')}</small>
                        </td>
                        <td>
                            <div>
                                <strong>${escapeHtml(transfer.sender_name || 'غير محدد')}</strong>
                                <br>
                                <small class="text-muted">${escapeHtml(transfer.sender_country || '')}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>${escapeHtml(transfer.recipient_name || 'غير محدد')}</strong>
                                <br>
                                <small class="text-muted">${escapeHtml(transfer.recipient_country || '')}</small>
                            </div>
                        </td>
                        <td>
                            <span class="amount-display">$${formatNumber(transfer.amount || 0)}</span>
                            <br>
                            <small class="text-muted">رسوم: $${formatNumber(transfer.fee || 0)}</small>
                        </td>
                        <td>
                            <span class="status-badge status-${transfer.status || 'pending'}">
                                ${getStatusText(transfer.status || 'pending')}
                            </span>
                        </td>
                        <td>
                            <div>
                                ${formatDate(transfer.created_at)}
                                <br>
                                <small class="text-muted">${formatTime(transfer.created_at)}</small>
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-info btn-action" onclick="viewTransfer(${transfer.id})" title="عرض التفاصيل">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-warning btn-action" onclick="updateStatus(${transfer.id}, '${transfer.status || 'pending'}')" title="تحديث الحالة">
                                <i class="bi bi-arrow-repeat"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        function displayError(message, details = null) {
            const tbody = $('#transfersTableBody');
            tbody.html(`
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>خطأ في تحميل البيانات</strong>
                            <p class="mt-2">${escapeHtml(message)}</p>
                            <div class="mt-3">
                                <button class="btn btn-outline-danger btn-sm me-2" onclick="loadTransfers()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    إعادة المحاولة
                                </button>
                                <a href="fix_transfers_table.php" class="btn btn-outline-warning btn-sm me-2">
                                    <i class="bi bi-tools me-1"></i>
                                    إصلاح الجدول
                                </a>
                                <a href="debug_ajax.php" class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-bug me-1"></i>
                                    تشخيص المشكلة
                                </a>
                            </div>
                            ${details ? `<details class="mt-3"><summary>تفاصيل تقنية</summary><pre class="text-start mt-2">${escapeHtml(details.substring(0, 500))}</pre></details>` : ''}
                        </div>
                    </td>
                </tr>
            `);
        }

        function updatePagination(page, pages, total) {
            const pagination = $('#pagination');
            pagination.empty();

            $('#tableInfo').text(`عرض ${total} تحويل - الصفحة ${page} من ${pages}`);

            if (pages <= 1) return;

            // Previous button
            if (page > 1) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage(${page - 1})">السابق</a>
                    </li>
                `);
            }

            // Page numbers
            for (let i = Math.max(1, page - 2); i <= Math.min(pages, page + 2); i++) {
                pagination.append(`
                    <li class="page-item ${i === page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `);
            }

            // Next button
            if (page < pages) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage(${page + 1})">التالي</a>
                    </li>
                `);
            }
        }

        function changePage(page) {
            if (isLoading) return;
            currentPage = page;
            loadTransfers();
        }

        function viewTransfer(id) {
            showInfo('عرض تفاصيل التحويل قيد التطوير');
        }

        function updateStatus(id, currentStatus) {
            showInfo('تحديث حالة التحويل قيد التطوير');
        }

        function exportTransfers() {
            showInfo('جاري تصدير التحويلات...');

            setTimeout(() => {
                const data = {
                    title: 'تقرير التحويلات الآمن',
                    exported_at: new Date().toISOString(),
                    table_structure: tableStructure,
                    filters: {
                        search: $('#searchInput').val(),
                        status: $('#statusFilter').val(),
                        date_from: $('#dateFrom').val(),
                        date_to: $('#dateTo').val()
                    }
                };

                const dataStr = JSON.stringify(data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `safe_transfers_export_${new Date().toISOString().slice(0, 10)}.json`;
                link.click();
                URL.revokeObjectURL(url);

                showSuccess('تم تصدير التحويلات بنجاح');
            }, 1000);
        }

        // Utility functions
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function formatNumber(num) {
            if (num === null || num === undefined || isNaN(num)) return '0.00';
            return parseFloat(num).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('ar-SA');
            } catch (e) {
                return dateString;
            }
        }

        function formatTime(dateString) {
            if (!dateString) return '-';
            try {
                const date = new Date(dateString);
                return date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
            } catch (e) {
                return '';
            }
        }

        function getStatusText(status) {
            const statuses = {
                'pending': 'في الانتظار',
                'processing': 'قيد المعالجة',
                'completed': 'مكتمل',
                'cancelled': 'ملغي',
                'failed': 'فاشل'
            };
            return statuses[status] || status || 'غير محدد';
        }

        function showSuccess(message) {
            showAlert(message, 'success');
        }

        function showError(message) {
            showAlert(message, 'danger');
        }

        function showWarning(message) {
            showAlert(message, 'warning');
        }

        function showInfo(message) {
            showAlert(message, 'info');
        }

        function showAlert(message, type) {
            $('.alert-fixed').remove();

            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show alert-fixed" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${escapeHtml(message)}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);

            setTimeout(function() {
                $('.alert-fixed').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    </script>
</body>
</html>
