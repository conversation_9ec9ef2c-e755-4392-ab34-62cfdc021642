[2025-07-26 00:00:03] [INFO] Database connection established successfully 
[2025-07-26 00:00:03] [INFO] Database connection established successfully 
[2025-07-26 00:00:14] [INFO] Database connection established successfully 
[2025-07-26 00:00:34] [INFO] Database connection established successfully 
[2025-07-26 00:00:44] [INFO] Database connection established successfully 
[2025-07-26 00:01:05] [INFO] Database connection established successfully 
[2025-07-26 00:01:14] [INFO] Database connection established successfully 
[2025-07-26 00:01:35] [INFO] Database connection established successfully 
[2025-07-26 00:01:44] [INFO] Database connection established successfully 
[2025-07-26 00:01:56] [INFO] Database connection established successfully 
[2025-07-26 00:02:15] [INFO] Database connection established successfully 
[2025-07-26 00:02:45] [INFO] Database connection established successfully 
[2025-07-26 00:03:15] [INFO] Database connection established successfully 
[2025-07-26 00:03:17] [INFO] Database connection established successfully 
[2025-07-26 00:03:17] [INFO] Database connection established successfully 
[2025-07-26 00:03:45] [INFO] Database connection established successfully 
[2025-07-26 00:03:51] [INFO] Database connection established successfully 
[2025-07-26 00:03:51] [INFO] Database connection established successfully 
[2025-07-26 00:04:07] [INFO] Database connection established successfully 
[2025-07-26 00:04:07] [INFO] Database connection established successfully 
[2025-07-26 00:04:15] [INFO] Database connection established successfully 
[2025-07-26 00:04:37] [INFO] Database connection established successfully 
[2025-07-26 00:04:45] [INFO] Database connection established successfully 
[2025-07-26 00:05:07] [INFO] Database connection established successfully 
[2025-07-26 00:05:16] [INFO] Database connection established successfully 
[2025-07-26 00:05:38] [INFO] Database connection established successfully 
[2025-07-26 00:05:41] [INFO] Database connection established successfully 
[2025-07-26 00:05:41] [INFO] Database connection established successfully 
[2025-07-26 00:05:46] [INFO] Database connection established successfully 
[2025-07-26 00:06:01] [INFO] Database connection established successfully 
[2025-07-26 00:06:03] [INFO] Database connection established successfully 
[2025-07-26 00:06:11] [INFO] Database connection established successfully 
[2025-07-26 00:06:16] [INFO] Database connection established successfully 
[2025-07-26 00:06:41] [INFO] Database connection established successfully 
[2025-07-26 00:06:46] [INFO] Database connection established successfully 
[2025-07-26 00:07:11] [INFO] Database connection established successfully 
[2025-07-26 00:07:15] [INFO] Database connection established successfully 
[2025-07-26 00:07:15] [INFO] Database connection established successfully 
[2025-07-26 00:07:17] [INFO] Database connection established successfully 
[2025-07-26 00:07:45] [INFO] Database connection established successfully 
[2025-07-26 00:07:47] [INFO] Database connection established successfully 
[2025-07-26 00:08:15] [INFO] Database connection established successfully 
[2025-07-26 00:08:18] [INFO] Database connection established successfully 
[2025-07-26 00:08:45] [INFO] Database connection established successfully 
[2025-07-26 00:08:48] [INFO] Database connection established successfully 
[2025-07-26 00:09:15] [INFO] Database connection established successfully 
[2025-07-26 00:09:18] [INFO] Database connection established successfully 
[2025-07-26 00:09:45] [INFO] Database connection established successfully 
[2025-07-26 00:09:48] [INFO] Database connection established successfully 
[2025-07-26 00:10:15] [INFO] Database connection established successfully 
[2025-07-26 00:10:17] [INFO] Database connection established successfully 
[2025-07-26 00:10:18] [INFO] Database connection established successfully 
[2025-07-26 00:10:18] [INFO] Database connection established successfully 
[2025-07-26 00:10:45] [INFO] Database connection established successfully 
[2025-07-26 00:10:48] [INFO] Database connection established successfully 
[2025-07-26 00:10:48] [INFO] Database connection established successfully 
[2025-07-26 00:10:54] [INFO] Database connection established successfully 
[2025-07-26 00:10:54] [INFO] Database connection established successfully 
[2025-07-26 00:11:03] [INFO] Database connection established successfully 
[2025-07-26 00:11:03] [INFO] Database connection established successfully 
[2025-07-26 00:11:05] [INFO] Database connection established successfully 
[2025-07-26 00:11:05] [INFO] Database connection established successfully 
[2025-07-26 00:11:19] [INFO] Database connection established successfully 
[2025-07-26 00:11:35] [INFO] Database connection established successfully 
[2025-07-26 00:11:49] [INFO] Database connection established successfully 
[2025-07-26 00:12:05] [INFO] Database connection established successfully 
[2025-07-26 00:12:19] [INFO] Database connection established successfully 
[2025-07-26 00:12:35] [INFO] Database connection established successfully 
[2025-07-26 00:12:44] [INFO] Database connection established successfully 
[2025-07-26 00:12:44] [INFO] Database connection established successfully 
[2025-07-26 00:12:45] [INFO] Database connection established successfully 
[2025-07-26 00:12:45] [INFO] Database connection established successfully 
[2025-07-26 00:12:45] [INFO] Database connection established successfully 
[2025-07-26 00:12:46] [INFO] Database connection established successfully 
[2025-07-26 00:12:46] [INFO] Database connection established successfully 
[2025-07-26 00:12:49] [INFO] Database connection established successfully 
[2025-07-26 00:13:04] [INFO] Database connection established successfully 
[2025-07-26 00:13:12] [INFO] Database connection established successfully 
[2025-07-26 00:13:12] [INFO] Database connection established successfully 
[2025-07-26 00:13:15] [INFO] Database connection established successfully 
[2025-07-26 00:13:17] [INFO] Database connection established successfully 
[2025-07-26 00:13:17] [INFO] Database connection established successfully 
[2025-07-26 00:13:19] [INFO] Database connection established successfully 
[2025-07-26 00:13:19] [INFO] Database connection established successfully 
[2025-07-26 00:13:22] [INFO] Database connection established successfully 
[2025-07-26 00:13:22] [INFO] Database connection established successfully 
[2025-07-26 00:13:26] [INFO] Database connection established successfully 
[2025-07-26 00:13:29] [INFO] Database connection established successfully 
[2025-07-26 00:13:29] [INFO] Database connection established successfully 
[2025-07-26 00:13:37] [INFO] Database connection established successfully 
[2025-07-26 00:13:38] [INFO] Database connection established successfully 
[2025-07-26 00:13:50] [INFO] Database connection established successfully 
[2025-07-26 00:14:20] [INFO] Database connection established successfully 
[2025-07-26 00:14:50] [INFO] Database connection established successfully 
[2025-07-26 00:15:20] [INFO] Database connection established successfully 
[2025-07-26 00:15:42] [INFO] Database connection established successfully 
[2025-07-26 00:15:42] [INFO] Database connection established successfully 
[2025-07-26 00:15:50] [INFO] Database connection established successfully 
[2025-07-26 00:15:54] [INFO] Database connection established successfully 
[2025-07-26 00:16:02] [INFO] Database connection established successfully 
[2025-07-26 00:16:09] [INFO] Database connection established successfully 
[2025-07-26 00:16:11] [INFO] Database connection established successfully 
[2025-07-26 00:16:11] [INFO] Database connection established successfully 
[2025-07-26 00:16:20] [INFO] Database connection established successfully 
[2025-07-26 00:16:41] [INFO] Database connection established successfully 
[2025-07-26 00:16:50] [INFO] Database connection established successfully 
[2025-07-26 00:17:11] [INFO] Database connection established successfully 
[2025-07-26 00:17:20] [INFO] Database connection established successfully 
[2025-07-26 00:17:41] [INFO] Database connection established successfully 
[2025-07-26 00:17:50] [INFO] Database connection established successfully 
[2025-07-26 00:18:11] [INFO] Database connection established successfully 
[2025-07-26 00:18:21] [INFO] Database connection established successfully 
[2025-07-26 00:18:41] [INFO] Database connection established successfully 
[2025-07-26 00:18:51] [INFO] Database connection established successfully 
[2025-07-26 00:19:13] [INFO] Database connection established successfully 
[2025-07-26 00:19:14] [INFO] Database connection established successfully 
[2025-07-26 00:19:14] [INFO] Database connection established successfully 
[2025-07-26 00:19:17] [INFO] Database connection established successfully 
[2025-07-26 00:19:17] [INFO] Database connection established successfully 
[2025-07-26 00:19:43] [INFO] Database connection established successfully 
[2025-07-26 00:19:47] [INFO] Database connection established successfully 
[2025-07-26 00:20:13] [INFO] Database connection established successfully 
[2025-07-26 00:20:17] [INFO] Database connection established successfully 
[2025-07-26 00:20:44] [INFO] Database connection established successfully 
[2025-07-26 00:20:47] [INFO] Database connection established successfully 
[2025-07-26 00:20:49] [INFO] User logged out successfully {"user_id":1,"email":"<EMAIL>"}
[2025-07-26 00:21:14] [INFO] Database connection established successfully 
[2025-07-26 00:21:14] [INFO] User logged in successfully {"user_id":1,"email":"<EMAIL>","role":"admin"}
[2025-07-26 00:21:14] [INFO] Auto-login performed for admin user 
[2025-07-26 00:21:17] [INFO] Database connection established successfully 
[2025-07-26 00:21:32] [INFO] Database connection established successfully 
[2025-07-26 00:21:32] [INFO] Database connection established successfully 
[2025-07-26 00:21:43] [INFO] Database connection established successfully 
[2025-07-26 00:21:44] [INFO] User logout {"user_id":1,"user_name":"System Administrator","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0"}
[2025-07-26 00:21:44] [INFO] User logged out successfully {"user_id":1,"email":"<EMAIL>"}
[2025-07-26 00:21:47] [INFO] Database connection established successfully 
[2025-07-26 00:21:47] [INFO] User logged in successfully {"user_id":1,"email":"<EMAIL>","role":"admin"}
[2025-07-26 00:21:47] [INFO] Auto-login performed for admin user 
[2025-07-26 00:21:50] [INFO] Database connection established successfully 
[2025-07-26 00:21:54] [INFO] User logout {"user_id":1,"user_name":"System Administrator","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0"}
[2025-07-26 00:21:54] [INFO] User logged out successfully {"user_id":1,"email":"<EMAIL>"}
[2025-07-26 00:21:57] [INFO] User logged out successfully {"user_id":null,"email":""}
[2025-07-26 00:21:58] [INFO] User logged out successfully {"user_id":null,"email":""}
[2025-07-26 00:22:01] [INFO] User logged out successfully {"user_id":null,"email":""}
[2025-07-26 00:22:20] [INFO] Database connection established successfully 
[2025-07-26 00:22:20] [INFO] User logged in successfully {"user_id":1,"email":"<EMAIL>","role":"admin"}
[2025-07-26 00:22:20] [INFO] Auto-login performed for admin user 
[2025-07-26 00:22:32] [INFO] Database connection established successfully 
[2025-07-26 00:22:44] [INFO] Database connection established successfully 
[2025-07-26 00:22:44] [INFO] Database connection established successfully 
[2025-07-26 00:22:55] [INFO] User logged out successfully {"user_id":1,"email":"<EMAIL>"}
[2025-07-26 00:22:57] [INFO] Database connection established successfully 
[2025-07-26 00:22:58] [INFO] User logged in successfully {"user_id":1,"email":"<EMAIL>","role":"admin"}
[2025-07-26 00:22:58] [INFO] Auto-login performed for admin user 
[2025-07-26 00:23:03] [INFO] Database connection established successfully 
[2025-07-26 00:23:03] [INFO] Database connection established successfully 
[2025-07-26 00:23:23] [INFO] Database connection established successfully 
[2025-07-26 00:23:24] [INFO] Database connection established successfully 
[2025-07-26 00:23:28] [INFO] Database connection established successfully 
[2025-07-26 00:23:31] [INFO] Database connection established successfully 
[2025-07-26 00:24:01] [INFO] Database connection established successfully 
[2025-07-26 00:24:02] [INFO] Database connection established successfully 
[2025-07-26 00:24:18] [INFO] User logged out successfully {"user_id":1,"email":"<EMAIL>"}
[2025-07-26 00:24:21] [INFO] Database connection established successfully 
[2025-07-26 00:24:21] [INFO] User logged in successfully {"user_id":1,"email":"<EMAIL>","role":"admin"}
[2025-07-26 00:24:21] [INFO] Auto-login performed for admin user 
[2025-07-26 00:24:37] [INFO] Database connection established successfully 
[2025-07-26 00:24:37] [INFO] Database connection established successfully 
[2025-07-26 00:25:07] [INFO] Database connection established successfully 
[2025-07-26 00:25:37] [INFO] Database connection established successfully 
[2025-07-26 00:25:57] [INFO] Database connection established successfully 
[2025-07-26 00:25:58] [INFO] Database connection established successfully 
[2025-07-26 00:26:28] [INFO] Database connection established successfully 
[2025-07-26 00:26:58] [INFO] Database connection established successfully 
[2025-07-26 00:27:28] [INFO] Database connection established successfully 
[2025-07-26 00:27:58] [INFO] Database connection established successfully 
[2025-07-26 00:28:28] [INFO] Database connection established successfully 
[2025-07-26 00:28:58] [INFO] Database connection established successfully 
[2025-07-26 00:29:28] [INFO] Database connection established successfully 
[2025-07-26 00:29:58] [INFO] Database connection established successfully 
[2025-07-26 00:30:28] [INFO] Database connection established successfully 
[2025-07-26 00:30:46] [INFO] Database connection established successfully 
[2025-07-26 00:30:46] [INFO] Database connection established successfully 
[2025-07-26 00:30:48] [INFO] Database connection established successfully 
[2025-07-26 00:30:48] [INFO] Database connection established successfully 
[2025-07-26 00:31:18] [INFO] Database connection established successfully 
[2025-07-26 00:31:48] [INFO] Database connection established successfully 
[2025-07-26 00:32:18] [INFO] Database connection established successfully 
[2025-07-26 00:32:48] [INFO] Database connection established successfully 
[2025-07-26 00:33:18] [INFO] Database connection established successfully 
[2025-07-26 00:33:48] [INFO] Database connection established successfully 
[2025-07-26 00:34:18] [INFO] Database connection established successfully 
[2025-07-26 00:34:48] [INFO] Database connection established successfully 
[2025-07-26 00:35:18] [INFO] Database connection established successfully 
[2025-07-26 00:35:30] [INFO] Database connection established successfully 
[2025-07-26 00:35:31] [INFO] Database connection established successfully 
[2025-07-26 00:35:49] [INFO] Database connection established successfully 
[2025-07-26 00:36:01] [INFO] Database connection established successfully 
[2025-07-26 00:36:19] [INFO] Database connection established successfully 
[2025-07-26 00:36:31] [INFO] Database connection established successfully 
[2025-07-26 00:36:49] [INFO] Database connection established successfully 
[2025-07-26 00:37:01] [INFO] Database connection established successfully 
[2025-07-26 00:37:19] [INFO] Database connection established successfully 
[2025-07-26 00:37:31] [INFO] Database connection established successfully 
[2025-07-26 00:37:46] [INFO] Database connection established successfully 
[2025-07-26 00:37:46] [INFO] Database connection established successfully 
[2025-07-26 00:37:49] [INFO] Database connection established successfully 
[2025-07-26 00:38:00] [INFO] Database connection established successfully 
[2025-07-26 00:38:00] [INFO] Database connection established successfully 
[2025-07-26 00:38:00] [ERROR] Query execution failed {"sql":"\n                    SELECT \n                        c.name as country_name,\n                        COUNT(t.id) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume,\n                        ROUND(COUNT(t.id) * 100.0 \/ NULLIF((SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN :start_date AND :end_date AND (deleted_at IS NULL OR deleted_at = '')), 0), 2) as percentage\n                    FROM transfers t\n                    LEFT JOIN countries c ON t.sender_country_id = c.id\n                    WHERE DATE(t.created_at) BETWEEN :start_date AND :end_date\n                    AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    GROUP BY c.id, c.name\n                    ORDER BY transfer_count DESC\n                    LIMIT 10\n                ","params":{"start_date":"2025-07-19","end_date":"2025-07-26"},"error":"SQLSTATE[HY093]: Invalid parameter number"}
[2025-07-26 00:38:09] [INFO] Database connection established successfully 
[2025-07-26 00:38:09] [INFO] Database connection established successfully 
[2025-07-26 00:38:09] [ERROR] Query execution failed {"sql":"\n                    SELECT \n                        c.name as country_name,\n                        COUNT(t.id) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume,\n                        ROUND(COUNT(t.id) * 100.0 \/ NULLIF((SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN :start_date AND :end_date AND (deleted_at IS NULL OR deleted_at = '')), 0), 2) as percentage\n                    FROM transfers t\n                    LEFT JOIN countries c ON t.sender_country_id = c.id\n                    WHERE DATE(t.created_at) BETWEEN :start_date AND :end_date\n                    AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    GROUP BY c.id, c.name\n                    ORDER BY transfer_count DESC\n                    LIMIT 10\n                ","params":{"start_date":"2025-07-19","end_date":"2025-07-26"},"error":"SQLSTATE[HY093]: Invalid parameter number"}
[2025-07-26 00:38:17] [INFO] Database connection established successfully 
[2025-07-26 00:38:17] [INFO] Database connection established successfully 
[2025-07-26 00:38:17] [ERROR] Query execution failed {"sql":"\n                    SELECT \n                        c.name as country_name,\n                        COUNT(t.id) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume,\n                        ROUND(COUNT(t.id) * 100.0 \/ NULLIF((SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN :start_date AND :end_date AND (deleted_at IS NULL OR deleted_at = '')), 0), 2) as percentage\n                    FROM transfers t\n                    LEFT JOIN countries c ON t.sender_country_id = c.id\n                    WHERE DATE(t.created_at) BETWEEN :start_date AND :end_date\n                    AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    GROUP BY c.id, c.name\n                    ORDER BY transfer_count DESC\n                    LIMIT 10\n                ","params":{"start_date":"2025-07-19","end_date":"2025-07-26"},"error":"SQLSTATE[HY093]: Invalid parameter number"}
[2025-07-26 00:38:19] [INFO] Database connection established successfully 
[2025-07-26 00:38:27] [INFO] Database connection established successfully 
[2025-07-26 00:38:27] [INFO] Database connection established successfully 
[2025-07-26 00:38:37] [INFO] Database connection established successfully 
[2025-07-26 00:38:37] [INFO] Database connection established successfully 
[2025-07-26 00:38:49] [INFO] Database connection established successfully 
[2025-07-26 00:38:57] [INFO] Database connection established successfully 
[2025-07-26 00:38:57] [INFO] Database connection established successfully 
[2025-07-26 00:39:21] [INFO] Database connection established successfully 
[2025-07-26 00:39:27] [INFO] Database connection established successfully 
[2025-07-26 00:39:40] [INFO] Database connection established successfully 
[2025-07-26 00:39:41] [INFO] Database connection established successfully 
[2025-07-26 00:39:45] [INFO] Database connection established successfully 
[2025-07-26 00:39:45] [INFO] Database connection established successfully 
[2025-07-26 00:39:45] [ERROR] Query execution failed {"sql":"\n                    SELECT \n                        c.*,\n                        COUNT(t.id) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume\n                    FROM countries c\n                    LEFT JOIN transfers t ON (c.id = t.sender_country_id OR c.id = t.recipient_country_id)\n                        AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    WHERE (deleted_at IS NULL OR deleted_at = '')\n                    GROUP BY c.id\n                    ORDER BY c.name ASC\n                    LIMIT 10 OFFSET 0\n                ","params":[],"error":"SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'deleted_at' in where clause is ambiguous"}
[2025-07-26 00:39:45] [INFO] Database connection established successfully 
[2025-07-26 00:39:45] [ERROR] Query execution failed {"sql":"SELECT COUNT(*) as count FROM countries WHERE status = 'active' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause'"}
[2025-07-26 00:39:51] [INFO] Database connection established successfully 
[2025-07-26 00:40:21] [INFO] Database connection established successfully 
[2025-07-26 00:40:51] [INFO] Database connection established successfully 
[2025-07-26 00:41:21] [INFO] Database connection established successfully 
[2025-07-26 00:41:51] [INFO] Database connection established successfully 
[2025-07-26 00:42:21] [INFO] Database connection established successfully 
[2025-07-26 00:42:51] [INFO] Database connection established successfully 
[2025-07-26 00:43:21] [INFO] Database connection established successfully 
[2025-07-26 00:43:51] [INFO] Database connection established successfully 
[2025-07-26 00:44:21] [INFO] Database connection established successfully 
[2025-07-26 00:44:51] [INFO] Database connection established successfully 
[2025-07-26 00:45:21] [INFO] Database connection established successfully 
[2025-07-26 00:45:44] [INFO] Database connection established successfully 
[2025-07-26 00:45:51] [INFO] Database connection established successfully 
[2025-07-26 00:45:52] [INFO] Database connection established successfully 
[2025-07-26 00:46:11] [INFO] Database connection established successfully 
[2025-07-26 00:47:13] [INFO] Database connection established successfully 
[2025-07-26 00:47:17] [INFO] Database connection established successfully 
[2025-07-26 00:47:33] [INFO] Database connection established successfully 
[2025-07-26 00:55:22] [INFO] Database connection established successfully 
[2025-07-26 00:55:22] [INFO] Database connection established successfully 
[2025-07-26 00:55:28] [INFO] Database connection established successfully 
[2025-07-26 00:55:37] [INFO] Database connection established successfully 
[2025-07-26 00:55:37] [INFO] Database connection established successfully 
[2025-07-26 00:55:37] [ERROR] Query execution failed {"sql":"\n                    SELECT\n                        c.*,\n                        COALESCE(COUNT(t.id), 0) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume\n                    FROM countries c\n                    LEFT JOIN transfers t ON (c.id = t.sender_country_id OR c.id = t.recipient_country_id)\n                        AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    WHERE (deleted_at IS NULL OR deleted_at = '')\n                    GROUP BY c.id, c.name, c.code, c.currency, c.exchange_rate, c.status, c.flag_url, c.created_at, c.updated_at, c.deleted_at\n                    ORDER BY c.name ASC\n                    LIMIT 10 OFFSET 0\n                ","params":[],"error":"SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'deleted_at' in where clause is ambiguous"}
[2025-07-26 00:55:37] [INFO] Database connection established successfully 
[2025-07-26 00:55:53] [INFO] Database connection established successfully 
[2025-07-26 00:56:23] [INFO] Database connection established successfully 
[2025-07-26 00:56:41] [INFO] Database connection established successfully 
[2025-07-26 00:56:41] [INFO] Database connection established successfully 
[2025-07-26 00:56:41] [ERROR] Query execution failed {"sql":"\n                    SELECT\n                        c.*,\n                        COALESCE(COUNT(t.id), 0) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume\n                    FROM countries c\n                    LEFT JOIN transfers t ON (c.id = t.sender_country_id OR c.id = t.recipient_country_id)\n                        AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    WHERE (deleted_at IS NULL OR deleted_at = '')\n                    GROUP BY c.id, c.name, c.code, c.currency, c.exchange_rate, c.status, c.flag_url, c.created_at, c.updated_at, c.deleted_at\n                    ORDER BY c.name ASC\n                    LIMIT 10 OFFSET 0\n                ","params":[],"error":"SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'deleted_at' in where clause is ambiguous"}
[2025-07-26 00:56:41] [INFO] Database connection established successfully 
[2025-07-26 00:56:50] [INFO] Database connection established successfully 
[2025-07-26 00:56:50] [INFO] Database connection established successfully 
[2025-07-26 00:56:55] [INFO] Database connection established successfully 
[2025-07-26 00:57:19] [INFO] Database connection established successfully 
[2025-07-26 00:57:19] [INFO] Database connection established successfully 
[2025-07-26 00:57:19] [ERROR] Query execution failed {"sql":"\n                    SELECT\n                        c.*,\n                        COALESCE(COUNT(t.id), 0) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume\n                    FROM countries c\n                    LEFT JOIN transfers t ON (c.id = t.sender_country_id OR c.id = t.recipient_country_id)\n                        AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    WHERE (deleted_at IS NULL OR deleted_at = '')\n                    GROUP BY c.id, c.name, c.code, c.currency, c.exchange_rate, c.status, c.flag_url, c.created_at, c.updated_at, c.deleted_at\n                    ORDER BY c.name ASC\n                    LIMIT 10 OFFSET 0\n                ","params":[],"error":"SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'deleted_at' in where clause is ambiguous"}
[2025-07-26 00:57:19] [INFO] Database connection established successfully 
[2025-07-26 00:57:21] [INFO] Database connection established successfully 
[2025-07-26 00:57:25] [INFO] Database connection established successfully 
[2025-07-26 00:57:28] [INFO] Database connection established successfully 
[2025-07-26 00:57:38] [INFO] Database connection established successfully 
[2025-07-26 00:57:38] [INFO] Database connection established successfully 
[2025-07-26 00:57:38] [ERROR] Query execution failed {"sql":"\n                    SELECT\n                        c.*,\n                        COALESCE(COUNT(t.id), 0) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume\n                    FROM countries c\n                    LEFT JOIN transfers t ON (c.id = t.sender_country_id OR c.id = t.recipient_country_id)\n                        AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    WHERE (deleted_at IS NULL OR deleted_at = '')\n                    GROUP BY c.id, c.name, c.code, c.currency, c.exchange_rate, c.status, c.flag_url, c.created_at, c.updated_at, c.deleted_at\n                    ORDER BY c.name ASC\n                    LIMIT 10 OFFSET 0\n                ","params":[],"error":"SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'deleted_at' in where clause is ambiguous"}
[2025-07-26 00:57:38] [INFO] Database connection established successfully 
[2025-07-26 00:57:46] [INFO] Database connection established successfully 
[2025-07-26 00:57:46] [INFO] Database connection established successfully 
[2025-07-26 00:57:51] [INFO] Database connection established successfully 
[2025-07-26 00:58:06] [INFO] Database connection established successfully 
[2025-07-26 00:58:21] [INFO] Database connection established successfully 
[2025-07-26 00:58:52] [INFO] Database connection established successfully 
[2025-07-26 00:59:22] [INFO] Database connection established successfully 
[2025-07-26 00:59:52] [INFO] Database connection established successfully 
[2025-07-26 01:00:07] [INFO] Database connection established successfully 
[2025-07-26 01:00:12] [INFO] Database connection established successfully 
[2025-07-26 01:00:12] [INFO] Database connection established successfully 
[2025-07-26 01:00:12] [INFO] Database connection established successfully 
[2025-07-26 01:00:22] [INFO] Database connection established successfully 
[2025-07-26 01:00:27] [INFO] Database connection established successfully 
[2025-07-26 01:00:27] [INFO] Database connection established successfully 
[2025-07-26 01:00:57] [INFO] Database connection established successfully 
[2025-07-26 01:01:22] [INFO] Database connection established successfully 
[2025-07-26 01:01:22] [INFO] Database connection established successfully 
[2025-07-26 01:01:27] [INFO] Database connection established successfully 
[2025-07-26 01:01:41] [INFO] Database connection established successfully 
[2025-07-26 01:01:41] [INFO] Database connection established successfully 
[2025-07-26 01:01:52] [INFO] Database connection established successfully 
[2025-07-26 01:01:57] [INFO] Database connection established successfully 
[2025-07-26 01:02:22] [INFO] Database connection established successfully 
[2025-07-26 01:02:28] [INFO] Database connection established successfully 
[2025-07-26 01:02:52] [INFO] Database connection established successfully 
[2025-07-26 01:02:58] [INFO] Database connection established successfully 
[2025-07-26 01:03:22] [INFO] Database connection established successfully 
[2025-07-26 01:03:28] [INFO] Database connection established successfully 
[2025-07-26 01:03:52] [INFO] Database connection established successfully 
[2025-07-26 01:03:58] [INFO] Database connection established successfully 
[2025-07-26 01:04:22] [INFO] Database connection established successfully 
[2025-07-26 01:04:28] [INFO] Database connection established successfully 
[2025-07-26 01:04:42] [INFO] Database connection established successfully 
[2025-07-26 01:04:43] [INFO] Database connection established successfully 
[2025-07-26 01:04:53] [INFO] Database connection established successfully 
[2025-07-26 01:04:58] [INFO] Database connection established successfully 
[2025-07-26 01:05:13] [INFO] Database connection established successfully 
[2025-07-26 01:05:23] [INFO] Database connection established successfully 
[2025-07-26 01:05:28] [INFO] Database connection established successfully 
[2025-07-26 01:05:43] [INFO] Database connection established successfully 
[2025-07-26 01:05:54] [INFO] Database connection established successfully 
[2025-07-26 01:05:59] [INFO] Database connection established successfully 
[2025-07-26 01:06:13] [INFO] Database connection established successfully 
[2025-07-26 01:06:13] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:06:24] [INFO] Database connection established successfully 
[2025-07-26 01:06:24] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:06:29] [INFO] Database connection established successfully 
[2025-07-26 01:06:29] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:06:43] [INFO] Database connection established successfully 
[2025-07-26 01:06:43] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:06:44] [INFO] Database connection established successfully 
[2025-07-26 01:06:44] [INFO] Database connection established successfully 
[2025-07-26 01:06:45] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:06:55] [INFO] Database connection established successfully 
[2025-07-26 01:06:55] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:06:59] [INFO] Database connection established successfully 
[2025-07-26 01:06:59] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:07:13] [INFO] Database connection established successfully 
[2025-07-26 01:07:13] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:07:15] [INFO] Database connection established successfully 
[2025-07-26 01:07:15] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:07:25] [INFO] Database connection established successfully 
[2025-07-26 01:07:25] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:07:29] [INFO] Database connection established successfully 
[2025-07-26 01:07:29] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:07:44] [INFO] Database connection established successfully 
[2025-07-26 01:07:44] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:07:45] [INFO] Database connection established successfully 
[2025-07-26 01:07:45] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:07:48] [INFO] Database connection established successfully 
[2025-07-26 01:07:48] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:07:54] [INFO] Database connection established successfully 
[2025-07-26 01:07:54] [INFO] Database connection established successfully 
[2025-07-26 01:07:55] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:07:56] [INFO] Database connection established successfully 
[2025-07-26 01:07:56] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:07:59] [INFO] Database connection established successfully 
[2025-07-26 01:07:59] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:08:14] [INFO] Database connection established successfully 
[2025-07-26 01:08:14] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:08:15] [INFO] Database connection established successfully 
[2025-07-26 01:08:15] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:08:25] [INFO] Database connection established successfully 
[2025-07-26 01:08:25] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:08:26] [INFO] Database connection established successfully 
[2025-07-26 01:08:26] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:08:29] [INFO] Database connection established successfully 
[2025-07-26 01:08:29] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:08:45] [INFO] Database connection established successfully 
[2025-07-26 01:08:45] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:08:45] [INFO] Database connection established successfully 
[2025-07-26 01:08:45] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:08:55] [INFO] Database connection established successfully 
[2025-07-26 01:08:55] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:08:58] [INFO] Database connection established successfully 
[2025-07-26 01:08:58] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:08:59] [INFO] Database connection established successfully 
[2025-07-26 01:08:59] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:09:15] [INFO] Database connection established successfully 
[2025-07-26 01:09:15] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:09:15] [INFO] Database connection established successfully 
[2025-07-26 01:09:15] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:09:26] [INFO] Database connection established successfully 
[2025-07-26 01:09:26] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:09:28] [INFO] Database connection established successfully 
[2025-07-26 01:09:28] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:09:30] [INFO] Database connection established successfully 
[2025-07-26 01:09:30] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:09:43] [INFO] Database connection established successfully 
[2025-07-26 01:09:43] [INFO] Database connection established successfully 
[2025-07-26 01:09:43] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:09:45] [INFO] Database connection established successfully 
[2025-07-26 01:09:45] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:09:45] [INFO] Database connection established successfully 
[2025-07-26 01:09:45] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:09:54] [INFO] Database connection established successfully 
[2025-07-26 01:09:57] [INFO] Database connection established successfully 
[2025-07-26 01:09:58] [INFO] Database connection established successfully 
[2025-07-26 01:09:58] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:10:04] [INFO] Database connection established successfully 
[2025-07-26 01:10:04] [INFO] Database connection established successfully 
[2025-07-26 01:10:10] [INFO] Database connection established successfully 
[2025-07-26 01:10:10] [INFO] Database connection established successfully 
[2025-07-26 01:10:10] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:10:12] [INFO] Database connection established successfully 
[2025-07-26 01:10:12] [INFO] Database connection established successfully 
[2025-07-26 01:10:36] [INFO] Database connection established successfully 
[2025-07-26 01:10:49] [INFO] Database connection established successfully 
[2025-07-26 01:10:49] [INFO] Database connection established successfully 
[2025-07-26 01:10:49] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:11:19] [INFO] Database connection established successfully 
[2025-07-26 01:11:19] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:11:50] [INFO] Database connection established successfully 
[2025-07-26 01:11:50] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:12:20] [INFO] Database connection established successfully 
[2025-07-26 01:12:20] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:12:50] [INFO] Database connection established successfully 
[2025-07-26 01:12:50] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:13:20] [INFO] Database connection established successfully 
[2025-07-26 01:13:20] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:13:50] [INFO] Database connection established successfully 
[2025-07-26 01:13:50] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:14:20] [INFO] Database connection established successfully 
[2025-07-26 01:14:20] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:14:50] [INFO] Database connection established successfully 
[2025-07-26 01:14:50] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:15:20] [INFO] Database connection established successfully 
[2025-07-26 01:15:20] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:15:50] [INFO] Database connection established successfully 
[2025-07-26 01:15:50] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:16:20] [INFO] Database connection established successfully 
[2025-07-26 01:16:20] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:16:39] [INFO] Database connection established successfully 
[2025-07-26 01:16:40] [INFO] Database connection established successfully 
[2025-07-26 01:16:40] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:16:42] [INFO] Database connection established successfully 
[2025-07-26 01:16:42] [INFO] Database connection established successfully 
[2025-07-26 01:16:42] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:17:12] [INFO] Database connection established successfully 
[2025-07-26 01:17:12] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:17:42] [INFO] Database connection established successfully 
[2025-07-26 01:17:42] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:18:12] [INFO] Database connection established successfully 
[2025-07-26 01:18:12] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:18:42] [INFO] Database connection established successfully 
[2025-07-26 01:18:42] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:19:12] [INFO] Database connection established successfully 
[2025-07-26 01:19:12] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:19:32] [INFO] Database connection established successfully 
[2025-07-26 01:19:32] [INFO] Database connection established successfully 
[2025-07-26 01:19:32] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:19:42] [INFO] Database connection established successfully 
[2025-07-26 01:19:42] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:20:12] [INFO] Database connection established successfully 
[2025-07-26 01:20:12] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:20:20] [INFO] Database connection established successfully 
[2025-07-26 01:20:20] [INFO] Database connection established successfully 
[2025-07-26 01:20:20] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:20:44] [INFO] Database connection established successfully 
[2025-07-26 01:20:46] [INFO] Database connection established successfully 
[2025-07-26 01:20:46] [INFO] Database connection established successfully 
[2025-07-26 01:20:46] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:21:50] [INFO] Database connection established successfully 
[2025-07-26 01:21:50] [INFO] Database connection established successfully 
[2025-07-26 01:21:50] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:22:38] [INFO] Database connection established successfully 
[2025-07-26 01:22:39] [INFO] Database connection established successfully 
[2025-07-26 01:22:39] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:22:57] [INFO] Database connection established successfully 
[2025-07-26 01:22:57] [INFO] Database connection established successfully 
[2025-07-26 01:22:57] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:33:09] [INFO] Database connection established successfully 
[2025-07-26 01:33:09] [INFO] Database connection established successfully 
[2025-07-26 01:33:09] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:33:32] [INFO] Database connection established successfully 
[2025-07-26 01:33:34] [INFO] Database connection established successfully 
[2025-07-26 01:33:34] [INFO] Database connection established successfully 
[2025-07-26 01:33:34] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:33:52] [INFO] Database connection established successfully 
[2025-07-26 01:33:53] [INFO] Database connection established successfully 
[2025-07-26 01:33:53] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:33:53] [INFO] Database connection established successfully 
[2025-07-26 01:34:23] [INFO] Database connection established successfully 
[2025-07-26 01:34:23] [INFO] Database connection established successfully 
[2025-07-26 01:34:23] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:34:24] [INFO] Database connection established successfully 
[2025-07-26 01:34:54] [INFO] Database connection established successfully 
[2025-07-26 01:35:01] [INFO] Database connection established successfully 
[2025-07-26 01:45:23] [INFO] Database connection established successfully 
[2025-07-26 01:45:23] [INFO] Database connection established successfully 
[2025-07-26 01:45:23] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:45:23] [INFO] Database connection established successfully 
[2025-07-26 01:45:32] [INFO] Database connection established successfully 
[2025-07-26 01:46:02] [INFO] Database connection established successfully 
[2025-07-26 01:46:32] [INFO] Database connection established successfully 
[2025-07-26 01:46:52] [INFO] Database connection established successfully 
[2025-07-26 01:46:53] [INFO] Database connection established successfully 
[2025-07-26 01:46:53] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:46:53] [INFO] Database connection established successfully 
[2025-07-26 01:47:03] [INFO] Database connection established successfully 
[2025-07-26 01:47:33] [INFO] Database connection established successfully 
[2025-07-26 01:48:03] [INFO] Database connection established successfully 
[2025-07-26 01:48:34] [INFO] Database connection established successfully 
[2025-07-26 01:48:59] [INFO] Database connection established successfully 
[2025-07-26 01:49:03] [INFO] Database connection established successfully 
[2025-07-26 01:49:05] [INFO] Database connection established successfully 
[2025-07-26 01:49:35] [INFO] Database connection established successfully 
[2025-07-26 01:50:06] [INFO] Database connection established successfully 
[2025-07-26 01:50:36] [INFO] Database connection established successfully 
[2025-07-26 01:51:06] [INFO] Database connection established successfully 
[2025-07-26 01:51:36] [INFO] Database connection established successfully 
[2025-07-26 01:52:06] [INFO] Database connection established successfully 
[2025-07-26 01:52:11] [INFO] User logout {"user_id":1,"user_name":"System Administrator","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0"}
[2025-07-26 01:52:11] [INFO] User logged out successfully {"user_id":1,"email":"<EMAIL>"}
[2025-07-26 01:52:13] [INFO] User logged out successfully {"user_id":null,"email":""}
[2025-07-26 01:52:14] [INFO] User logged out successfully {"user_id":null,"email":""}
[2025-07-26 01:52:30] [INFO] Database connection established successfully 
[2025-07-26 01:52:30] [INFO] User logged in successfully {"user_id":1,"email":"<EMAIL>","role":"admin"}
[2025-07-26 01:52:30] [INFO] Auto-login performed for admin user 
[2025-07-26 01:52:36] [INFO] Database connection established successfully 
[2025-07-26 01:52:38] [INFO] Database connection established successfully 
[2025-07-26 01:52:38] [INFO] Database connection established successfully 
[2025-07-26 01:52:38] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:52:38] [INFO] Database connection established successfully 
[2025-07-26 01:53:08] [INFO] Database connection established successfully 
[2025-07-26 01:53:18] [INFO] Database connection established successfully 
[2025-07-26 01:55:33] [INFO] Database connection established successfully 
[2025-07-26 01:55:33] [ERROR] Query execution failed {"sql":"INSERT INTO transfers (transfer_code, user_id, sender_name, sender_phone, sender_country_id, recipient_name, recipient_phone, recipient_country_id, amount, fees, total_amount, exchange_rate, status, payment_method, notes) VALUES (:transfer_code, :user_id, :sender_name, :sender_phone, :sender_country_id, :recipient_name, :recipient_phone, :recipient_country_id, :amount, :fees, :total_amount, :exchange_rate, :status, :payment_method, :notes)","params":{"transfer_code":"TR202507266897","user_id":1,"sender_name":"محمد يوسف محمد","sender_phone":"+1665577441","sender_country_id":"21","recipient_name":"عمر يوسف محمد","recipient_phone":"+201234567890","recipient_country_id":"9","amount":100,"fees":1,"total_amount":101,"exchange_rate":1,"status":"pending","payment_method":"card","notes":""},"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:57:00] [INFO] Database connection established successfully 
[2025-07-26 01:57:50] [INFO] Database connection established successfully 
[2025-07-26 01:57:51] [INFO] Database connection established successfully 
[2025-07-26 01:57:52] [INFO] Database connection established successfully 
[2025-07-26 01:58:53] [INFO] Database connection established successfully 
[2025-07-26 01:58:53] [INFO] Database connection established successfully 
[2025-07-26 01:58:53] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 01:58:53] [INFO] Database connection established successfully 
[2025-07-26 02:00:20] [INFO] Database connection established successfully 
[2025-07-26 02:00:23] [INFO] Database connection established successfully 
[2025-07-26 02:03:57] [INFO] Database connection established successfully 
[2025-07-26 02:03:58] [INFO] Database connection established successfully 
[2025-07-26 02:03:58] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 02:03:58] [INFO] Database connection established successfully 
[2025-07-26 02:04:15] [INFO] Database connection established successfully 
[2025-07-26 02:04:37] [INFO] Database connection established successfully 
[2025-07-26 02:04:37] [INFO] Database connection established successfully 
[2025-07-26 02:04:37] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 02:04:37] [INFO] Database connection established successfully 
[2025-07-26 02:05:15] [INFO] Database connection established successfully 
[2025-07-26 02:05:15] [INFO] Database connection established successfully 
[2025-07-26 02:05:15] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 02:05:15] [INFO] Database connection established successfully 
[2025-07-26 02:05:35] [INFO] Database connection established successfully 
[2025-07-26 02:06:07] [INFO] Database connection established successfully 
[2025-07-26 02:06:15] [INFO] Database connection established successfully 
[2025-07-26 02:06:18] [INFO] Database connection established successfully 
[2025-07-26 02:06:39] [INFO] Database connection established successfully 
[2025-07-26 02:06:39] [INFO] Database connection established successfully 
[2025-07-26 02:06:39] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 02:06:40] [INFO] Database connection established successfully 
[2025-07-26 02:07:15] [INFO] Database connection established successfully 
[2025-07-26 02:11:54] [INFO] Database connection established successfully 
[2025-07-26 02:11:55] [INFO] Database connection established successfully 
[2025-07-26 02:11:55] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 02:11:55] [INFO] Database connection established successfully 
[2025-07-26 02:13:21] [INFO] Database connection established successfully 
[2025-07-26 02:13:22] [INFO] Database connection established successfully 
[2025-07-26 02:13:22] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 02:13:22] [INFO] Database connection established successfully 
[2025-07-26 02:14:05] [INFO] Database connection established successfully 
[2025-07-26 02:14:05] [INFO] Database connection established successfully 
[2025-07-26 02:14:05] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 02:14:05] [INFO] Database connection established successfully 
[2025-07-26 02:14:57] [INFO] Database connection established successfully 
[2025-07-26 02:14:57] [INFO] Database connection established successfully 
[2025-07-26 02:14:57] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 02:14:57] [INFO] Database connection established successfully 
[2025-07-26 02:15:27] [INFO] Database connection established successfully 
[2025-07-26 02:15:27] [INFO] Database connection established successfully 
[2025-07-26 02:15:27] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 02:15:27] [INFO] Database connection established successfully 
[2025-07-26 02:18:22] [INFO] Database connection established successfully 
[2025-07-26 02:18:22] [INFO] Database connection established successfully 
[2025-07-26 02:18:22] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 02:18:22] [INFO] Database connection established successfully 
[2025-07-26 02:21:04] [INFO] Database connection established successfully 
[2025-07-26 02:21:04] [INFO] Database connection established successfully 
[2025-07-26 02:21:04] [ERROR] Query execution failed {"sql":"SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'fees' in 'field list'"}
[2025-07-26 02:21:04] [INFO] Database connection established successfully 
