[2025-07-26 00:00:03] [INFO] Database connection established successfully 
[2025-07-26 00:00:03] [INFO] Database connection established successfully 
[2025-07-26 00:00:14] [INFO] Database connection established successfully 
[2025-07-26 00:00:34] [INFO] Database connection established successfully 
[2025-07-26 00:00:44] [INFO] Database connection established successfully 
[2025-07-26 00:01:05] [INFO] Database connection established successfully 
[2025-07-26 00:01:14] [INFO] Database connection established successfully 
[2025-07-26 00:01:35] [INFO] Database connection established successfully 
[2025-07-26 00:01:44] [INFO] Database connection established successfully 
[2025-07-26 00:01:56] [INFO] Database connection established successfully 
[2025-07-26 00:02:15] [INFO] Database connection established successfully 
[2025-07-26 00:02:45] [INFO] Database connection established successfully 
[2025-07-26 00:03:15] [INFO] Database connection established successfully 
[2025-07-26 00:03:17] [INFO] Database connection established successfully 
[2025-07-26 00:03:17] [INFO] Database connection established successfully 
[2025-07-26 00:03:45] [INFO] Database connection established successfully 
[2025-07-26 00:03:51] [INFO] Database connection established successfully 
[2025-07-26 00:03:51] [INFO] Database connection established successfully 
[2025-07-26 00:04:07] [INFO] Database connection established successfully 
[2025-07-26 00:04:07] [INFO] Database connection established successfully 
[2025-07-26 00:04:15] [INFO] Database connection established successfully 
[2025-07-26 00:04:37] [INFO] Database connection established successfully 
[2025-07-26 00:04:45] [INFO] Database connection established successfully 
[2025-07-26 00:05:07] [INFO] Database connection established successfully 
[2025-07-26 00:05:16] [INFO] Database connection established successfully 
[2025-07-26 00:05:38] [INFO] Database connection established successfully 
[2025-07-26 00:05:41] [INFO] Database connection established successfully 
[2025-07-26 00:05:41] [INFO] Database connection established successfully 
[2025-07-26 00:05:46] [INFO] Database connection established successfully 
[2025-07-26 00:06:01] [INFO] Database connection established successfully 
[2025-07-26 00:06:03] [INFO] Database connection established successfully 
[2025-07-26 00:06:11] [INFO] Database connection established successfully 
[2025-07-26 00:06:16] [INFO] Database connection established successfully 
[2025-07-26 00:06:41] [INFO] Database connection established successfully 
[2025-07-26 00:06:46] [INFO] Database connection established successfully 
[2025-07-26 00:07:11] [INFO] Database connection established successfully 
[2025-07-26 00:07:15] [INFO] Database connection established successfully 
[2025-07-26 00:07:15] [INFO] Database connection established successfully 
[2025-07-26 00:07:17] [INFO] Database connection established successfully 
[2025-07-26 00:07:45] [INFO] Database connection established successfully 
[2025-07-26 00:07:47] [INFO] Database connection established successfully 
[2025-07-26 00:08:15] [INFO] Database connection established successfully 
[2025-07-26 00:08:18] [INFO] Database connection established successfully 
[2025-07-26 00:08:45] [INFO] Database connection established successfully 
[2025-07-26 00:08:48] [INFO] Database connection established successfully 
[2025-07-26 00:09:15] [INFO] Database connection established successfully 
[2025-07-26 00:09:18] [INFO] Database connection established successfully 
[2025-07-26 00:09:45] [INFO] Database connection established successfully 
[2025-07-26 00:09:48] [INFO] Database connection established successfully 
[2025-07-26 00:10:15] [INFO] Database connection established successfully 
[2025-07-26 00:10:17] [INFO] Database connection established successfully 
[2025-07-26 00:10:18] [INFO] Database connection established successfully 
[2025-07-26 00:10:18] [INFO] Database connection established successfully 
[2025-07-26 00:10:45] [INFO] Database connection established successfully 
[2025-07-26 00:10:48] [INFO] Database connection established successfully 
[2025-07-26 00:10:48] [INFO] Database connection established successfully 
[2025-07-26 00:10:54] [INFO] Database connection established successfully 
[2025-07-26 00:10:54] [INFO] Database connection established successfully 
[2025-07-26 00:11:03] [INFO] Database connection established successfully 
[2025-07-26 00:11:03] [INFO] Database connection established successfully 
[2025-07-26 00:11:05] [INFO] Database connection established successfully 
[2025-07-26 00:11:05] [INFO] Database connection established successfully 
[2025-07-26 00:11:19] [INFO] Database connection established successfully 
[2025-07-26 00:11:35] [INFO] Database connection established successfully 
[2025-07-26 00:11:49] [INFO] Database connection established successfully 
[2025-07-26 00:12:05] [INFO] Database connection established successfully 
[2025-07-26 00:12:19] [INFO] Database connection established successfully 
[2025-07-26 00:12:35] [INFO] Database connection established successfully 
[2025-07-26 00:12:44] [INFO] Database connection established successfully 
[2025-07-26 00:12:44] [INFO] Database connection established successfully 
[2025-07-26 00:12:45] [INFO] Database connection established successfully 
[2025-07-26 00:12:45] [INFO] Database connection established successfully 
[2025-07-26 00:12:45] [INFO] Database connection established successfully 
[2025-07-26 00:12:46] [INFO] Database connection established successfully 
[2025-07-26 00:12:46] [INFO] Database connection established successfully 
[2025-07-26 00:12:49] [INFO] Database connection established successfully 
[2025-07-26 00:13:04] [INFO] Database connection established successfully 
[2025-07-26 00:13:12] [INFO] Database connection established successfully 
[2025-07-26 00:13:12] [INFO] Database connection established successfully 
[2025-07-26 00:13:15] [INFO] Database connection established successfully 
[2025-07-26 00:13:17] [INFO] Database connection established successfully 
[2025-07-26 00:13:17] [INFO] Database connection established successfully 
[2025-07-26 00:13:19] [INFO] Database connection established successfully 
[2025-07-26 00:13:19] [INFO] Database connection established successfully 
[2025-07-26 00:13:22] [INFO] Database connection established successfully 
[2025-07-26 00:13:22] [INFO] Database connection established successfully 
[2025-07-26 00:13:26] [INFO] Database connection established successfully 
[2025-07-26 00:13:29] [INFO] Database connection established successfully 
[2025-07-26 00:13:29] [INFO] Database connection established successfully 
[2025-07-26 00:13:37] [INFO] Database connection established successfully 
[2025-07-26 00:13:38] [INFO] Database connection established successfully 
[2025-07-26 00:13:50] [INFO] Database connection established successfully 
[2025-07-26 00:14:20] [INFO] Database connection established successfully 
[2025-07-26 00:14:50] [INFO] Database connection established successfully 
[2025-07-26 00:15:20] [INFO] Database connection established successfully 
[2025-07-26 00:15:42] [INFO] Database connection established successfully 
[2025-07-26 00:15:42] [INFO] Database connection established successfully 
[2025-07-26 00:15:50] [INFO] Database connection established successfully 
[2025-07-26 00:15:54] [INFO] Database connection established successfully 
[2025-07-26 00:16:02] [INFO] Database connection established successfully 
[2025-07-26 00:16:09] [INFO] Database connection established successfully 
[2025-07-26 00:16:11] [INFO] Database connection established successfully 
[2025-07-26 00:16:11] [INFO] Database connection established successfully 
[2025-07-26 00:16:20] [INFO] Database connection established successfully 
[2025-07-26 00:16:41] [INFO] Database connection established successfully 
[2025-07-26 00:16:50] [INFO] Database connection established successfully 
[2025-07-26 00:17:11] [INFO] Database connection established successfully 
[2025-07-26 00:17:20] [INFO] Database connection established successfully 
[2025-07-26 00:17:41] [INFO] Database connection established successfully 
[2025-07-26 00:17:50] [INFO] Database connection established successfully 
[2025-07-26 00:18:11] [INFO] Database connection established successfully 
[2025-07-26 00:18:21] [INFO] Database connection established successfully 
[2025-07-26 00:18:41] [INFO] Database connection established successfully 
[2025-07-26 00:18:51] [INFO] Database connection established successfully 
[2025-07-26 00:19:13] [INFO] Database connection established successfully 
[2025-07-26 00:19:14] [INFO] Database connection established successfully 
[2025-07-26 00:19:14] [INFO] Database connection established successfully 
[2025-07-26 00:19:17] [INFO] Database connection established successfully 
[2025-07-26 00:19:17] [INFO] Database connection established successfully 
[2025-07-26 00:19:43] [INFO] Database connection established successfully 
[2025-07-26 00:19:47] [INFO] Database connection established successfully 
[2025-07-26 00:20:13] [INFO] Database connection established successfully 
[2025-07-26 00:20:17] [INFO] Database connection established successfully 
[2025-07-26 00:20:44] [INFO] Database connection established successfully 
[2025-07-26 00:20:47] [INFO] Database connection established successfully 
[2025-07-26 00:20:49] [INFO] User logged out successfully {"user_id":1,"email":"<EMAIL>"}
[2025-07-26 00:21:14] [INFO] Database connection established successfully 
[2025-07-26 00:21:14] [INFO] User logged in successfully {"user_id":1,"email":"<EMAIL>","role":"admin"}
[2025-07-26 00:21:14] [INFO] Auto-login performed for admin user 
[2025-07-26 00:21:17] [INFO] Database connection established successfully 
[2025-07-26 00:21:32] [INFO] Database connection established successfully 
[2025-07-26 00:21:32] [INFO] Database connection established successfully 
[2025-07-26 00:21:43] [INFO] Database connection established successfully 
[2025-07-26 00:21:44] [INFO] User logout {"user_id":1,"user_name":"System Administrator","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0"}
[2025-07-26 00:21:44] [INFO] User logged out successfully {"user_id":1,"email":"<EMAIL>"}
[2025-07-26 00:21:47] [INFO] Database connection established successfully 
[2025-07-26 00:21:47] [INFO] User logged in successfully {"user_id":1,"email":"<EMAIL>","role":"admin"}
[2025-07-26 00:21:47] [INFO] Auto-login performed for admin user 
[2025-07-26 00:21:50] [INFO] Database connection established successfully 
[2025-07-26 00:21:54] [INFO] User logout {"user_id":1,"user_name":"System Administrator","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0"}
[2025-07-26 00:21:54] [INFO] User logged out successfully {"user_id":1,"email":"<EMAIL>"}
[2025-07-26 00:21:57] [INFO] User logged out successfully {"user_id":null,"email":""}
[2025-07-26 00:21:58] [INFO] User logged out successfully {"user_id":null,"email":""}
[2025-07-26 00:22:01] [INFO] User logged out successfully {"user_id":null,"email":""}
[2025-07-26 00:22:20] [INFO] Database connection established successfully 
[2025-07-26 00:22:20] [INFO] User logged in successfully {"user_id":1,"email":"<EMAIL>","role":"admin"}
[2025-07-26 00:22:20] [INFO] Auto-login performed for admin user 
[2025-07-26 00:22:32] [INFO] Database connection established successfully 
[2025-07-26 00:22:44] [INFO] Database connection established successfully 
[2025-07-26 00:22:44] [INFO] Database connection established successfully 
[2025-07-26 00:22:55] [INFO] User logged out successfully {"user_id":1,"email":"<EMAIL>"}
[2025-07-26 00:22:57] [INFO] Database connection established successfully 
[2025-07-26 00:22:58] [INFO] User logged in successfully {"user_id":1,"email":"<EMAIL>","role":"admin"}
[2025-07-26 00:22:58] [INFO] Auto-login performed for admin user 
[2025-07-26 00:23:03] [INFO] Database connection established successfully 
[2025-07-26 00:23:03] [INFO] Database connection established successfully 
[2025-07-26 00:23:23] [INFO] Database connection established successfully 
[2025-07-26 00:23:24] [INFO] Database connection established successfully 
[2025-07-26 00:23:28] [INFO] Database connection established successfully 
[2025-07-26 00:23:31] [INFO] Database connection established successfully 
[2025-07-26 00:24:01] [INFO] Database connection established successfully 
[2025-07-26 00:24:02] [INFO] Database connection established successfully 
[2025-07-26 00:24:18] [INFO] User logged out successfully {"user_id":1,"email":"<EMAIL>"}
[2025-07-26 00:24:21] [INFO] Database connection established successfully 
[2025-07-26 00:24:21] [INFO] User logged in successfully {"user_id":1,"email":"<EMAIL>","role":"admin"}
[2025-07-26 00:24:21] [INFO] Auto-login performed for admin user 
[2025-07-26 00:24:37] [INFO] Database connection established successfully 
[2025-07-26 00:24:37] [INFO] Database connection established successfully 
[2025-07-26 00:25:07] [INFO] Database connection established successfully 
[2025-07-26 00:25:37] [INFO] Database connection established successfully 
[2025-07-26 00:25:57] [INFO] Database connection established successfully 
[2025-07-26 00:25:58] [INFO] Database connection established successfully 
[2025-07-26 00:26:28] [INFO] Database connection established successfully 
[2025-07-26 00:26:58] [INFO] Database connection established successfully 
[2025-07-26 00:27:28] [INFO] Database connection established successfully 
[2025-07-26 00:27:58] [INFO] Database connection established successfully 
[2025-07-26 00:28:28] [INFO] Database connection established successfully 
[2025-07-26 00:28:58] [INFO] Database connection established successfully 
[2025-07-26 00:29:28] [INFO] Database connection established successfully 
[2025-07-26 00:29:58] [INFO] Database connection established successfully 
[2025-07-26 00:30:28] [INFO] Database connection established successfully 
[2025-07-26 00:30:46] [INFO] Database connection established successfully 
[2025-07-26 00:30:46] [INFO] Database connection established successfully 
[2025-07-26 00:30:48] [INFO] Database connection established successfully 
[2025-07-26 00:30:48] [INFO] Database connection established successfully 
[2025-07-26 00:31:18] [INFO] Database connection established successfully 
[2025-07-26 00:31:48] [INFO] Database connection established successfully 
[2025-07-26 00:32:18] [INFO] Database connection established successfully 
[2025-07-26 00:32:48] [INFO] Database connection established successfully 
[2025-07-26 00:33:18] [INFO] Database connection established successfully 
[2025-07-26 00:33:48] [INFO] Database connection established successfully 
[2025-07-26 00:34:18] [INFO] Database connection established successfully 
[2025-07-26 00:34:48] [INFO] Database connection established successfully 
[2025-07-26 00:35:18] [INFO] Database connection established successfully 
[2025-07-26 00:35:30] [INFO] Database connection established successfully 
[2025-07-26 00:35:31] [INFO] Database connection established successfully 
[2025-07-26 00:35:49] [INFO] Database connection established successfully 
[2025-07-26 00:36:01] [INFO] Database connection established successfully 
[2025-07-26 00:36:19] [INFO] Database connection established successfully 
[2025-07-26 00:36:31] [INFO] Database connection established successfully 
[2025-07-26 00:36:49] [INFO] Database connection established successfully 
[2025-07-26 00:37:01] [INFO] Database connection established successfully 
[2025-07-26 00:37:19] [INFO] Database connection established successfully 
[2025-07-26 00:37:31] [INFO] Database connection established successfully 
[2025-07-26 00:37:46] [INFO] Database connection established successfully 
[2025-07-26 00:37:46] [INFO] Database connection established successfully 
[2025-07-26 00:37:49] [INFO] Database connection established successfully 
[2025-07-26 00:38:00] [INFO] Database connection established successfully 
[2025-07-26 00:38:00] [INFO] Database connection established successfully 
[2025-07-26 00:38:00] [ERROR] Query execution failed {"sql":"\n                    SELECT \n                        c.name as country_name,\n                        COUNT(t.id) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume,\n                        ROUND(COUNT(t.id) * 100.0 \/ NULLIF((SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN :start_date AND :end_date AND (deleted_at IS NULL OR deleted_at = '')), 0), 2) as percentage\n                    FROM transfers t\n                    LEFT JOIN countries c ON t.sender_country_id = c.id\n                    WHERE DATE(t.created_at) BETWEEN :start_date AND :end_date\n                    AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    GROUP BY c.id, c.name\n                    ORDER BY transfer_count DESC\n                    LIMIT 10\n                ","params":{"start_date":"2025-07-19","end_date":"2025-07-26"},"error":"SQLSTATE[HY093]: Invalid parameter number"}
[2025-07-26 00:38:09] [INFO] Database connection established successfully 
[2025-07-26 00:38:09] [INFO] Database connection established successfully 
[2025-07-26 00:38:09] [ERROR] Query execution failed {"sql":"\n                    SELECT \n                        c.name as country_name,\n                        COUNT(t.id) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume,\n                        ROUND(COUNT(t.id) * 100.0 \/ NULLIF((SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN :start_date AND :end_date AND (deleted_at IS NULL OR deleted_at = '')), 0), 2) as percentage\n                    FROM transfers t\n                    LEFT JOIN countries c ON t.sender_country_id = c.id\n                    WHERE DATE(t.created_at) BETWEEN :start_date AND :end_date\n                    AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    GROUP BY c.id, c.name\n                    ORDER BY transfer_count DESC\n                    LIMIT 10\n                ","params":{"start_date":"2025-07-19","end_date":"2025-07-26"},"error":"SQLSTATE[HY093]: Invalid parameter number"}
[2025-07-26 00:38:17] [INFO] Database connection established successfully 
[2025-07-26 00:38:17] [INFO] Database connection established successfully 
[2025-07-26 00:38:17] [ERROR] Query execution failed {"sql":"\n                    SELECT \n                        c.name as country_name,\n                        COUNT(t.id) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume,\n                        ROUND(COUNT(t.id) * 100.0 \/ NULLIF((SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN :start_date AND :end_date AND (deleted_at IS NULL OR deleted_at = '')), 0), 2) as percentage\n                    FROM transfers t\n                    LEFT JOIN countries c ON t.sender_country_id = c.id\n                    WHERE DATE(t.created_at) BETWEEN :start_date AND :end_date\n                    AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    GROUP BY c.id, c.name\n                    ORDER BY transfer_count DESC\n                    LIMIT 10\n                ","params":{"start_date":"2025-07-19","end_date":"2025-07-26"},"error":"SQLSTATE[HY093]: Invalid parameter number"}
[2025-07-26 00:38:19] [INFO] Database connection established successfully 
[2025-07-26 00:38:27] [INFO] Database connection established successfully 
[2025-07-26 00:38:27] [INFO] Database connection established successfully 
[2025-07-26 00:38:37] [INFO] Database connection established successfully 
[2025-07-26 00:38:37] [INFO] Database connection established successfully 
[2025-07-26 00:38:49] [INFO] Database connection established successfully 
[2025-07-26 00:38:57] [INFO] Database connection established successfully 
[2025-07-26 00:38:57] [INFO] Database connection established successfully 
[2025-07-26 00:39:21] [INFO] Database connection established successfully 
[2025-07-26 00:39:27] [INFO] Database connection established successfully 
[2025-07-26 00:39:40] [INFO] Database connection established successfully 
[2025-07-26 00:39:41] [INFO] Database connection established successfully 
[2025-07-26 00:39:45] [INFO] Database connection established successfully 
[2025-07-26 00:39:45] [INFO] Database connection established successfully 
[2025-07-26 00:39:45] [ERROR] Query execution failed {"sql":"\n                    SELECT \n                        c.*,\n                        COUNT(t.id) as transfer_count,\n                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume\n                    FROM countries c\n                    LEFT JOIN transfers t ON (c.id = t.sender_country_id OR c.id = t.recipient_country_id)\n                        AND (t.deleted_at IS NULL OR t.deleted_at = '')\n                    WHERE (deleted_at IS NULL OR deleted_at = '')\n                    GROUP BY c.id\n                    ORDER BY c.name ASC\n                    LIMIT 10 OFFSET 0\n                ","params":[],"error":"SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'deleted_at' in where clause is ambiguous"}
[2025-07-26 00:39:45] [INFO] Database connection established successfully 
[2025-07-26 00:39:45] [ERROR] Query execution failed {"sql":"SELECT COUNT(*) as count FROM countries WHERE status = 'active' AND (deleted_at IS NULL OR deleted_at = '')","params":[],"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause'"}
[2025-07-26 00:39:51] [INFO] Database connection established successfully 
[2025-07-26 00:40:21] [INFO] Database connection established successfully 
[2025-07-26 00:40:51] [INFO] Database connection established successfully 
