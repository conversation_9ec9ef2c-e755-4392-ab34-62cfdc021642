<?php

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';
// Load session helper


header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Connect to database
    $db = new PDO('sqlite:' . __DIR__ . '/../database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get all countries with additional statistics
    $countries = $db->query("
        SELECT 
            c.*,
            COALESCE(sender_stats.sender_count, 0) as transfers_as_sender,
            COALESCE(receiver_stats.receiver_count, 0) as transfers_as_receiver,
            COALESCE(sender_stats.total_sent, 0) as total_amount_sent,
            COALESCE(receiver_stats.total_received, 0) as total_amount_received
        FROM countries c
        LEFT JOIN (
            SELECT 
                sender_country_id,
                COUNT(*) as sender_count,
                SUM(amount) as total_sent
            FROM transfers 
            WHERE status = 'completed'
            GROUP BY sender_country_id
        ) sender_stats ON c.id = sender_stats.sender_country_id
        LEFT JOIN (
            SELECT 
                receiver_country_id,
                COUNT(*) as receiver_count,
                SUM(converted_amount) as total_received
            FROM transfers 
            WHERE status = 'completed'
            GROUP BY receiver_country_id
        ) receiver_stats ON c.id = receiver_stats.receiver_country_id
        ORDER BY c.name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // Format the data
    $formattedCountries = [];
    foreach ($countries as $country) {
        $formattedCountries[] = [
            'id' => (int)$country['id'],
            'name' => $country['name'],
            'code' => $country['code'],
            'currency' => $country['currency'],
            'flag_url' => $country['flag_url'],
            'is_active' => (bool)$country['is_active'],
            'created_at' => $country['created_at'],
            'statistics' => [
                'transfers_as_sender' => (int)$country['transfers_as_sender'],
                'transfers_as_receiver' => (int)$country['transfers_as_receiver'],
                'total_transfers' => (int)$country['transfers_as_sender'] + (int)$country['transfers_as_receiver'],
                'total_amount_sent' => (float)$country['total_amount_sent'],
                'total_amount_received' => (float)$country['total_amount_received']
            ]
        ];
    }
    
    // Get exchange rates for active countries
    $exchangeRates = $db->query("
        SELECT from_currency, to_currency, rate, provider, created_at
        FROM exchange_rates 
        ORDER BY from_currency, to_currency
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // Group exchange rates by currency
    $ratesByCurrency = [];
    foreach ($exchangeRates as $rate) {
        if (!isset($ratesByCurrency[$rate['from_currency']])) {
            $ratesByCurrency[$rate['from_currency']] = [];
        }
        $ratesByCurrency[$rate['from_currency']][$rate['to_currency']] = [
            'rate' => (float)$rate['rate'],
            'provider' => $rate['provider'],
            'updated_at' => $rate['created_at']
        ];
    }
    
    // Get country statistics
    $stats = [
        'total_countries' => count($countries),
        'active_countries' => count(array_filter($countries, function($c) { return $c['is_active']; })),
        'inactive_countries' => count(array_filter($countries, function($c) { return !$c['is_active']; })),
        'countries_with_transfers' => count(array_filter($countries, function($c) { 
            return $c['transfers_as_sender'] > 0 || $c['transfers_as_receiver'] > 0; 
        })),
        'total_currencies' => count(array_unique(array_column($countries, 'currency'))),
        'most_active_sender' => null,
        'most_active_receiver' => null
    ];
    
    // Find most active countries
    if (!empty($formattedCountries)) {
        $mostActiveSender = array_reduce($formattedCountries, function($carry, $country) {
            return (!$carry || $country['statistics']['transfers_as_sender'] > $carry['statistics']['transfers_as_sender']) 
                ? $country : $carry;
        });
        
        $mostActiveReceiver = array_reduce($formattedCountries, function($carry, $country) {
            return (!$carry || $country['statistics']['transfers_as_receiver'] > $carry['statistics']['transfers_as_receiver']) 
                ? $country : $carry;
        });
        
        $stats['most_active_sender'] = [
            'name' => $mostActiveSender['name'],
            'code' => $mostActiveSender['code'],
            'transfers' => $mostActiveSender['statistics']['transfers_as_sender']
        ];
        
        $stats['most_active_receiver'] = [
            'name' => $mostActiveReceiver['name'],
            'code' => $mostActiveReceiver['code'],
            'transfers' => $mostActiveReceiver['statistics']['transfers_as_receiver']
        ];
    }
    
    $response = [
        'success' => true,
        'data' => [
            'countries' => $formattedCountries,
            'exchange_rates' => $ratesByCurrency,
            'statistics' => $stats
        ],
        'count' => count($formattedCountries),
        'timestamp' => date('Y-m-d H:i:s'),
        'generated_by' => is_logged_in() ? get_user_data()['name'] : 'Anonymous'
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
