<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Transfer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'transfer_code',
        'sender_id',
        'receiver_id',
        'agent_id',
        'branch_id',
        'sender_country_id',
        'receiver_country_id',
        'sender_currency_id',
        'receiver_currency_id',
        'amount',
        'exchange_rate',
        'converted_amount',
        'fee_amount',
        'total_amount',
        'purpose',
        'notes',
        'status',
        'type',
        'priority',
        'scheduled_at',
        'completed_at',
        'cancelled_at',
        'cancellation_reason',
        'pickup_code',
        'pickup_location',
        'receiver_name',
        'receiver_phone',
        'receiver_address',
        'receiver_id_number',
        'sender_name',
        'sender_phone',
        'sender_address',
        'sender_id_number',
        'is_anonymous',
        'risk_score',
        'fraud_check_status',
        'compliance_status',
        'attachments',
        'tracking_data',
        'ai_recommendations',
        'commission_amount',
        'profit_amount',
        'external_reference',
        'swift_code',
        'bank_details',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:6',
        'converted_amount' => 'decimal:2',
        'fee_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'profit_amount' => 'decimal:2',
        'scheduled_at' => 'datetime',
        'completed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'is_anonymous' => 'boolean',
        'risk_score' => 'integer',
        'attachments' => 'array',
        'tracking_data' => 'array',
        'ai_recommendations' => 'array',
        'bank_details' => 'array',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_READY_FOR_PICKUP = 'ready_for_pickup';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_FAILED = 'failed';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_ON_HOLD = 'on_hold';

    // Type constants
    const TYPE_INDIVIDUAL = 'individual';
    const TYPE_BULK = 'bulk';
    const TYPE_SCHEDULED = 'scheduled';
    const TYPE_ANONYMOUS = 'anonymous';
    const TYPE_BUSINESS = 'business';

    // Priority constants
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    // Fraud check status
    const FRAUD_CHECK_PENDING = 'pending';
    const FRAUD_CHECK_APPROVED = 'approved';
    const FRAUD_CHECK_FLAGGED = 'flagged';
    const FRAUD_CHECK_REJECTED = 'rejected';

    // Compliance status
    const COMPLIANCE_PENDING = 'pending';
    const COMPLIANCE_APPROVED = 'approved';
    const COMPLIANCE_REJECTED = 'rejected';
    const COMPLIANCE_UNDER_REVIEW = 'under_review';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transfer) {
            if (empty($transfer->transfer_code)) {
                $transfer->transfer_code = $transfer->generateTransferCode();
            }
            if (empty($transfer->pickup_code)) {
                $transfer->pickup_code = $transfer->generatePickupCode();
            }
        });
    }

    /**
     * Generate unique transfer code
     */
    public function generateTransferCode(): string
    {
        do {
            $code = 'TRF' . date('Ymd') . strtoupper(Str::random(6));
        } while (self::where('transfer_code', $code)->exists());

        return $code;
    }

    /**
     * Generate pickup code
     */
    public function generatePickupCode(): string
    {
        return strtoupper(Str::random(8));
    }

    /**
     * Get the sender
     */
    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Get the receiver
     */
    public function receiver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    /**
     * Get the handling agent
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'agent_id');
    }

    /**
     * Get the branch
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get sender country
     */
    public function senderCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'sender_country_id');
    }

    /**
     * Get receiver country
     */
    public function receiverCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'receiver_country_id');
    }

    /**
     * Get sender currency
     */
    public function senderCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'sender_currency_id');
    }

    /**
     * Get receiver currency
     */
    public function receiverCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'receiver_currency_id');
    }

    /**
     * Get transfer status history
     */
    public function statusHistory(): HasMany
    {
        return $this->hasMany(TransferStatusHistory::class);
    }

    /**
     * Get transfer fees
     */
    public function fees(): HasMany
    {
        return $this->hasMany(TransferFee::class);
    }

    /**
     * Get transfer disputes
     */
    public function disputes(): HasMany
    {
        return $this->hasMany(TransferDispute::class);
    }

    /**
     * Check if transfer can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, [
            self::STATUS_PENDING,
            self::STATUS_PROCESSING,
        ]);
    }

    /**
     * Check if transfer is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if transfer is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Update transfer status
     */
    public function updateStatus(string $status, string $notes = null, User $updatedBy = null): void
    {
        $oldStatus = $this->status;
        
        $this->update([
            'status' => $status,
            'completed_at' => $status === self::STATUS_COMPLETED ? now() : null,
            'cancelled_at' => $status === self::STATUS_CANCELLED ? now() : null,
        ]);

        // Record status change in history
        $this->statusHistory()->create([
            'old_status' => $oldStatus,
            'new_status' => $status,
            'notes' => $notes,
            'updated_by' => $updatedBy?->id,
        ]);

        // Send notifications based on status
        $this->sendStatusNotification($status);
    }

    /**
     * Send status notification
     */
    private function sendStatusNotification(string $status): void
    {
        // Implementation for sending notifications
        // This would integrate with notification service
    }

    /**
     * Calculate total fees
     */
    public function calculateFees(): float
    {
        return $this->fees()->sum('amount');
    }

    /**
     * Get tracking timeline
     */
    public function getTrackingTimeline(): array
    {
        return $this->statusHistory()
            ->with('updatedBy')
            ->orderBy('created_at')
            ->get()
            ->map(function ($history) {
                return [
                    'status' => $history->new_status,
                    'timestamp' => $history->created_at,
                    'notes' => $history->notes,
                    'updated_by' => $history->updatedBy?->name,
                ];
            })
            ->toArray();
    }

    /**
     * Generate QR code data
     */
    public function getQrCodeData(): array
    {
        return [
            'transfer_code' => $this->transfer_code,
            'pickup_code' => $this->pickup_code,
            'amount' => $this->converted_amount,
            'currency' => $this->receiverCurrency->code,
            'receiver' => $this->receiver_name,
        ];
    }
}
