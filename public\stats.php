<?php

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';
// Load session helper


header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Connect to database
    $db = new PDO('sqlite:' . __DIR__ . '/../database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get detailed statistics
    $stats = [
        'users' => [
            'total' => (int)$db->query("SELECT COUNT(*) FROM users")->fetchColumn(),
            'active' => (int)$db->query("SELECT COUNT(*) FROM users WHERE is_active = 1")->fetchColumn(),
            'admins' => (int)$db->query("SELECT COUNT(*) FROM users WHERE role = 'admin'")->fetchColumn(),
            'customers' => (int)$db->query("SELECT COUNT(*) FROM users WHERE role = 'customer'")->fetchColumn(),
            'agents' => (int)$db->query("SELECT COUNT(*) FROM users WHERE role = 'agent'")->fetchColumn(),
            'verified' => (int)$db->query("SELECT COUNT(*) FROM users WHERE kyc_status = 'verified'")->fetchColumn(),
            'pending_kyc' => (int)$db->query("SELECT COUNT(*) FROM users WHERE kyc_status = 'pending'")->fetchColumn()
        ],
        'transfers' => [
            'total' => (int)$db->query("SELECT COUNT(*) FROM transfers")->fetchColumn(),
            'completed' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE status = 'completed'")->fetchColumn(),
            'pending' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE status = 'pending'")->fetchColumn(),
            'processing' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE status = 'processing'")->fetchColumn(),
            'ready_for_pickup' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE status = 'ready_for_pickup'")->fetchColumn(),
            'cancelled' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE status = 'cancelled'")->fetchColumn(),
            'today' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = DATE('now')")->fetchColumn(),
            'this_week' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) >= DATE('now', '-7 days')")->fetchColumn(),
            'this_month' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) >= DATE('now', 'start of month')")->fetchColumn()
        ],
        'financial' => [
            'total_amount' => (float)$db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE status = 'completed'")->fetchColumn(),
            'total_fees' => (float)$db->query("SELECT COALESCE(SUM(fee_amount), 0) FROM transfers WHERE status = 'completed'")->fetchColumn(),
            'avg_transfer_amount' => (float)$db->query("SELECT COALESCE(AVG(amount), 0) FROM transfers WHERE status = 'completed'")->fetchColumn(),
            'today_amount' => (float)$db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE status = 'completed' AND DATE(created_at) = DATE('now')")->fetchColumn(),
            'this_month_amount' => (float)$db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE status = 'completed' AND DATE(created_at) >= DATE('now', 'start of month')")->fetchColumn()
        ],
        'countries' => [
            'total' => (int)$db->query("SELECT COUNT(*) FROM countries")->fetchColumn(),
            'active' => (int)$db->query("SELECT COUNT(*) FROM countries WHERE is_active = 1")->fetchColumn(),
            'most_popular_sender' => $db->query("
                SELECT c.name, COUNT(*) as count 
                FROM transfers t 
                JOIN countries c ON t.sender_country_id = c.id 
                GROUP BY c.id 
                ORDER BY count DESC 
                LIMIT 1
            ")->fetch(PDO::FETCH_ASSOC),
            'most_popular_receiver' => $db->query("
                SELECT c.name, COUNT(*) as count 
                FROM transfers t 
                JOIN countries c ON t.receiver_country_id = c.id 
                GROUP BY c.id 
                ORDER BY count DESC 
                LIMIT 1
            ")->fetch(PDO::FETCH_ASSOC)
        ],
        'system' => [
            'exchange_rates' => (int)$db->query("SELECT COUNT(*) FROM exchange_rates")->fetchColumn(),
            'system_settings' => (int)$db->query("SELECT COUNT(*) FROM system_settings")->fetchColumn(),
            'notifications' => (int)$db->query("SELECT COUNT(*) FROM notifications")->fetchColumn(),
            'database_size_mb' => round(filesize(__DIR__ . '/../database/elite_transfer.db') / 1024 / 1024, 2)
        ]
    ];
    
    // Get recent activity
    $recent_transfers = $db->query("
        SELECT transfer_code, sender_name, receiver_name, amount, sender_currency, status, created_at
        FROM transfers 
        ORDER BY created_at DESC 
        LIMIT 5
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    $recent_users = $db->query("
        SELECT name, email, role, created_at
        FROM users 
        ORDER BY created_at DESC 
        LIMIT 5
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    $response = [
        'success' => true,
        'data' => $stats,
        'recent_activity' => [
            'transfers' => $recent_transfers,
            'users' => $recent_users
        ],
        'timestamp' => date('Y-m-d H:i:s'),
        'generated_by' => is_logged_in() ? get_user_data()['name'] : 'Anonymous'
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
