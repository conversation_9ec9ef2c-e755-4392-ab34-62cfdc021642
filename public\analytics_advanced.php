<?php

/**
 * Advanced Analytics System
 * Elite Transfer System - Comprehensive Analytics & Data Insights
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Check authentication
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = DatabaseManager::getInstance();
$userData = getUserData();

// Ensure countries table exists
try {
    $db->query("
        CREATE TABLE IF NOT EXISTS countries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            currency VARCHAR(3) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            status ENUM('active', 'inactive') DEFAULT 'active',
            flag_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP NULL,
            INDEX idx_code (code),
            INDEX idx_status (status),
            INDEX idx_deleted_at (deleted_at)
        )
    ");
} catch (Exception $e) {
    // Table might already exist
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'get_analytics_data':
                $period = $_POST['period'] ?? '30';
                $startDate = date('Y-m-d', strtotime("-{$period} days"));
                $endDate = date('Y-m-d');
                
                // Performance metrics
                $performanceData = $db->selectOne("
                    SELECT 
                        COUNT(*) as total_transfers,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transfers,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transfers,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transfers,
                        ROUND(COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as success_rate,
                        ROUND(COUNT(CASE WHEN status = 'failed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as failure_rate,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount END), 0) as total_volume,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN fee END), 0) as total_fees,
                        COALESCE(AVG(CASE WHEN status = 'completed' THEN total_amount END), 0) as avg_amount
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Daily trend data
                $trendData = $db->select("
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as transfers,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount END), 0) as volume,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN fee END), 0) as fees
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY DATE(created_at)
                    ORDER BY DATE(created_at)
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Hourly distribution
                $hourlyData = $db->select("
                    SELECT 
                        HOUR(created_at) as hour,
                        COUNT(*) as transfers,
                        COALESCE(AVG(total_amount), 0) as avg_amount
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY HOUR(created_at)
                    ORDER BY hour
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Top countries
                $countryData = [];
                try {
                    $countryData = $db->select("
                        SELECT
                            COALESCE(c.name, 'غير محدد') as country_name,
                            COUNT(t.id) as transfer_count,
                            COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume,
                            ROUND(COUNT(t.id) * 100.0 / NULLIF((SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN :start_date2 AND :end_date2 AND (deleted_at IS NULL OR deleted_at = '')), 0), 2) as percentage
                        FROM transfers t
                        LEFT JOIN countries c ON t.sender_country_id = c.id
                        WHERE DATE(t.created_at) BETWEEN :start_date AND :end_date
                        AND (t.deleted_at IS NULL OR t.deleted_at = '')
                        GROUP BY c.id, c.name
                        ORDER BY transfer_count DESC
                        LIMIT 10
                    ", [
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'start_date2' => $startDate,
                        'end_date2' => $endDate
                    ]);
                } catch (Exception $e) {
                    // If countries table doesn't exist or has issues, return empty array
                    $countryData = [];
                }
                
                // User activity
                $userActivity = $db->select("
                    SELECT 
                        u.role,
                        COUNT(t.id) as transfers_created,
                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume
                    FROM users u
                    LEFT JOIN transfers t ON u.id = t.user_id 
                        AND DATE(t.created_at) BETWEEN :start_date AND :end_date
                        AND (t.deleted_at IS NULL OR t.deleted_at = '')
                    WHERE (u.deleted_at IS NULL OR u.deleted_at = '')
                    GROUP BY u.role
                    ORDER BY transfers_created DESC
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Amount distribution
                $amountRanges = [
                    ['min' => 0, 'max' => 100, 'label' => '0-100'],
                    ['min' => 101, 'max' => 500, 'label' => '101-500'],
                    ['min' => 501, 'max' => 1000, 'label' => '501-1000'],
                    ['min' => 1001, 'max' => 5000, 'label' => '1001-5000'],
                    ['min' => 5001, 'max' => 999999, 'label' => '5000+']
                ];
                
                $amountDistribution = [];
                foreach ($amountRanges as $range) {
                    $result = $db->selectOne("
                        SELECT COUNT(*) as count
                        FROM transfers 
                        WHERE total_amount BETWEEN :min AND :max
                        AND DATE(created_at) BETWEEN :start_date AND :end_date
                        AND (deleted_at IS NULL OR deleted_at = '')
                    ", [
                        'min' => $range['min'],
                        'max' => $range['max'],
                        'start_date' => $startDate,
                        'end_date' => $endDate
                    ]);
                    
                    $amountDistribution[] = [
                        'label' => $range['label'],
                        'count' => intval($result['count'])
                    ];
                }
                
                echo json_encode([
                    'success' => true,
                    'performance' => $performanceData,
                    'trend_data' => $trendData,
                    'hourly_data' => $hourlyData,
                    'country_data' => $countryData,
                    'user_activity' => $userActivity,
                    'amount_distribution' => $amountDistribution,
                    'period' => ['start' => $startDate, 'end' => $endDate, 'days' => $period]
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'export_analytics':
                $format = $_POST['format'] ?? 'csv';
                $period = $_POST['period'] ?? '30';
                $startDate = date('Y-m-d', strtotime("-{$period} days"));
                $endDate = date('Y-m-d');
                
                // Get analytics data for export
                $exportData = $db->select("
                    SELECT 
                        DATE(created_at) as date,
                        transfer_code,
                        sender_name,
                        recipient_name,
                        total_amount,
                        fee,
                        status,
                        created_at
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    ORDER BY created_at DESC
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                if ($format === 'csv') {
                    $csvContent = '';
                    if (!empty($exportData)) {
                        // Headers
                        $csvContent .= "التاريخ,رمز التحويل,المرسل,المستقبل,المبلغ,الرسوم,الحالة,وقت الإنشاء\n";
                        
                        // Data rows
                        foreach ($exportData as $row) {
                            $csvContent .= implode(',', [
                                '"' . $row['date'] . '"',
                                '"' . $row['transfer_code'] . '"',
                                '"' . $row['sender_name'] . '"',
                                '"' . $row['recipient_name'] . '"',
                                $row['total_amount'],
                                $row['fee'],
                                '"' . $row['status'] . '"',
                                '"' . $row['created_at'] . '"'
                            ]) . "\n";
                        }
                    }
                    
                    $filename = "analytics_export_{$startDate}_to_{$endDate}.csv";
                    
                    echo json_encode([
                        'success' => true,
                        'filename' => $filename,
                        'content' => base64_encode($csvContent),
                        'mime_type' => 'text/csv'
                    ], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحليلات المتقدمة - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --border-radius: 20px;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .analytics-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .analytics-header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            color: white;
            text-align: center;
        }
        
        .analytics-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .analytics-controls {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .period-selector {
            display: flex;
            gap: 10px;
        }
        
        .period-btn {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            color: white;
            padding: 8px 16px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .period-btn.active,
        .period-btn:hover {
            background: var(--info-gradient);
            transform: translateY(-2px);
        }
        
        .export-controls {
            display: flex;
            gap: 10px;
        }
        
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .analytics-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 25px;
            color: white;
            transition: all 0.3s ease;
        }
        
        .analytics-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }
        
        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .back-btn:hover {
            background: var(--info-gradient);
            color: white;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .analytics-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .period-selector,
            .export-controls {
                justify-content: center;
            }
            
            .analytics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Back Button -->
    <a href="dashboard_advanced.php" class="back-btn">
        <i class="bi bi-arrow-right me-2"></i>
        العودة للوحة التحكم
    </a>

    <div class="analytics-container">
        <!-- Header -->
        <div class="analytics-header">
            <h1><i class="bi bi-graph-up me-3"></i>التحليلات المتقدمة</h1>
            <p>تحليل شامل لأداء النظام والاتجاهات</p>
        </div>

        <!-- Controls -->
        <div class="analytics-controls">
            <div class="period-selector">
                <button class="period-btn active" data-period="7">آخر 7 أيام</button>
                <button class="period-btn" data-period="30">آخر 30 يوم</button>
                <button class="period-btn" data-period="90">آخر 3 أشهر</button>
                <button class="period-btn" data-period="365">آخر سنة</button>
            </div>
            <div class="export-controls">
                <button class="btn btn-outline-light btn-sm" onclick="exportAnalytics('csv')">
                    <i class="bi bi-download me-1"></i>
                    تصدير CSV
                </button>
                <button class="btn btn-outline-light btn-sm" onclick="refreshAnalytics()">
                    <i class="bi bi-arrow-clockwise me-1"></i>
                    تحديث
                </button>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loadingState" class="text-center text-white" style="display: none;">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3">جاري تحميل البيانات...</p>
        </div>

        <!-- Analytics Content -->
        <div id="analyticsContent">
            <!-- Performance Metrics -->
            <div class="analytics-grid">
                <div class="analytics-card">
                    <div class="card-header">
                        <h3 class="card-title">معدل النجاح</h3>
                        <div class="card-icon" style="background: var(--success-gradient);">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                    <div class="metric-value" id="successRate">0%</div>
                    <div class="metric-label">من إجمالي التحويلات</div>
                </div>

                <div class="analytics-card">
                    <div class="card-header">
                        <h3 class="card-title">إجمالي الحجم</h3>
                        <div class="card-icon" style="background: var(--info-gradient);">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                    <div class="metric-value" id="totalVolume">$0</div>
                    <div class="metric-label">قيمة التحويلات المكتملة</div>
                </div>

                <div class="analytics-card">
                    <div class="card-header">
                        <h3 class="card-title">متوسط المبلغ</h3>
                        <div class="card-icon" style="background: var(--warning-gradient);">
                            <i class="bi bi-calculator"></i>
                        </div>
                    </div>
                    <div class="metric-value" id="avgAmount">$0</div>
                    <div class="metric-label">متوسط قيمة التحويل</div>
                </div>

                <div class="analytics-card">
                    <div class="card-header">
                        <h3 class="card-title">إجمالي الرسوم</h3>
                        <div class="card-icon" style="background: var(--danger-gradient);">
                            <i class="bi bi-piggy-bank"></i>
                        </div>
                    </div>
                    <div class="metric-value" id="totalFees">$0</div>
                    <div class="metric-label">إجمالي الرسوم المحصلة</div>
                </div>
            </div>

            <!-- Charts -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="analytics-card">
                        <div class="card-header">
                            <h3 class="card-title">اتجاه التحويلات</h3>
                            <div class="card-icon" style="background: var(--primary-gradient);">
                                <i class="bi bi-graph-up"></i>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="trendChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="analytics-card">
                        <div class="card-header">
                            <h3 class="card-title">توزيع الحالات</h3>
                            <div class="card-icon" style="background: var(--success-gradient);">
                                <i class="bi bi-pie-chart"></i>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-lg-6">
                    <div class="analytics-card">
                        <div class="card-header">
                            <h3 class="card-title">التوزيع الساعي</h3>
                            <div class="card-icon" style="background: var(--info-gradient);">
                                <i class="bi bi-clock"></i>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="hourlyChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="analytics-card">
                        <div class="card-header">
                            <h3 class="card-title">توزيع المبالغ</h3>
                            <div class="card-icon" style="background: var(--warning-gradient);">
                                <i class="bi bi-bar-chart"></i>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="amountChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="analytics-card">
                        <div class="card-header">
                            <h3 class="card-title">أهم البلدان</h3>
                            <div class="card-icon" style="background: var(--danger-gradient);">
                                <i class="bi bi-globe"></i>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="countryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let analyticsData = {};
        let charts = {};
        let currentPeriod = '7';

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadAnalyticsData();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Period selector
            document.querySelectorAll('.period-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.period-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentPeriod = this.dataset.period;
                    loadAnalyticsData();
                });
            });
        }

        function loadAnalyticsData() {
            showLoading();

            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_analytics_data',
                    period: currentPeriod
                },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        analyticsData = response;
                        updateMetrics();
                        initializeCharts();
                    } else {
                        showError('فشل في تحميل بيانات التحليلات');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                },
                complete: function() {
                    hideLoading();
                }
            });
        }

        function updateMetrics() {
            const performance = analyticsData.performance;

            document.getElementById('successRate').textContent = (performance.success_rate || 0) + '%';
            document.getElementById('totalVolume').textContent = '$' + (performance.total_volume || 0).toLocaleString();
            document.getElementById('avgAmount').textContent = '$' + (performance.avg_amount || 0).toLocaleString();
            document.getElementById('totalFees').textContent = '$' + (performance.total_fees || 0).toLocaleString();
        }

        function initializeCharts() {
            initializeTrendChart();
            initializeStatusChart();
            initializeHourlyChart();
            initializeAmountChart();
            initializeCountryChart();
        }

        function initializeTrendChart() {
            const ctx = document.getElementById('trendChart');
            if (charts.trendChart) {
                charts.trendChart.destroy();
            }

            const trendData = analyticsData.trend_data || [];

            charts.trendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: trendData.map(item => new Date(item.date).toLocaleDateString('ar-SA')),
                    datasets: [
                        {
                            label: 'إجمالي التحويلات',
                            data: trendData.map(item => item.transfers),
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'التحويلات المكتملة',
                            data: trendData.map(item => item.completed),
                            borderColor: '#38ef7d',
                            backgroundColor: 'rgba(56, 239, 125, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        function initializeStatusChart() {
            const ctx = document.getElementById('statusChart');
            if (charts.statusChart) {
                charts.statusChart.destroy();
            }

            const performance = analyticsData.performance;

            charts.statusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['مكتملة', 'معلقة', 'فاشلة'],
                    datasets: [{
                        data: [
                            performance.completed_transfers || 0,
                            performance.pending_transfers || 0,
                            performance.failed_transfers || 0
                        ],
                        backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: { color: 'white' }
                        }
                    }
                }
            });
        }

        function initializeHourlyChart() {
            const ctx = document.getElementById('hourlyChart');
            if (charts.hourlyChart) {
                charts.hourlyChart.destroy();
            }

            const hourlyData = analyticsData.hourly_data || [];

            charts.hourlyChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: hourlyData.map(item => item.hour + ':00'),
                    datasets: [{
                        label: 'عدد التحويلات',
                        data: hourlyData.map(item => item.transfers),
                        backgroundColor: 'rgba(79, 172, 254, 0.8)',
                        borderColor: '#4facfe',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        function initializeAmountChart() {
            const ctx = document.getElementById('amountChart');
            if (charts.amountChart) {
                charts.amountChart.destroy();
            }

            const amountData = analyticsData.amount_distribution || [];

            charts.amountChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: amountData.map(item => item.label),
                    datasets: [{
                        label: 'عدد التحويلات',
                        data: amountData.map(item => item.count),
                        backgroundColor: 'rgba(255, 193, 7, 0.8)',
                        borderColor: '#ffc107',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        function initializeCountryChart() {
            const ctx = document.getElementById('countryChart');
            if (charts.countryChart) {
                charts.countryChart.destroy();
            }

            const countryData = analyticsData.country_data || [];

            charts.countryChart = new Chart(ctx, {
                type: 'horizontalBar',
                data: {
                    labels: countryData.map(item => item.country_name || 'غير محدد'),
                    datasets: [{
                        label: 'عدد التحويلات',
                        data: countryData.map(item => item.transfer_count),
                        backgroundColor: 'rgba(255, 65, 108, 0.8)',
                        borderColor: '#ff416c',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        function exportAnalytics(format) {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'export_analytics',
                    format: format,
                    period: currentPeriod
                },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        // Create download link
                        const blob = new Blob([atob(response.content)], { type: response.mime_type });
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = response.filename;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        window.URL.revokeObjectURL(url);

                        Swal.fire({
                            icon: 'success',
                            title: 'تم التصدير بنجاح',
                            text: 'تم تحميل الملف',
                            confirmButtonText: 'حسناً'
                        });
                    } else {
                        showError('فشل في تصدير البيانات');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                }
            });
        }

        function refreshAnalytics() {
            loadAnalyticsData();

            Swal.fire({
                icon: 'success',
                title: 'تم التحديث',
                text: 'تم تحديث بيانات التحليلات',
                timer: 2000,
                showConfirmButton: false
            });
        }

        function showLoading() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('analyticsContent').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('analyticsContent').style.display = 'block';
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: message,
                confirmButtonText: 'حسناً'
            });
        }
    </script>
</body>
</html>
