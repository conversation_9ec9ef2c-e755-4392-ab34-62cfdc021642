<?php

/**
 * Debug AJAX Responses
 * Elite Transfer System - Debug AJAX issues
 */

// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Auto login if not logged in
if (!isLoggedIn()) {
    require_once __DIR__ . '/includes/database_manager.php';
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    $stmt = $db->prepare("SELECT * FROM users WHERE role = 'admin' AND status = 'active' AND deleted_at IS NULL LIMIT 1");
    $stmt->execute();
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        $_SESSION['user_id'] = $admin['id'];
        $_SESSION['name'] = $admin['name'];
        $_SESSION['email'] = $admin['email'];
        $_SESSION['role'] = $admin['role'];
    }
}

// Load database manager
require_once __DIR__ . '/includes/database_manager.php';

try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
} catch (Exception $e) {
    // Return JSON error instead of dying
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    // Ensure we always return JSON
    header('Content-Type: application/json');
    
    // Capture any output that might interfere
    ob_start();
    
    try {
        switch ($_POST['action']) {
            case 'test_connection':
                // Simple test
                $result = $db->query("SELECT 1 as test")->fetch();
                ob_clean(); // Clear any output
                echo json_encode(['success' => true, 'message' => 'اتصال ناجح', 'data' => $result]);
                exit;
                
            case 'get_transfers':
                $page = intval($_POST['page'] ?? 1);
                $limit = intval($_POST['limit'] ?? 10);
                $search = $_POST['search'] ?? '';
                $status_filter = $_POST['status_filter'] ?? '';
                $date_from = $_POST['date_from'] ?? '';
                $date_to = $_POST['date_to'] ?? '';
                
                $offset = ($page - 1) * $limit;
                
                // Build WHERE clause
                $where = ["t.deleted_at IS NULL"];
                $params = [];
                
                if ($search) {
                    $where[] = "(t.transfer_code LIKE ? OR t.pickup_code LIKE ? OR t.sender_name LIKE ? OR t.recipient_name LIKE ?)";
                    $params[] = "%$search%";
                    $params[] = "%$search%";
                    $params[] = "%$search%";
                    $params[] = "%$search%";
                }
                
                if ($status_filter) {
                    $where[] = "t.status = ?";
                    $params[] = $status_filter;
                }
                
                if ($date_from) {
                    $where[] = "DATE(t.created_at) >= ?";
                    $params[] = $date_from;
                }
                
                if ($date_to) {
                    $where[] = "DATE(t.created_at) <= ?";
                    $params[] = $date_to;
                }
                
                $whereClause = implode(' AND ', $where);
                
                // Get total count
                $countQuery = "SELECT COUNT(*) FROM transfers t WHERE $whereClause";
                $stmt = $db->prepare($countQuery);
                $stmt->execute($params);
                $total = $stmt->fetchColumn();
                
                // Get transfers with safe column selection
                $query = "
                    SELECT t.id, t.transfer_code, t.pickup_code, t.sender_name, t.sender_phone,
                           t.recipient_name, t.recipient_phone, t.amount, t.fee, t.total_amount,
                           t.status, t.payment_method, t.purpose, t.notes, t.created_at, t.updated_at,
                           COALESCE(u.name, 'غير محدد') as user_name,
                           COALESCE(sc.name, 'غير محدد') as sender_country,
                           COALESCE(rc.name, 'غير محدد') as recipient_country
                    FROM transfers t
                    LEFT JOIN users u ON t.user_id = u.id AND u.deleted_at IS NULL
                    LEFT JOIN countries sc ON t.sender_country_id = sc.id
                    LEFT JOIN countries rc ON t.recipient_country_id = rc.id
                    WHERE $whereClause
                    ORDER BY t.created_at DESC
                    LIMIT $limit OFFSET $offset
                ";
                
                $stmt = $db->prepare($query);
                $stmt->execute($params);
                $transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                ob_clean(); // Clear any output
                echo json_encode([
                    'success' => true,
                    'transfers' => $transfers,
                    'total' => $total,
                    'page' => $page,
                    'pages' => ceil($total / $limit),
                    'debug' => [
                        'query' => $query,
                        'params' => $params,
                        'where' => $whereClause
                    ]
                ]);
                exit;
                
            case 'get_transfer':
                $id = intval($_POST['id']);
                $stmt = $db->prepare("
                    SELECT t.id, t.transfer_code, t.pickup_code, t.sender_name, t.sender_phone,
                           t.sender_address, t.recipient_name, t.recipient_phone, t.recipient_address,
                           t.amount, t.fee, t.total_amount, t.exchange_rate, t.currency_from, t.currency_to,
                           t.status, t.payment_method, t.purpose, t.notes, t.created_at, t.updated_at,
                           COALESCE(u.name, 'غير محدد') as user_name,
                           COALESCE(u.email, 'غير محدد') as user_email,
                           COALESCE(sc.name, 'غير محدد') as sender_country,
                           COALESCE(rc.name, 'غير محدد') as recipient_country
                    FROM transfers t
                    LEFT JOIN users u ON t.user_id = u.id AND u.deleted_at IS NULL
                    LEFT JOIN countries sc ON t.sender_country_id = sc.id
                    LEFT JOIN countries rc ON t.recipient_country_id = rc.id
                    WHERE t.id = ? AND (t.deleted_at IS NULL OR t.deleted_at IS NULL)
                ");
                $stmt->execute([$id]);
                $transfer = $stmt->fetch(PDO::FETCH_ASSOC);
                
                ob_clean(); // Clear any output
                if ($transfer) {
                    echo json_encode(['success' => true, 'transfer' => $transfer]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'التحويل غير موجود']);
                }
                exit;
                
            case 'update_transfer_status':
                $id = intval($_POST['id']);
                $status = $_POST['status'] ?? '';
                $notes = $_POST['notes'] ?? '';
                
                // Update transfer status
                $stmt = $db->prepare("
                    UPDATE transfers 
                    SET status = ?, updated_at = NOW()
                    WHERE id = ? AND deleted_at IS NULL
                ");
                
                $result = $stmt->execute([$status, $id]);
                
                ob_clean(); // Clear any output
                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'تم تحديث حالة التحويل بنجاح']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في تحديث حالة التحويل']);
                }
                exit;
                
            default:
                ob_clean(); // Clear any output
                echo json_encode(['success' => false, 'message' => 'إجراء غير معروف: ' . $_POST['action']]);
                exit;
        }
        
    } catch (Exception $e) {
        ob_clean(); // Clear any output
        echo json_encode([
            'success' => false, 
            'message' => 'خطأ في الخادم: ' . $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
        exit;
    }
}

// If not AJAX request, show debug page
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص AJAX - Elite Transfer System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .debug-card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 20px; }
        .response-area { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; min-height: 200px; font-family: monospace; }
        .test-btn { margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white text-center mb-4">🔍 تشخيص AJAX</h1>
        
        <div class="debug-card">
            <h3>اختبار اتصال AJAX</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>اختبارات سريعة:</h5>
                    <button class="btn btn-primary test-btn" onclick="testConnection()">اختبار الاتصال</button>
                    <button class="btn btn-success test-btn" onclick="testGetTransfers()">اختبار التحويلات</button>
                    <button class="btn btn-info test-btn" onclick="testGetTransfer()">اختبار تحويل واحد</button>
                    <button class="btn btn-warning test-btn" onclick="testUpdateStatus()">اختبار تحديث الحالة</button>
                    <button class="btn btn-danger test-btn" onclick="clearResponse()">مسح النتائج</button>
                </div>
                <div class="col-md-6">
                    <h5>معلومات النظام:</h5>
                    <p><strong>PHP Version:</strong> <?= PHP_VERSION ?></p>
                    <p><strong>Session Status:</strong> <?= session_status() === PHP_SESSION_ACTIVE ? 'نشط' : 'غير نشط' ?></p>
                    <p><strong>User Logged In:</strong> <?= isLoggedIn() ? 'نعم' : 'لا' ?></p>
                    <p><strong>User Role:</strong> <?= isAdmin() ? 'مدير' : 'عادي' ?></p>
                </div>
            </div>
            
            <h5 class="mt-4">استجابة الخادم:</h5>
            <div id="responseArea" class="response-area">انقر على أحد الأزرار لاختبار AJAX...</div>
        </div>
        
        <div class="debug-card">
            <h3>روابط مفيدة</h3>
            <div class="d-grid gap-2 d-md-flex">
                <a href="fix_transfers_page.php" class="btn btn-outline-primary">الصفحة المُصلحة</a>
                <a href="admin_transfers_enhanced.php" class="btn btn-outline-success">الصفحة الأصلية</a>
                <a href="diagnose_admin_issues.php" class="btn btn-outline-info">التشخيص الشامل</a>
                <a href="dashboard.php" class="btn btn-outline-secondary">لوحة التحكم</a>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script>
        function testConnection() {
            makeAjaxCall('test_connection', {}, 'اختبار الاتصال');
        }
        
        function testGetTransfers() {
            makeAjaxCall('get_transfers', {
                page: 1,
                limit: 5,
                search: '',
                status_filter: '',
                date_from: '',
                date_to: ''
            }, 'اختبار جلب التحويلات');
        }
        
        function testGetTransfer() {
            makeAjaxCall('get_transfer', {
                id: 1
            }, 'اختبار جلب تحويل واحد');
        }
        
        function testUpdateStatus() {
            makeAjaxCall('update_transfer_status', {
                id: 1,
                status: 'processing',
                notes: 'اختبار تحديث الحالة'
            }, 'اختبار تحديث الحالة');
        }
        
        function makeAjaxCall(action, data, description) {
            const responseArea = $('#responseArea');
            responseArea.html(`<div class="text-info">جاري تنفيذ: ${description}...</div>`);
            
            data.action = action;
            
            $.ajax({
                url: '',
                method: 'POST',
                data: data,
                dataType: 'json',
                success: function(response) {
                    responseArea.html(`
                        <div class="text-success"><strong>✅ ${description} - نجح</strong></div>
                        <hr>
                        <strong>الاستجابة:</strong>
                        <pre>${JSON.stringify(response, null, 2)}</pre>
                    `);
                },
                error: function(xhr, status, error) {
                    responseArea.html(`
                        <div class="text-danger"><strong>❌ ${description} - فشل</strong></div>
                        <hr>
                        <strong>خطأ:</strong> ${error}<br>
                        <strong>الحالة:</strong> ${status}<br>
                        <strong>كود الاستجابة:</strong> ${xhr.status}<br>
                        <strong>نص الاستجابة:</strong>
                        <pre>${xhr.responseText}</pre>
                    `);
                }
            });
        }
        
        function clearResponse() {
            $('#responseArea').html('انقر على أحد الأزرار لاختبار AJAX...');
        }
    </script>
</body>
</html>
