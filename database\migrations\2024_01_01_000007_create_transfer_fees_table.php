<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transfer_fees', function (Blueprint $table) {
            $table->id();
            $table->foreignId('transfer_id')->nullable()->constrained()->cascadeOnDelete();
            $table->foreignId('sender_country_id')->constrained('countries');
            $table->foreignId('receiver_country_id')->constrained('countries');
            $table->foreignId('sender_currency_id')->constrained('currencies');
            $table->foreignId('receiver_currency_id')->constrained('currencies');
            $table->decimal('min_amount', 15, 2)->default(0.00);
            $table->decimal('max_amount', 15, 2)->default(999999999.99);
            $table->decimal('fixed_fee', 15, 2)->default(0.00);
            $table->decimal('percentage_fee', 5, 4)->default(0.0000);
            $table->decimal('agent_commission', 5, 4)->default(0.0000);
            $table->decimal('branch_commission', 5, 4)->default(0.0000);
            $table->string('fee_type')->default('standard'); // standard, express, premium
            $table->boolean('is_active')->default(true);
            $table->json('conditions')->nullable();
            $table->timestamps();

            $table->index(['sender_country_id', 'receiver_country_id']);
            $table->index(['sender_currency_id', 'receiver_currency_id']);
            $table->index(['min_amount', 'max_amount']);
            $table->index(['is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transfer_fees');
    }
};
