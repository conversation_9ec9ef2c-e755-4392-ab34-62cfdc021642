<?php

/**
 * Auto Login for Testing
 * Elite Transfer System - Automatic login for admin testing
 */

// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Load database manager
require_once __DIR__ . '/includes/database_manager.php';

try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    // Find admin user
    $stmt = $db->prepare("
        SELECT * FROM users 
        WHERE role = 'admin' 
        AND status = 'active' 
        AND deleted_at IS NULL 
        LIMIT 1
    ");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        // Login as admin
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['name'] = $user['name'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['last_login_at'] = date('Y-m-d H:i:s');
        
        // Update last login time
        $updateStmt = $db->prepare("UPDATE users SET last_login_at = NOW() WHERE id = ?");
        $updateStmt->execute([$user['id']]);
        
        echo "<!DOCTYPE html>";
        echo "<html lang='ar' dir='rtl'>";
        echo "<head>";
        echo "<meta charset='UTF-8'>";
        echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
        echo "<title>تسجيل دخول تلقائي - Elite Transfer System</title>";
        echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
        echo "<style>";
        echo "body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }";
        echo ".login-card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 40px; text-align: center; max-width: 500px; }";
        echo "</style>";
        echo "</head>";
        echo "<body>";
        echo "<div class='login-card'>";
        echo "<h1 class='text-success mb-4'>✅ تم تسجيل الدخول بنجاح!</h1>";
        echo "<div class='alert alert-success'>";
        echo "<h5>مرحباً " . htmlspecialchars($user['name']) . "</h5>";
        echo "<p>البريد الإلكتروني: " . htmlspecialchars($user['email']) . "</p>";
        echo "<p>الدور: " . htmlspecialchars($user['role']) . "</p>";
        echo "</div>";
        echo "<div class='d-grid gap-2'>";
        echo "<a href='dashboard.php' class='btn btn-primary btn-lg'>الانتقال إلى لوحة التحكم</a>";
        echo "<a href='admin_users_enhanced.php' class='btn btn-outline-primary'>إدارة المستخدمين</a>";
        echo "<a href='admin_transfers_enhanced.php' class='btn btn-outline-success'>إدارة التحويلات</a>";
        echo "<a href='admin_reports_enhanced.php' class='btn btn-outline-info'>التقارير</a>";
        echo "<a href='admin_monitoring_enhanced.php' class='btn btn-outline-warning'>مراقبة النظام</a>";
        echo "<a href='admin_compliance_enhanced.php' class='btn btn-outline-danger'>الامتثال التنظيمي</a>";
        echo "</div>";
        echo "</div>";
        echo "</body>";
        echo "</html>";
        
    } else {
        // No admin user found, create one
        echo "<!DOCTYPE html>";
        echo "<html lang='ar' dir='rtl'>";
        echo "<head>";
        echo "<meta charset='UTF-8'>";
        echo "<title>إنشاء مدير النظام - Elite Transfer System</title>";
        echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
        echo "</head>";
        echo "<body class='bg-light'>";
        echo "<div class='container mt-5'>";
        echo "<div class='row justify-content-center'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card'>";
        echo "<div class='card-header'>";
        echo "<h3>إنشاء حساب مدير النظام</h3>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        if ($_POST && isset($_POST['create_admin'])) {
            $name = $_POST['name'] ?? 'System Administrator';
            $email = $_POST['email'] ?? '<EMAIL>';
            $password = $_POST['password'] ?? 'admin123';
            
            // Generate user code
            $stmt = $db->prepare("SELECT MAX(id) FROM users");
            $stmt->execute();
            $maxId = $stmt->fetchColumn() ?: 0;
            $userCode = 'ADM' . str_pad($maxId + 1, 3, '0', STR_PAD_LEFT);
            
            // Insert admin user
            $stmt = $db->prepare("
                INSERT INTO users (user_code, name, email, password_hash, role, status, created_at) 
                VALUES (?, ?, ?, ?, 'admin', 'active', NOW())
            ");
            
            $result = $stmt->execute([
                $userCode,
                $name,
                $email,
                password_hash($password, PASSWORD_DEFAULT)
            ]);
            
            if ($result) {
                echo "<div class='alert alert-success'>تم إنشاء حساب المدير بنجاح!</div>";
                echo "<p><strong>البريد الإلكتروني:</strong> $email</p>";
                echo "<p><strong>كلمة المرور:</strong> $password</p>";
                echo "<a href='auto_login.php' class='btn btn-primary'>تسجيل الدخول الآن</a>";
            } else {
                echo "<div class='alert alert-danger'>فشل في إنشاء حساب المدير</div>";
            }
        } else {
            echo "<form method='POST'>";
            echo "<div class='mb-3'>";
            echo "<label class='form-label'>الاسم:</label>";
            echo "<input type='text' class='form-control' name='name' value='System Administrator' required>";
            echo "</div>";
            echo "<div class='mb-3'>";
            echo "<label class='form-label'>البريد الإلكتروني:</label>";
            echo "<input type='email' class='form-control' name='email' value='<EMAIL>' required>";
            echo "</div>";
            echo "<div class='mb-3'>";
            echo "<label class='form-label'>كلمة المرور:</label>";
            echo "<input type='password' class='form-control' name='password' value='admin123' required>";
            echo "</div>";
            echo "<button type='submit' name='create_admin' class='btn btn-primary'>إنشاء حساب المدير</button>";
            echo "</form>";
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</body>";
        echo "</html>";
    }
    
} catch (Exception $e) {
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head><meta charset='UTF-8'><title>خطأ</title></head>";
    echo "<body>";
    echo "<div class='container mt-5'>";
    echo "<div class='alert alert-danger'>";
    echo "<h4>خطأ في الاتصال بقاعدة البيانات</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<a href='database_status.php' class='btn btn-info'>فحص حالة قاعدة البيانات</a>";
    echo "</div>";
    echo "</div>";
    echo "</body>";
    echo "</html>";
}

?>
