<?php

/**
 * Track Transfer - Fixed Version
 * Elite Transfer System - Track transfer with MySQL support
 */

// Load session helper
require_once __DIR__ . '/includes/session_helper.php';
require_once __DIR__ . '/includes/database_manager.php';

// Get transfer code from URL parameter or form
$transferCode = $_GET['code'] ?? $_POST['transfer_code'] ?? '';
$transfer = null;
$error = '';

if ($transferCode) {
    try {
        $dbManager = DatabaseManager::getInstance();
        $db = $dbManager->getConnection();
        
        $stmt = $db->prepare("
            SELECT t.*,
                   COALESCE(u.name, 'غير محدد') as user_name,
                   COALESCE(sc.name, 'غير محدد') as sender_country_name, 
                   COALESCE(sc.currency, 'USD') as sender_currency_name,
                   COALESCE(rc.name, 'غير محدد') as recipient_country_name, 
                   COALESCE(rc.currency, 'USD') as recipient_currency_name
            FROM transfers t
            LEFT JOIN users u ON t.user_id = u.id AND u.deleted_at IS NULL
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.recipient_country_id = rc.id
            WHERE (t.transfer_code = ? OR t.pickup_code = ?) 
            AND (t.deleted_at IS NULL OR t.deleted_at = '')
        ");
        $stmt->execute([$transferCode, $transferCode]);
        $transfer = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$transfer) {
            $error = 'لم يتم العثور على التحويل. يرجى التحقق من رمز التحويل.';
        }
        
    } catch (Exception $e) {
        error_log("Error fetching transfer: " . $e->getMessage());
        $error = 'حدث خطأ في البحث عن التحويل. يرجى المحاولة مرة أخرى.';
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تتبع التحويل - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .track-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 50px auto;
            max-width: 800px;
            overflow: hidden;
        }
        
        .track-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .track-content {
            padding: 40px;
        }
        
        .search-box {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .transfer-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
        }
        
        .status-badge {
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .status-pending { background: #fff3cd; color: #664d03; }
        .status-processing { background: #cff4fc; color: #055160; }
        .status-completed { background: #d1e7dd; color: #0f5132; }
        .status-cancelled { background: #f8d7da; color: #842029; }
        .status-failed { background: #f8d7da; color: #842029; }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: bold;
            opacity: 0.8;
        }
        
        .info-value {
            font-size: 1.1em;
        }
        
        .amount-display {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        
        .timeline {
            position: relative;
            padding: 20px 0;
        }
        
        .timeline-item {
            position: relative;
            padding: 15px 0 15px 50px;
            border-left: 3px solid #dee2e6;
        }
        
        .timeline-item:last-child {
            border-left: none;
        }
        
        .timeline-icon {
            position: absolute;
            left: -12px;
            top: 15px;
            width: 24px;
            height: 24px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .timeline-pending .timeline-icon {
            background: #ffc107;
        }
        
        .timeline-processing .timeline-icon {
            background: #17a2b8;
        }
        
        .timeline-completed .timeline-icon {
            background: #28a745;
        }
        
        .error-card {
            background: #f8d7da;
            color: #721c24;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
        }
        
        .btn-track {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-track:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="track-container">
            <!-- Header -->
            <div class="track-header">
                <h1 class="mb-3">
                    <i class="bi bi-search me-3"></i>
                    تتبع التحويل
                </h1>
                <p class="mb-0 opacity-75">ادخل رمز التحويل أو رمز الاستلام لتتبع حالة التحويل</p>
            </div>
            
            <!-- Content -->
            <div class="track-content">
                <!-- Search Form -->
                <div class="search-box">
                    <form method="POST" action="">
                        <div class="row align-items-end">
                            <div class="col-md-8">
                                <label for="transfer_code" class="form-label">
                                    <i class="bi bi-qr-code me-2"></i>
                                    رمز التحويل أو رمز الاستلام
                                </label>
                                <input 
                                    type="text" 
                                    class="form-control form-control-lg" 
                                    id="transfer_code" 
                                    name="transfer_code" 
                                    value="<?= htmlspecialchars($transferCode) ?>"
                                    placeholder="مثال: TRF001 أو PCK001"
                                    required
                                >
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-track btn-lg w-100">
                                    <i class="bi bi-search me-2"></i>
                                    تتبع
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                
                <?php if ($error): ?>
                    <!-- Error Message -->
                    <div class="error-card">
                        <i class="bi bi-exclamation-triangle display-4 mb-3"></i>
                        <h4>لم يتم العثور على التحويل</h4>
                        <p class="mb-0"><?= htmlspecialchars($error) ?></p>
                    </div>
                <?php elseif ($transfer): ?>
                    <!-- Transfer Details -->
                    <div class="transfer-card">
                        <div class="row">
                            <div class="col-md-8">
                                <h3 class="mb-4">
                                    <i class="bi bi-check-circle me-2"></i>
                                    تم العثور على التحويل
                                </h3>
                                
                                <div class="info-row">
                                    <span class="info-label">رمز التحويل:</span>
                                    <span class="info-value"><?= htmlspecialchars($transfer['transfer_code'] ?? 'غير محدد') ?></span>
                                </div>
                                
                                <div class="info-row">
                                    <span class="info-label">رمز الاستلام:</span>
                                    <span class="info-value"><?= htmlspecialchars($transfer['pickup_code'] ?? 'غير محدد') ?></span>
                                </div>
                                
                                <div class="info-row">
                                    <span class="info-label">المرسل:</span>
                                    <span class="info-value"><?= htmlspecialchars($transfer['sender_name'] ?? 'غير محدد') ?></span>
                                </div>
                                
                                <div class="info-row">
                                    <span class="info-label">المستلم:</span>
                                    <span class="info-value"><?= htmlspecialchars($transfer['recipient_name'] ?? 'غير محدد') ?></span>
                                </div>
                                
                                <div class="info-row">
                                    <span class="info-label">تاريخ الإرسال:</span>
                                    <span class="info-value">
                                        <?= $transfer['created_at'] ? date('Y-m-d H:i', strtotime($transfer['created_at'])) : 'غير محدد' ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="col-md-4 text-center">
                                <div class="amount-display">
                                    $<?= number_format($transfer['amount'] ?? 0, 2) ?>
                                </div>
                                
                                <div class="status-badge status-<?= $transfer['status'] ?? 'pending' ?>">
                                    <?php
                                    $statusText = [
                                        'pending' => 'في الانتظار',
                                        'processing' => 'قيد المعالجة',
                                        'completed' => 'مكتمل',
                                        'cancelled' => 'ملغي',
                                        'failed' => 'فاشل'
                                    ];
                                    echo $statusText[$transfer['status'] ?? 'pending'] ?? 'غير محدد';
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-person-circle me-2"></i>
                                        تفاصيل المرسل
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>الاسم:</strong> <?= htmlspecialchars($transfer['sender_name'] ?? 'غير محدد') ?></p>
                                    <p><strong>الهاتف:</strong> <?= htmlspecialchars($transfer['sender_phone'] ?? 'غير محدد') ?></p>
                                    <p><strong>الدولة:</strong> <?= htmlspecialchars($transfer['sender_country_name'] ?? 'غير محدد') ?></p>
                                    <?php if (!empty($transfer['sender_address'])): ?>
                                        <p><strong>العنوان:</strong> <?= htmlspecialchars($transfer['sender_address']) ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-person-check me-2"></i>
                                        تفاصيل المستلم
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>الاسم:</strong> <?= htmlspecialchars($transfer['recipient_name'] ?? 'غير محدد') ?></p>
                                    <p><strong>الهاتف:</strong> <?= htmlspecialchars($transfer['recipient_phone'] ?? 'غير محدد') ?></p>
                                    <p><strong>الدولة:</strong> <?= htmlspecialchars($transfer['recipient_country_name'] ?? 'غير محدد') ?></p>
                                    <?php if (!empty($transfer['recipient_address'])): ?>
                                        <p><strong>العنوان:</strong> <?= htmlspecialchars($transfer['recipient_address']) ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Financial Details -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-currency-dollar me-2"></i>
                                التفاصيل المالية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <p><strong>المبلغ:</strong> $<?= number_format($transfer['amount'] ?? 0, 2) ?></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>الرسوم:</strong> $<?= number_format($transfer['fee'] ?? 0, 2) ?></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>المبلغ الإجمالي:</strong> $<?= number_format($transfer['total_amount'] ?? $transfer['amount'] ?? 0, 2) ?></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>سعر الصرف:</strong> <?= $transfer['exchange_rate'] ?? '1.00' ?></p>
                                </div>
                            </div>
                            
                            <?php if (!empty($transfer['purpose'])): ?>
                                <p><strong>الغرض:</strong> <?= htmlspecialchars($transfer['purpose']) ?></p>
                            <?php endif; ?>
                            
                            <?php if (!empty($transfer['notes'])): ?>
                                <p><strong>ملاحظات:</strong> <?= htmlspecialchars($transfer['notes']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Timeline -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-clock-history me-2"></i>
                                تتبع الحالة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item timeline-completed">
                                    <div class="timeline-icon">
                                        <i class="bi bi-check"></i>
                                    </div>
                                    <h6>تم إنشاء التحويل</h6>
                                    <small class="text-muted">
                                        <?= $transfer['created_at'] ? date('Y-m-d H:i', strtotime($transfer['created_at'])) : 'غير محدد' ?>
                                    </small>
                                </div>
                                
                                <?php if ($transfer['status'] !== 'pending'): ?>
                                <div class="timeline-item timeline-<?= $transfer['status'] === 'completed' ? 'completed' : 'processing' ?>">
                                    <div class="timeline-icon">
                                        <i class="bi bi-arrow-repeat"></i>
                                    </div>
                                    <h6>قيد المعالجة</h6>
                                    <small class="text-muted">
                                        <?= $transfer['updated_at'] ? date('Y-m-d H:i', strtotime($transfer['updated_at'])) : 'غير محدد' ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($transfer['status'] === 'completed'): ?>
                                <div class="timeline-item timeline-completed">
                                    <div class="timeline-icon">
                                        <i class="bi bi-check-circle"></i>
                                    </div>
                                    <h6>تم إكمال التحويل</h6>
                                    <small class="text-muted">
                                        <?= $transfer['updated_at'] ? date('Y-m-d H:i', strtotime($transfer['updated_at'])) : 'غير محدد' ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                <?php endif; ?>
                
                <!-- Navigation -->
                <div class="text-center mt-4">
                    <a href="dashboard.php" class="btn btn-outline-primary me-2">
                        <i class="bi bi-house me-1"></i>
                        الرئيسية
                    </a>
                    <a href="create-transfer.php" class="btn btn-outline-success">
                        <i class="bi bi-plus-circle me-1"></i>
                        تحويل جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
