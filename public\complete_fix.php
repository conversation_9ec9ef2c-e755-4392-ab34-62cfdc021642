<?php

/**
 * Complete System Fix
 * Elite Transfer System - Complete fix for all database issues
 */

require_once __DIR__ . '/includes/database_manager.php';

try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<title>الإصلاح الشامل للنظام - Elite Transfer System</title>";
    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
    echo "<style>";
    echo "body { background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); min-height: 100vh; padding: 20px; }";
    echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 20px; }";
    echo ".step { padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid; }";
    echo ".success { border-left-color: #28a745; background: #d4edda; color: #155724; }";
    echo ".error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }";
    echo ".warning { border-left-color: #ffc107; background: #fff3cd; color: #856404; }";
    echo ".info { border-left-color: #17a2b8; background: #d1ecf1; color: #0c5460; }";
    echo ".progress { height: 25px; margin: 10px 0; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";
    
    echo "<div class='container'>";
    echo "<h1 class='text-white text-center mb-4'>🔧 الإصلاح الشامل للنظام</h1>";
    
    $totalSteps = 8;
    $currentStep = 0;
    
    function updateProgress($current, $total, $message) {
        $percentage = ($current / $total) * 100;
        echo "<div class='progress'>";
        echo "<div class='progress-bar progress-bar-striped progress-bar-animated' style='width: {$percentage}%'>";
        echo "الخطوة $current من $total: $message";
        echo "</div>";
        echo "</div>";
    }
    
    // Step 1: Remove foreign key constraints
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 1: إزالة قيود المفاتيح الخارجية</h3>";
    updateProgress(++$currentStep, $totalSteps, "إزالة القيود المشكلة");
    
    try {
        $foreignKeys = $db->query("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = 'elite_transfer' 
            AND TABLE_NAME = 'transfers' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($foreignKeys) > 0) {
            foreach ($foreignKeys as $fk) {
                try {
                    $db->exec("ALTER TABLE transfers DROP FOREIGN KEY " . $fk['CONSTRAINT_NAME']);
                    echo "<div class='step success'>✅ تم حذف القيد: " . $fk['CONSTRAINT_NAME'] . "</div>";
                } catch (Exception $e) {
                    echo "<div class='step warning'>⚠️ تحذير: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            }
        } else {
            echo "<div class='step success'>✅ لا توجد قيود مفاتيح خارجية مشكلة</div>";
        }
    } catch (Exception $e) {
        echo "<div class='step error'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Step 2: Fix transfers table structure
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 2: إصلاح بنية جدول التحويلات</h3>";
    updateProgress(++$currentStep, $totalSteps, "فحص وإصلاح الأعمدة");
    
    try {
        $columns = $db->query("DESCRIBE transfers")->fetchAll(PDO::FETCH_ASSOC);
        $existingColumns = array_column($columns, 'Field');
        
        $requiredColumns = [
            'user_id' => 'INT',
            'sender_country_id' => 'INT',
            'recipient_country_id' => 'INT',
            'sender_address' => 'TEXT',
            'recipient_address' => 'TEXT',
            'fee' => 'DECIMAL(15,2)',
            'exchange_rate' => 'DECIMAL(10,4)',
            'total_amount' => 'DECIMAL(15,2)',
            'currency_from' => 'VARCHAR(3) DEFAULT "USD"',
            'currency_to' => 'VARCHAR(3) DEFAULT "USD"',
            'payment_method' => 'VARCHAR(50)',
            'purpose' => 'TEXT',
            'notes' => 'TEXT',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'deleted_at' => 'TIMESTAMP NULL DEFAULT NULL'
        ];
        
        foreach ($requiredColumns as $column => $definition) {
            if (!in_array($column, $existingColumns)) {
                try {
                    $db->exec("ALTER TABLE transfers ADD COLUMN $column $definition");
                    echo "<div class='step success'>✅ تم إضافة العمود: $column</div>";
                } catch (Exception $e) {
                    echo "<div class='step error'>❌ فشل في إضافة العمود $column: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            } else {
                echo "<div class='step info'>ℹ️ العمود $column موجود بالفعل</div>";
            }
        }
    } catch (Exception $e) {
        echo "<div class='step error'>❌ خطأ في إصلاح الجدول: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Step 3: Create users if missing
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 3: إنشاء المستخدمين</h3>";
    updateProgress(++$currentStep, $totalSteps, "فحص وإنشاء المستخدمين");
    
    try {
        $userCount = $db->query("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL")->fetchColumn();
        
        if ($userCount == 0) {
            $testUsers = [
                ['USR001', 'مستخدم تجريبي 1', '<EMAIL>', '+************', password_hash('password123', PASSWORD_DEFAULT), 'user'],
                ['ADM001', 'مدير النظام', '<EMAIL>', '+************', password_hash('admin123', PASSWORD_DEFAULT), 'admin']
            ];
            
            $stmt = $db->prepare("
                INSERT INTO users (user_code, name, email, phone, password_hash, role, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, 'active', NOW())
            ");
            
            foreach ($testUsers as $user) {
                try {
                    $stmt->execute($user);
                    echo "<div class='step success'>✅ تم إنشاء المستخدم: {$user[1]}</div>";
                } catch (Exception $e) {
                    echo "<div class='step warning'>⚠️ تحذير: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            }
        } else {
            echo "<div class='step success'>✅ يوجد $userCount مستخدم في النظام</div>";
        }
    } catch (Exception $e) {
        echo "<div class='step error'>❌ خطأ في إنشاء المستخدمين: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Step 4: Create countries if missing
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 4: إنشاء الدول</h3>";
    updateProgress(++$currentStep, $totalSteps, "فحص وإنشاء الدول");
    
    try {
        $countryCount = $db->query("SELECT COUNT(*) FROM countries")->fetchColumn();
        
        if ($countryCount == 0) {
            $testCountries = [
                ['SA', 'المملكة العربية السعودية', 'SAR'],
                ['EG', 'مصر', 'EGP'],
                ['AE', 'الإمارات العربية المتحدة', 'AED'],
                ['JO', 'الأردن', 'JOD'],
                ['US', 'الولايات المتحدة الأمريكية', 'USD']
            ];
            
            $stmt = $db->prepare("
                INSERT INTO countries (code, name, currency, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            
            foreach ($testCountries as $country) {
                try {
                    $stmt->execute($country);
                    echo "<div class='step success'>✅ تم إنشاء الدولة: {$country[1]}</div>";
                } catch (Exception $e) {
                    echo "<div class='step warning'>⚠️ تحذير: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            }
        } else {
            echo "<div class='step success'>✅ توجد $countryCount دولة في النظام</div>";
        }
    } catch (Exception $e) {
        echo "<div class='step error'>❌ خطأ في إنشاء الدول: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Step 5: Add sample transfers
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 5: إضافة تحويلات تجريبية</h3>";
    updateProgress(++$currentStep, $totalSteps, "إضافة البيانات التجريبية");
    
    try {
        $transferCount = $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn();
        
        if ($transferCount == 0) {
            // Get safe IDs
            $firstUser = $db->query("SELECT id FROM users WHERE deleted_at IS NULL LIMIT 1")->fetch();
            $userId = $firstUser ? $firstUser['id'] : 1;
            
            $firstCountry = $db->query("SELECT id FROM countries LIMIT 1")->fetch();
            $countryId = $firstCountry ? $firstCountry['id'] : 1;
            
            $sampleData = [
                [
                    'TRF001', 'PCK001', $userId, 'أحمد محمد علي', '+************', $countryId, 'الرياض، المملكة العربية السعودية',
                    'فاطمة أحمد', '+************', $countryId, 'القاهرة، مصر',
                    1000.00, 25.00, 4.75, 1025.00, 'USD', 'EGP', 'bank_transfer', 'دعم الأسرة', 'تحويل شهري', 'completed'
                ],
                [
                    'TRF002', 'PCK002', $userId, 'سارة عبدالله', '+************', $countryId, 'جدة، المملكة العربية السعودية',
                    'محمد حسن', '+************', $countryId, 'دبي، الإمارات العربية المتحدة',
                    500.00, 15.00, 3.67, 515.00, 'USD', 'AED', 'cash', 'تعليم', 'رسوم دراسية', 'pending'
                ],
                [
                    'TRF003', 'PCK003', $userId, 'عبدالله سالم', '+************', $countryId, 'الدمام، المملكة العربية السعودية',
                    'ليلى محمود', '+************', $countryId, 'عمان، الأردن',
                    750.00, 20.00, 0.71, 770.00, 'USD', 'JOD', 'credit_card', 'علاج طبي', 'مساعدة طبية', 'processing'
                ]
            ];
            
            $insertSQL = "
                INSERT INTO transfers (
                    transfer_code, pickup_code, user_id, sender_name, sender_phone, 
                    sender_country_id, sender_address, recipient_name, recipient_phone,
                    recipient_country_id, recipient_address, amount, fee, exchange_rate,
                    total_amount, currency_from, currency_to, payment_method, purpose, notes, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $db->prepare($insertSQL);
            
            foreach ($sampleData as $data) {
                try {
                    $stmt->execute($data);
                    echo "<div class='step success'>✅ تم إضافة تحويل: {$data[0]}</div>";
                } catch (Exception $e) {
                    echo "<div class='step error'>❌ فشل في إضافة تحويل {$data[0]}: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            }
        } else {
            echo "<div class='step success'>✅ يوجد $transferCount تحويل في النظام</div>";
        }
    } catch (Exception $e) {
        echo "<div class='step error'>❌ خطأ في إضافة التحويلات: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Step 6: Create indexes
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 6: إنشاء الفهارس</h3>";
    updateProgress(++$currentStep, $totalSteps, "تحسين الأداء");
    
    $indexes = [
        'idx_user_id' => 'user_id',
        'idx_status' => 'status',
        'idx_created_at' => 'created_at',
        'idx_transfer_code' => 'transfer_code',
        'idx_pickup_code' => 'pickup_code'
    ];
    
    foreach ($indexes as $indexName => $column) {
        try {
            $db->exec("CREATE INDEX $indexName ON transfers ($column)");
            echo "<div class='step success'>✅ تم إنشاء الفهرس: $indexName</div>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<div class='step info'>ℹ️ الفهرس $indexName موجود بالفعل</div>";
            } else {
                echo "<div class='step warning'>⚠️ تحذير: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        }
    }
    
    echo "</div>";
    
    // Step 7: Test queries
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 7: اختبار الاستعلامات</h3>";
    updateProgress(++$currentStep, $totalSteps, "اختبار النظام");
    
    try {
        $testQuery = "
            SELECT t.*, 
                   COALESCE(u.name, 'غير محدد') as user_name,
                   COALESCE(sc.name, 'غير محدد') as sender_country,
                   COALESCE(rc.name, 'غير محدد') as recipient_country
            FROM transfers t
            LEFT JOIN users u ON t.user_id = u.id AND u.deleted_at IS NULL
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.recipient_country_id = rc.id
            WHERE t.deleted_at IS NULL
            ORDER BY t.created_at DESC 
            LIMIT 3
        ";
        
        $stmt = $db->prepare($testQuery);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='step success'>✅ الاستعلام نجح! تم العثور على " . count($results) . " تحويل</div>";
        
        if (count($results) > 0) {
            echo "<div class='table-responsive'>";
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>الرمز</th><th>المرسل</th><th>المستلم</th><th>المبلغ</th><th>الحالة</th></tr></thead>";
            echo "<tbody>";
            foreach ($results as $row) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['transfer_code']) . "</td>";
                echo "<td>" . htmlspecialchars($row['sender_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['recipient_name']) . "</td>";
                echo "<td>$" . number_format($row['amount'], 2) . "</td>";
                echo "<td>" . htmlspecialchars($row['status']) . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='step error'>❌ فشل الاستعلام: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Step 8: Final summary
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 8: الملخص النهائي</h3>";
    updateProgress(++$currentStep, $totalSteps, "اكتمل الإصلاح");
    
    echo "<div class='alert alert-success'>";
    echo "<h4>🎉 تم الإصلاح الشامل بنجاح!</h4>";
    echo "<h5>✅ ما تم إصلاحه:</h5>";
    echo "<ul>";
    echo "<li>إزالة قيود المفاتيح الخارجية المشكلة</li>";
    echo "<li>إضافة جميع الأعمدة المطلوبة لجدول التحويلات</li>";
    echo "<li>إنشاء مستخدمين ودول تجريبية</li>";
    echo "<li>إضافة تحويلات تجريبية بنجاح</li>";
    echo "<li>إنشاء فهارس لتحسين الأداء</li>";
    echo "<li>اختبار جميع الاستعلامات والتأكد من عملها</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='text-center'>";
    echo "<h5>🚀 النظام جاهز للاستخدام!</h5>";
    echo "<div class='d-grid gap-2 d-md-flex justify-content-center'>";
    echo "<a href='transfers_safe.php' class='btn btn-success btn-lg'>الصفحة الآمنة</a>";
    echo "<a href='debug_ajax.php' class='btn btn-info'>اختبار AJAX</a>";
    echo "<a href='dashboard.php' class='btn btn-primary'>لوحة التحكم</a>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    
    echo "</div>";
    echo "</body>";
    echo "</html>";
    
} catch (Exception $e) {
    echo "<!DOCTYPE html>";
    echo "<html><head><meta charset='UTF-8'><title>خطأ</title></head><body>";
    echo "<div class='container mt-5'>";
    echo "<div class='alert alert-danger'>";
    echo "<h4>خطأ في قاعدة البيانات</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    echo "</div>";
    echo "</body></html>";
}

?>
