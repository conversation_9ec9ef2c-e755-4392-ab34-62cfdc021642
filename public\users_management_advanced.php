<?php

/**
 * Advanced Users Management
 * Elite Transfer System - Professional User Management Interface
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Check authentication
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = DatabaseManager::getInstance();
$userData = getUserData();

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'get_users':
                $page = intval($_POST['page'] ?? 1);
                $limit = intval($_POST['limit'] ?? 10);
                $search = trim($_POST['search'] ?? '');
                $role = trim($_POST['role'] ?? '');
                $status = trim($_POST['status'] ?? '');
                
                $offset = ($page - 1) * $limit;
                $conditions = ["(deleted_at IS NULL OR deleted_at = '')"];
                $params = [];
                
                if (!empty($search)) {
                    $conditions[] = "(name LIKE :search OR email LIKE :search OR phone LIKE :search)";
                    $params['search'] = "%$search%";
                }
                
                if (!empty($role)) {
                    $conditions[] = "role = :role";
                    $params['role'] = $role;
                }
                
                if (!empty($status)) {
                    $conditions[] = "status = :status";
                    $params['status'] = $status;
                }
                
                $whereClause = implode(' AND ', $conditions);
                
                // Get total count
                $totalQuery = "SELECT COUNT(*) as total FROM users WHERE $whereClause";
                $total = $db->selectOne($totalQuery, $params)['total'];
                
                // Get users
                $usersQuery = "SELECT * FROM users WHERE $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
                $users = $db->select($usersQuery, $params);
                
                echo json_encode([
                    'success' => true,
                    'users' => $users,
                    'total' => intval($total),
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'create_user':
                $name = trim($_POST['name'] ?? '');
                $email = trim($_POST['email'] ?? '');
                $phone = trim($_POST['phone'] ?? '');
                $role = trim($_POST['role'] ?? 'customer');
                $password = trim($_POST['password'] ?? '');
                $status = trim($_POST['status'] ?? 'active');
                
                // Validation
                if (empty($name) || empty($email) || empty($password)) {
                    throw new Exception('جميع الحقول مطلوبة');
                }
                
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('البريد الإلكتروني غير صحيح');
                }
                
                // Check if email exists
                $existingUser = $db->selectOne("SELECT id FROM users WHERE email = :email", ['email' => $email]);
                if ($existingUser) {
                    throw new Exception('البريد الإلكتروني مستخدم بالفعل');
                }
                
                // Create user
                $userData = [
                    'name' => $name,
                    'email' => $email,
                    'phone' => $phone,
                    'role' => $role,
                    'password' => password_hash($password, PASSWORD_DEFAULT),
                    'status' => $status,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $userId = $db->insert('users', $userData);
                
                if ($userId) {
                    logMessage('INFO', 'User created', [
                        'user_id' => $userId,
                        'email' => $email,
                        'created_by' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم إنشاء المستخدم بنجاح',
                        'user_id' => $userId
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception('فشل في إنشاء المستخدم');
                }
                break;
                
            case 'update_user':
                $userId = intval($_POST['user_id'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $email = trim($_POST['email'] ?? '');
                $phone = trim($_POST['phone'] ?? '');
                $role = trim($_POST['role'] ?? '');
                $status = trim($_POST['status'] ?? '');
                
                if (!$userId || empty($name) || empty($email)) {
                    throw new Exception('البيانات المطلوبة مفقودة');
                }
                
                // Check if email exists for other users
                $existingUser = $db->selectOne("SELECT id FROM users WHERE email = :email AND id != :id", [
                    'email' => $email,
                    'id' => $userId
                ]);
                
                if ($existingUser) {
                    throw new Exception('البريد الإلكتروني مستخدم بالفعل');
                }
                
                $updateData = [
                    'name' => $name,
                    'email' => $email,
                    'phone' => $phone,
                    'role' => $role,
                    'status' => $status,
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                // Update password if provided
                if (!empty($_POST['password'])) {
                    $updateData['password'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
                }
                
                $updated = $db->update('users', $updateData, 'id = :id', ['id' => $userId]);
                
                if ($updated) {
                    logMessage('INFO', 'User updated', [
                        'user_id' => $userId,
                        'updated_by' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم تحديث المستخدم بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception('فشل في تحديث المستخدم');
                }
                break;
                
            case 'delete_user':
                $userId = intval($_POST['user_id'] ?? 0);
                
                if (!$userId) {
                    throw new Exception('معرف المستخدم مطلوب');
                }
                
                // Soft delete
                $deleted = $db->update('users', [
                    'deleted_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = :id', ['id' => $userId]);
                
                if ($deleted) {
                    logMessage('INFO', 'User deleted', [
                        'user_id' => $userId,
                        'deleted_by' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم حذف المستخدم بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception('فشل في حذف المستخدم');
                }
                break;
                
            case 'toggle_status':
                $userId = intval($_POST['user_id'] ?? 0);
                $status = trim($_POST['status'] ?? '');
                
                if (!$userId || !in_array($status, ['active', 'inactive'])) {
                    throw new Exception('بيانات غير صحيحة');
                }
                
                $updated = $db->update('users', [
                    'status' => $status,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = :id', ['id' => $userId]);
                
                if ($updated) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم تغيير حالة المستخدم بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception('فشل في تغيير الحالة');
                }
                break;
                
            case 'get_user_stats':
                $stats = [
                    'total_users' => $db->selectOne("SELECT COUNT(*) as count FROM users WHERE (deleted_at IS NULL OR deleted_at = '')")['count'],
                    'active_users' => $db->selectOne("SELECT COUNT(*) as count FROM users WHERE status = 'active' AND (deleted_at IS NULL OR deleted_at = '')")['count'],
                    'inactive_users' => $db->selectOne("SELECT COUNT(*) as count FROM users WHERE status = 'inactive' AND (deleted_at IS NULL OR deleted_at = '')")['count'],
                    'new_users_today' => $db->selectOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE() AND (deleted_at IS NULL OR deleted_at = '')")['count'],
                    'new_users_week' => $db->selectOne("SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) AND (deleted_at IS NULL OR deleted_at = '')")['count'],
                    'roles' => [
                        'admin' => $db->selectOne("SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND (deleted_at IS NULL OR deleted_at = '')")['count'],
                        'manager' => $db->selectOne("SELECT COUNT(*) as count FROM users WHERE role = 'manager' AND (deleted_at IS NULL OR deleted_at = '')")['count'],
                        'agent' => $db->selectOne("SELECT COUNT(*) as count FROM users WHERE role = 'agent' AND (deleted_at IS NULL OR deleted_at = '')")['count'],
                        'customer' => $db->selectOne("SELECT COUNT(*) as count FROM users WHERE role = 'customer' AND (deleted_at IS NULL OR deleted_at = '')")['count']
                    ]
                ];
                
                echo json_encode([
                    'success' => true,
                    'stats' => $stats
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين المتقدمة - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
</head>
