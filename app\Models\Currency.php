<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Currency extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_ar',
        'code',
        'symbol',
        'decimal_places',
        'exchange_rate_to_usd',
        'is_active',
        'is_base_currency',
        'last_updated',
        'volatility_index',
        'daily_change_percentage',
        'market_cap',
        'trading_volume',
    ];

    protected $casts = [
        'decimal_places' => 'integer',
        'exchange_rate_to_usd' => 'decimal:8',
        'is_active' => 'boolean',
        'is_base_currency' => 'boolean',
        'last_updated' => 'datetime',
        'volatility_index' => 'decimal:4',
        'daily_change_percentage' => 'decimal:4',
        'market_cap' => 'decimal:2',
        'trading_volume' => 'decimal:2',
    ];

    /**
     * Get countries using this currency
     */
    public function countries(): HasMany
    {
        return $this->hasMany(Country::class);
    }

    /**
     * Get transfers using this as sender currency
     */
    public function senderTransfers(): HasMany
    {
        return $this->hasMany(Transfer::class, 'sender_currency_id');
    }

    /**
     * Get transfers using this as receiver currency
     */
    public function receiverTransfers(): HasMany
    {
        return $this->hasMany(Transfer::class, 'receiver_currency_id');
    }

    /**
     * Get exchange rates from this currency
     */
    public function exchangeRatesFrom(): HasMany
    {
        return $this->hasMany(ExchangeRate::class, 'from_currency_id');
    }

    /**
     * Get exchange rates to this currency
     */
    public function exchangeRatesTo(): HasMany
    {
        return $this->hasMany(ExchangeRate::class, 'to_currency_id');
    }

    /**
     * Format amount in this currency
     */
    public function formatAmount(float $amount): string
    {
        return $this->symbol . ' ' . number_format($amount, $this->decimal_places);
    }

    /**
     * Convert amount to USD
     */
    public function convertToUsd(float $amount): float
    {
        return $amount * $this->exchange_rate_to_usd;
    }

    /**
     * Convert amount from USD
     */
    public function convertFromUsd(float $usdAmount): float
    {
        return $usdAmount / $this->exchange_rate_to_usd;
    }

    /**
     * Get exchange rate to another currency
     */
    public function getExchangeRateTo(Currency $toCurrency): float
    {
        // First convert to USD, then to target currency
        $usdAmount = $this->convertToUsd(1);
        return $toCurrency->convertFromUsd($usdAmount);
    }

    /**
     * Check if currency is stable (low volatility)
     */
    public function isStable(): bool
    {
        return $this->volatility_index <= 0.05; // 5% volatility threshold
    }

    /**
     * Get currency statistics
     */
    public function getStatistics(): array
    {
        $today = now()->startOfDay();
        $thisMonth = now()->startOfMonth();

        return [
            'total_countries' => $this->countries()->count(),
            'today_transfers_sent' => $this->senderTransfers()->whereDate('created_at', $today)->count(),
            'today_transfers_received' => $this->receiverTransfers()->whereDate('created_at', $today)->count(),
            'today_volume_sent' => $this->senderTransfers()->whereDate('created_at', $today)->sum('amount'),
            'today_volume_received' => $this->receiverTransfers()->whereDate('created_at', $today)->sum('converted_amount'),
            'month_volume_sent' => $this->senderTransfers()->whereDate('created_at', '>=', $thisMonth)->sum('amount'),
            'month_volume_received' => $this->receiverTransfers()->whereDate('created_at', '>=', $thisMonth)->sum('converted_amount'),
            'exchange_rate_trend' => $this->getExchangeRateTrend(),
        ];
    }

    /**
     * Get exchange rate trend (last 7 days)
     */
    private function getExchangeRateTrend(): array
    {
        // This would typically fetch historical exchange rate data
        // For now, return mock data
        return [
            'direction' => $this->daily_change_percentage > 0 ? 'up' : 'down',
            'percentage' => abs($this->daily_change_percentage),
            'is_volatile' => !$this->isStable(),
        ];
    }

    /**
     * Update exchange rate
     */
    public function updateExchangeRate(float $newRate): void
    {
        $oldRate = $this->exchange_rate_to_usd;
        $changePercentage = (($newRate - $oldRate) / $oldRate) * 100;

        $this->update([
            'exchange_rate_to_usd' => $newRate,
            'daily_change_percentage' => $changePercentage,
            'last_updated' => now(),
        ]);

        // Log the rate change
        ExchangeRateHistory::create([
            'currency_id' => $this->id,
            'old_rate' => $oldRate,
            'new_rate' => $newRate,
            'change_percentage' => $changePercentage,
            'updated_at' => now(),
        ]);
    }
}
