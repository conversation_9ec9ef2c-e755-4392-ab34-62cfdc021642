<?php

/**
 * Fix Admin Pages
 * Elite Transfer System - Fix common issues with admin pages
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح الصفحات الإدارية - Elite Transfer System</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }";
echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 20px; }";
echo ".step { padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #007bff; background: #f8f9fa; }";
echo ".success { border-left-color: #28a745; background: #d4edda; }";
echo ".error { border-left-color: #dc3545; background: #f8d7da; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 class='text-white text-center mb-4'>🔧 إصلاح الصفحات الإدارية</h1>";

echo "<div class='fix-card'>";
echo "<h3>الخطوة 1: إنشاء مستخدم إداري</h3>";

try {
    require_once __DIR__ . '/includes/database_manager.php';
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    // Check if admin exists
    $stmt = $db->prepare("SELECT * FROM users WHERE role = 'admin' AND deleted_at IS NULL LIMIT 1");
    $stmt->execute();
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        echo "<div class='step'>إنشاء مستخدم إداري جديد...</div>";
        
        // Generate user code
        $stmt = $db->prepare("SELECT MAX(id) FROM users");
        $stmt->execute();
        $maxId = $stmt->fetchColumn() ?: 0;
        $userCode = 'ADM' . str_pad($maxId + 1, 3, '0', STR_PAD_LEFT);
        
        // Insert admin user
        $stmt = $db->prepare("
            INSERT INTO users (user_code, name, email, phone, password_hash, role, status, created_at) 
            VALUES (?, ?, ?, ?, ?, 'admin', 'active', NOW())
        ");
        
        $result = $stmt->execute([
            $userCode,
            'System Administrator',
            '<EMAIL>',
            '+966501234567',
            password_hash('admin123', PASSWORD_DEFAULT)
        ]);
        
        if ($result) {
            echo "<div class='step success'>✅ تم إنشاء المستخدم الإداري بنجاح</div>";
            echo "<div class='alert alert-info'>";
            echo "<strong>بيانات تسجيل الدخول:</strong><br>";
            echo "البريد الإلكتروني: <EMAIL><br>";
            echo "كلمة المرور: admin123";
            echo "</div>";
        } else {
            echo "<div class='step error'>❌ فشل في إنشاء المستخدم الإداري</div>";
        }
    } else {
        echo "<div class='step success'>✅ المستخدم الإداري موجود بالفعل</div>";
        echo "<div class='alert alert-info'>";
        echo "<strong>المستخدم الإداري الحالي:</strong><br>";
        echo "الاسم: " . htmlspecialchars($admin['name']) . "<br>";
        echo "البريد: " . htmlspecialchars($admin['email']) . "<br>";
        echo "الدور: " . htmlspecialchars($admin['role']);
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='step error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
}

echo "</div>";

echo "<div class='fix-card'>";
echo "<h3>الخطوة 2: تسجيل دخول تلقائي</h3>";

try {
    require_once __DIR__ . '/includes/session_helper.php';
    
    // Get admin user
    $stmt = $db->prepare("SELECT * FROM users WHERE role = 'admin' AND status = 'active' AND deleted_at IS NULL LIMIT 1");
    $stmt->execute();
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        // Create session
        $_SESSION['user_id'] = $admin['id'];
        $_SESSION['name'] = $admin['name'];
        $_SESSION['email'] = $admin['email'];
        $_SESSION['role'] = $admin['role'];
        $_SESSION['last_login_at'] = date('Y-m-d H:i:s');
        
        // Update last login
        $stmt = $db->prepare("UPDATE users SET last_login_at = NOW() WHERE id = ?");
        $stmt->execute([$admin['id']]);
        
        echo "<div class='step success'>✅ تم تسجيل الدخول تلقائياً</div>";
        echo "<div class='alert alert-success'>";
        echo "<strong>مرحباً " . htmlspecialchars($admin['name']) . "!</strong><br>";
        echo "تم تسجيل دخولك كمدير للنظام";
        echo "</div>";
    } else {
        echo "<div class='step error'>❌ لم يتم العثور على مستخدم إداري</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='step error'>❌ خطأ في تسجيل الدخول: " . $e->getMessage() . "</div>";
}

echo "</div>";

echo "<div class='fix-card'>";
echo "<h3>الخطوة 3: اختبار الوظائف</h3>";

$functions = ['isLoggedIn', 'getUserData', 'isAdmin'];
foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "<div class='step success'>✅ الوظيفة $func() متاحة</div>";
    } else {
        echo "<div class='step error'>❌ الوظيفة $func() غير متاحة</div>";
    }
}

// Test functions
if (function_exists('isLoggedIn') && function_exists('isAdmin')) {
    if (isLoggedIn()) {
        echo "<div class='step success'>✅ المستخدم مسجل دخول</div>";
        
        if (isAdmin()) {
            echo "<div class='step success'>✅ المستخدم لديه صلاحيات إدارية</div>";
        } else {
            echo "<div class='step error'>❌ المستخدم ليس لديه صلاحيات إدارية</div>";
        }
    } else {
        echo "<div class='step error'>❌ المستخدم غير مسجل دخول</div>";
    }
}

echo "</div>";

echo "<div class='fix-card'>";
echo "<h3>الخطوة 4: اختبار الصفحات الإدارية</h3>";

$adminPages = [
    'admin_users_enhanced.php' => 'إدارة المستخدمين',
    'admin_transfers_enhanced.php' => 'إدارة التحويلات',
    'admin_reports_enhanced.php' => 'التقارير',
    'admin_monitoring_enhanced.php' => 'مراقبة النظام',
    'admin_compliance_enhanced.php' => 'الامتثال التنظيمي'
];

foreach ($adminPages as $file => $title) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "<div class='step success'>✅ $title ($file) موجود</div>";
    } else {
        echo "<div class='step error'>❌ $title ($file) غير موجود</div>";
    }
}

echo "</div>";

echo "<div class='fix-card text-center'>";
echo "<h3>🚀 الصفحات جاهزة للاستخدام</h3>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<div class='d-grid gap-2'>";
echo "<a href='dashboard.php' class='btn btn-primary btn-lg'>لوحة التحكم الرئيسية</a>";
echo "<a href='admin_users_enhanced.php' class='btn btn-outline-primary'>إدارة المستخدمين</a>";
echo "<a href='admin_transfers_enhanced.php' class='btn btn-outline-success'>إدارة التحويلات</a>";
echo "</div>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<div class='d-grid gap-2'>";
echo "<a href='admin_reports_enhanced.php' class='btn btn-outline-info'>التقارير والإحصائيات</a>";
echo "<a href='admin_monitoring_enhanced.php' class='btn btn-outline-warning'>مراقبة النظام</a>";
echo "<a href='admin_compliance_enhanced.php' class='btn btn-outline-danger'>الامتثال التنظيمي</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h5>📋 معلومات مهمة:</h5>";
echo "<ul>";
echo "<li>تم إنشاء جلسة تسجيل دخول تلقائية</li>";
echo "<li>جميع الصفحات الإدارية جاهزة للاستخدام</li>";
echo "<li>قاعدة البيانات MySQL متصلة ومُحدثة</li>";
echo "<li>يمكنك الآن الوصول لجميع الوظائف الإدارية</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

?>
