<?php

/**
 * Countries Management System
 * Elite Transfer System - Advanced Countries Management
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Check authentication
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = DatabaseManager::getInstance();
$userData = getUserData();

// Create countries table if not exists with proper structure
try {
    $db->query("
        CREATE TABLE IF NOT EXISTS countries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            currency VARCHAR(3) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            status ENUM('active', 'inactive') DEFAULT 'active',
            flag_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP NULL,
            INDEX idx_code (code),
            INDEX idx_status (status),
            INDEX idx_deleted_at (deleted_at)
        )
    ");
} catch (Exception $e) {
    // Table might already exist
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'get_countries':
                $page = intval($_POST['page'] ?? 1);
                $limit = intval($_POST['limit'] ?? 10);
                $search = trim($_POST['search'] ?? '');
                $status = trim($_POST['status'] ?? '');
                
                $offset = ($page - 1) * $limit;
                $conditions = ["(deleted_at IS NULL OR deleted_at = '')"];
                $params = [];
                
                if (!empty($search)) {
                    $conditions[] = "(name LIKE :search OR code LIKE :search OR currency LIKE :search)";
                    $params['search'] = "%$search%";
                }
                
                if (!empty($status)) {
                    $conditions[] = "status = :status";
                    $params['status'] = $status;
                }
                
                $whereClause = implode(' AND ', $conditions);
                
                // Get total count
                $totalQuery = "SELECT COUNT(*) as total FROM countries WHERE $whereClause";
                $total = $db->selectOne($totalQuery, $params)['total'];
                
                // Get countries with transfer statistics
                $countriesQuery = "
                    SELECT
                        c.*,
                        COALESCE(COUNT(t.id), 0) as transfer_count,
                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume
                    FROM countries c
                    LEFT JOIN transfers t ON (c.id = t.sender_country_id OR c.id = t.recipient_country_id)
                        AND (t.deleted_at IS NULL OR t.deleted_at = '')
                    WHERE $whereClause
                    GROUP BY c.id, c.name, c.code, c.currency, c.exchange_rate, c.status, c.flag_url, c.created_at, c.updated_at, c.deleted_at
                    ORDER BY c.name ASC
                    LIMIT $limit OFFSET $offset
                ";
                $countries = $db->select($countriesQuery, $params);
                
                echo json_encode([
                    'success' => true,
                    'countries' => $countries,
                    'total' => intval($total),
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'create_country':
                $name = trim($_POST['name'] ?? '');
                $code = trim($_POST['code'] ?? '');
                $currency = trim($_POST['currency'] ?? '');
                $exchange_rate = floatval($_POST['exchange_rate'] ?? 1);
                $status = trim($_POST['status'] ?? 'active');
                $flag_url = trim($_POST['flag_url'] ?? '');
                
                // Validation
                if (empty($name) || empty($code) || empty($currency)) {
                    throw new Exception('الاسم والرمز والعملة مطلوبة');
                }
                
                if (strlen($code) !== 2) {
                    throw new Exception('رمز البلد يجب أن يكون حرفين فقط');
                }
                
                // Check if country exists
                $existingCountry = $db->selectOne("SELECT id FROM countries WHERE code = :code OR name = :name", [
                    'code' => $code,
                    'name' => $name
                ]);
                
                if ($existingCountry) {
                    throw new Exception('البلد موجود بالفعل');
                }
                
                // Create country
                $countryData = [
                    'name' => $name,
                    'code' => strtoupper($code),
                    'currency' => strtoupper($currency),
                    'exchange_rate' => $exchange_rate,
                    'status' => $status,
                    'flag_url' => $flag_url,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $countryId = $db->insert('countries', $countryData);
                
                if ($countryId) {
                    logMessage('INFO', 'Country created', [
                        'country_id' => $countryId,
                        'name' => $name,
                        'code' => $code,
                        'created_by' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم إنشاء البلد بنجاح',
                        'country_id' => $countryId
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception('فشل في إنشاء البلد');
                }
                break;
                
            case 'update_country':
                $countryId = intval($_POST['country_id'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $code = trim($_POST['code'] ?? '');
                $currency = trim($_POST['currency'] ?? '');
                $exchange_rate = floatval($_POST['exchange_rate'] ?? 1);
                $status = trim($_POST['status'] ?? 'active');
                $flag_url = trim($_POST['flag_url'] ?? '');
                
                if (!$countryId || empty($name) || empty($code) || empty($currency)) {
                    throw new Exception('البيانات المطلوبة مفقودة');
                }
                
                // Check if country exists for other records
                $existingCountry = $db->selectOne("SELECT id FROM countries WHERE (code = :code OR name = :name) AND id != :id", [
                    'code' => $code,
                    'name' => $name,
                    'id' => $countryId
                ]);
                
                if ($existingCountry) {
                    throw new Exception('البلد موجود بالفعل');
                }
                
                $updateData = [
                    'name' => $name,
                    'code' => strtoupper($code),
                    'currency' => strtoupper($currency),
                    'exchange_rate' => $exchange_rate,
                    'status' => $status,
                    'flag_url' => $flag_url,
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $updated = $db->update('countries', $updateData, 'id = :id', ['id' => $countryId]);
                
                if ($updated) {
                    logMessage('INFO', 'Country updated', [
                        'country_id' => $countryId,
                        'updated_by' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم تحديث البلد بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception('فشل في تحديث البلد');
                }
                break;
                
            case 'delete_country':
                $countryId = intval($_POST['country_id'] ?? 0);
                
                if (!$countryId) {
                    throw new Exception('معرف البلد مطلوب');
                }
                
                // Check if country is used in transfers
                $transferCount = $db->selectOne("
                    SELECT COUNT(*) as count 
                    FROM transfers 
                    WHERE (sender_country_id = :id OR recipient_country_id = :id)
                    AND (deleted_at IS NULL OR deleted_at = '')
                ", ['id' => $countryId])['count'];
                
                if ($transferCount > 0) {
                    throw new Exception("لا يمكن حذف البلد لأنه مستخدم في $transferCount تحويل");
                }
                
                // Soft delete
                $deleted = $db->update('countries', [
                    'deleted_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = :id', ['id' => $countryId]);
                
                if ($deleted) {
                    logMessage('INFO', 'Country deleted', [
                        'country_id' => $countryId,
                        'deleted_by' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم حذف البلد بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception('فشل في حذف البلد');
                }
                break;
                
            case 'toggle_status':
                $countryId = intval($_POST['country_id'] ?? 0);
                $status = trim($_POST['status'] ?? '');
                
                if (!$countryId || !in_array($status, ['active', 'inactive'])) {
                    throw new Exception('بيانات غير صحيحة');
                }
                
                $updated = $db->update('countries', [
                    'status' => $status,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = :id', ['id' => $countryId]);
                
                if ($updated) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم تغيير حالة البلد بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception('فشل في تغيير الحالة');
                }
                break;
                
            case 'get_country_stats':
                $stats = [
                    'total_countries' => $db->selectOne("SELECT COUNT(*) as count FROM countries WHERE (deleted_at IS NULL OR deleted_at = '')")['count'],
                    'active_countries' => $db->selectOne("SELECT COUNT(*) as count FROM countries WHERE status = 'active' AND (deleted_at IS NULL OR deleted_at = '')")['count'],
                    'inactive_countries' => $db->selectOne("SELECT COUNT(*) as count FROM countries WHERE status = 'inactive' AND (deleted_at IS NULL OR deleted_at = '')")['count'],
                    'countries_with_transfers' => 0 // Will be calculated if transfers table exists
                ];

                // Check if transfers table exists
                try {
                    $transfersCount = $db->selectOne("
                        SELECT COUNT(DISTINCT c.id) as count
                        FROM countries c
                        INNER JOIN transfers t ON (c.id = t.sender_country_id OR c.id = t.recipient_country_id)
                        WHERE (c.deleted_at IS NULL OR c.deleted_at = '')
                        AND (t.deleted_at IS NULL OR t.deleted_at = '')
                    ");
                    $stats['countries_with_transfers'] = $transfersCount['count'];
                } catch (Exception $e) {
                    // Transfers table doesn't exist or has different structure
                    $stats['countries_with_transfers'] = 0;
                }
                
                // Top countries by transfer volume
                $topCountries = [];
                try {
                    $topCountries = $db->select("
                        SELECT
                            c.name,
                            c.code,
                            COALESCE(COUNT(t.id), 0) as transfer_count,
                            COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_volume
                        FROM countries c
                        LEFT JOIN transfers t ON (c.id = t.sender_country_id OR c.id = t.recipient_country_id)
                            AND (t.deleted_at IS NULL OR t.deleted_at = '')
                        WHERE (c.deleted_at IS NULL OR c.deleted_at = '')
                        GROUP BY c.id, c.name, c.code
                        HAVING transfer_count > 0
                        ORDER BY total_volume DESC
                        LIMIT 5
                    ");
                } catch (Exception $e) {
                    // If transfers table doesn't exist, return empty array
                    $topCountries = [];
                }
                
                echo json_encode([
                    'success' => true,
                    'stats' => $stats,
                    'top_countries' => $topCountries
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'import_countries':
                // Sample countries data
                $sampleCountries = [
                    ['name' => 'المملكة العربية السعودية', 'code' => 'SA', 'currency' => 'SAR', 'exchange_rate' => 1.0],
                    ['name' => 'الإمارات العربية المتحدة', 'code' => 'AE', 'currency' => 'AED', 'exchange_rate' => 1.02],
                    ['name' => 'الكويت', 'code' => 'KW', 'currency' => 'KWD', 'exchange_rate' => 0.30],
                    ['name' => 'قطر', 'code' => 'QA', 'currency' => 'QAR', 'exchange_rate' => 3.64],
                    ['name' => 'البحرين', 'code' => 'BH', 'currency' => 'BHD', 'exchange_rate' => 0.38],
                    ['name' => 'عمان', 'code' => 'OM', 'currency' => 'OMR', 'exchange_rate' => 0.38],
                    ['name' => 'الأردن', 'code' => 'JO', 'currency' => 'JOD', 'exchange_rate' => 0.71],
                    ['name' => 'لبنان', 'code' => 'LB', 'currency' => 'LBP', 'exchange_rate' => 1507.5],
                    ['name' => 'مصر', 'code' => 'EG', 'currency' => 'EGP', 'exchange_rate' => 30.9],
                    ['name' => 'المغرب', 'code' => 'MA', 'currency' => 'MAD', 'exchange_rate' => 10.1],
                    ['name' => 'تونس', 'code' => 'TN', 'currency' => 'TND', 'exchange_rate' => 3.1],
                    ['name' => 'الجزائر', 'code' => 'DZ', 'currency' => 'DZD', 'exchange_rate' => 134.4],
                    ['name' => 'العراق', 'code' => 'IQ', 'currency' => 'IQD', 'exchange_rate' => 1460],
                    ['name' => 'سوريا', 'code' => 'SY', 'currency' => 'SYP', 'exchange_rate' => 2512],
                    ['name' => 'اليمن', 'code' => 'YE', 'currency' => 'YER', 'exchange_rate' => 250],
                    ['name' => 'السودان', 'code' => 'SD', 'currency' => 'SDG', 'exchange_rate' => 601],
                    ['name' => 'ليبيا', 'code' => 'LY', 'currency' => 'LYD', 'exchange_rate' => 4.8],
                    ['name' => 'الصومال', 'code' => 'SO', 'currency' => 'SOS', 'exchange_rate' => 570],
                    ['name' => 'جيبوتي', 'code' => 'DJ', 'currency' => 'DJF', 'exchange_rate' => 177.7],
                    ['name' => 'موريتانيا', 'code' => 'MR', 'currency' => 'MRU', 'exchange_rate' => 36.7]
                ];
                
                $imported = 0;
                $skipped = 0;
                
                foreach ($sampleCountries as $country) {
                    // Check if country already exists
                    $existing = $db->selectOne("SELECT id FROM countries WHERE code = :code", ['code' => $country['code']]);
                    
                    if (!$existing) {
                        $countryData = [
                            'name' => $country['name'],
                            'code' => $country['code'],
                            'currency' => $country['currency'],
                            'exchange_rate' => $country['exchange_rate'],
                            'status' => 'active',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];
                        
                        if ($db->insert('countries', $countryData)) {
                            $imported++;
                        }
                    } else {
                        $skipped++;
                    }
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => "تم استيراد $imported بلد، تم تخطي $skipped بلد موجود مسبقاً",
                    'imported' => $imported,
                    'skipped' => $skipped
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة البلدان - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --border-radius: 20px;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .countries-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .countries-header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            color: white;
            text-align: center;
        }

        .countries-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .controls-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 30px;
            color: white;
        }

        .countries-table {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 25px;
            color: white;
        }

        .table {
            color: white;
        }

        .table th {
            border-color: var(--glass-border);
            background: rgba(255, 255, 255, 0.05);
        }

        .table td {
            border-color: var(--glass-border);
        }

        .back-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-btn:hover {
            background: var(--info-gradient);
            color: white;
            transform: translateY(-2px);
        }

        .country-modal .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            color: white;
        }

        .country-modal .modal-header {
            border-bottom: 1px solid var(--glass-border);
        }

        .country-modal .modal-footer {
            border-top: 1px solid var(--glass-border);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            color: white;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--info-gradient);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-select option {
            background: #2c3e50;
            color: white;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-active {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }

        .status-inactive {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }

        .flag-img {
            width: 24px;
            height: 16px;
            object-fit: cover;
            border-radius: 3px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .table-responsive {
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <!-- Back Button -->
    <a href="dashboard_advanced.php" class="back-btn">
        <i class="bi bi-arrow-right me-2"></i>
        العودة للوحة التحكم
    </a>

    <div class="countries-container">
        <!-- Header -->
        <div class="countries-header">
            <h1><i class="bi bi-globe me-3"></i>إدارة البلدان</h1>
            <p>إدارة شاملة لبلدان النظام والعملات وأسعار الصرف</p>
        </div>

        <!-- Statistics -->
        <div class="controls-section">
            <h5 class="mb-3"><i class="bi bi-bar-chart me-2"></i>إحصائيات البلدان</h5>
            <div class="stats-grid" id="statsGrid">
                <!-- Stats will be loaded here -->
            </div>
        </div>

        <!-- Controls -->
        <div class="controls-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="openCountryModal()">
                            <i class="bi bi-plus-circle me-1"></i>
                            إضافة بلد جديد
                        </button>
                        <button class="btn btn-success" onclick="importCountries()">
                            <i class="bi bi-download me-1"></i>
                            استيراد البلدان
                        </button>
                        <button class="btn btn-info" onclick="refreshCountries()">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            تحديث
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row">
                        <div class="col-md-8">
                            <input type="text" class="form-control" id="searchInput" placeholder="البحث في البلدان...">
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Countries Table -->
        <div class="countries-table">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>العلم</th>
                            <th>اسم البلد</th>
                            <th>الرمز</th>
                            <th>العملة</th>
                            <th>سعر الصرف</th>
                            <th>عدد التحويلات</th>
                            <th>إجمالي الحجم</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="countriesTableBody">
                        <!-- Countries will be loaded here -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Countries pagination">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- Pagination will be loaded here -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- Country Modal -->
    <div class="modal fade country-modal" id="countryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="countryModalTitle">إضافة بلد جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="countryForm">
                        <input type="hidden" id="countryId" name="country_id">

                        <div class="mb-3">
                            <label for="countryName" class="form-label">اسم البلد *</label>
                            <input type="text" class="form-control" id="countryName" name="name" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <label for="countryCode" class="form-label">رمز البلد (حرفين) *</label>
                                <input type="text" class="form-control" id="countryCode" name="code" maxlength="2" required style="text-transform: uppercase;">
                            </div>
                            <div class="col-md-6">
                                <label for="countryCurrency" class="form-label">العملة *</label>
                                <input type="text" class="form-control" id="countryCurrency" name="currency" maxlength="3" required style="text-transform: uppercase;">
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label for="exchangeRate" class="form-label">سعر الصرف</label>
                                <input type="number" class="form-control" id="exchangeRate" name="exchange_rate" step="0.01" min="0" value="1">
                            </div>
                            <div class="col-md-6">
                                <label for="countryStatus" class="form-label">الحالة</label>
                                <select class="form-select" id="countryStatus" name="status">
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                        </div>

                        <div class="mt-3">
                            <label for="flagUrl" class="form-label">رابط العلم (اختياري)</label>
                            <input type="url" class="form-control" id="flagUrl" name="flag_url" placeholder="https://example.com/flag.png">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveCountry()">
                        <i class="bi bi-check-circle me-1"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let currentPage = 1;
        let currentLimit = 10;
        let currentSearch = '';
        let currentStatus = '';
        let isEditing = false;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadCountries();
            loadStats();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Search input
            document.getElementById('searchInput').addEventListener('input', function() {
                currentSearch = this.value;
                currentPage = 1;
                loadCountries();
            });

            // Status filter
            document.getElementById('statusFilter').addEventListener('change', function() {
                currentStatus = this.value;
                currentPage = 1;
                loadCountries();
            });
        }

        function loadStats() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_country_stats' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        renderStats(response.stats);
                    }
                }
            });
        }

        function renderStats(stats) {
            const statsGrid = document.getElementById('statsGrid');

            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${stats.total_countries}</div>
                    <div class="stat-label">إجمالي البلدان</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.active_countries}</div>
                    <div class="stat-label">البلدان النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.inactive_countries}</div>
                    <div class="stat-label">البلدان غير النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.countries_with_transfers}</div>
                    <div class="stat-label">بلدان لها تحويلات</div>
                </div>
            `;
        }

        function loadCountries() {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_countries',
                    page: currentPage,
                    limit: currentLimit,
                    search: currentSearch,
                    status: currentStatus
                },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        renderCountries(response.countries);
                        renderPagination(response.page, response.pages, response.total);
                    } else {
                        showError('فشل في تحميل البلدان');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                }
            });
        }

        function renderCountries(countries) {
            const tbody = document.getElementById('countriesTableBody');

            if (countries.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center">لا توجد بلدان</td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = countries.map(country => `
                <tr>
                    <td>
                        ${country.flag_url ?
                            `<img src="${country.flag_url}" alt="${country.name}" class="flag-img">` :
                            `<i class="bi bi-flag text-muted"></i>`
                        }
                    </td>
                    <td>${country.name}</td>
                    <td><span class="badge bg-secondary">${country.code}</span></td>
                    <td>${country.currency}</td>
                    <td>${parseFloat(country.exchange_rate).toFixed(2)}</td>
                    <td>${country.transfer_count || 0}</td>
                    <td>$${parseFloat(country.total_volume || 0).toLocaleString()}</td>
                    <td>
                        <span class="status-badge status-${country.status}">
                            ${country.status === 'active' ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editCountry(${country.id})" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-${country.status === 'active' ? 'warning' : 'success'}"
                                    onclick="toggleCountryStatus(${country.id}, '${country.status === 'active' ? 'inactive' : 'active'}')"
                                    title="${country.status === 'active' ? 'إلغاء التفعيل' : 'تفعيل'}">
                                <i class="bi bi-${country.status === 'active' ? 'pause' : 'play'}"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteCountry(${country.id})" title="حذف">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function renderPagination(page, pages, total) {
            const pagination = document.getElementById('pagination');

            if (pages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            // Previous button
            if (page > 1) {
                paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${page - 1})">السابق</a></li>`;
            }

            // Page numbers
            for (let i = Math.max(1, page - 2); i <= Math.min(pages, page + 2); i++) {
                paginationHTML += `<li class="page-item ${i === page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>`;
            }

            // Next button
            if (page < pages) {
                paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${page + 1})">التالي</a></li>`;
            }

            pagination.innerHTML = paginationHTML;
        }

        function changePage(page) {
            currentPage = page;
            loadCountries();
        }

        function openCountryModal(countryData = null) {
            isEditing = !!countryData;

            document.getElementById('countryModalTitle').textContent = isEditing ? 'تعديل البلد' : 'إضافة بلد جديد';

            if (isEditing) {
                document.getElementById('countryId').value = countryData.id;
                document.getElementById('countryName').value = countryData.name;
                document.getElementById('countryCode').value = countryData.code;
                document.getElementById('countryCurrency').value = countryData.currency;
                document.getElementById('exchangeRate').value = countryData.exchange_rate;
                document.getElementById('countryStatus').value = countryData.status;
                document.getElementById('flagUrl').value = countryData.flag_url || '';
            } else {
                document.getElementById('countryForm').reset();
                document.getElementById('countryId').value = '';
                document.getElementById('exchangeRate').value = '1';
            }

            const modal = new bootstrap.Modal(document.getElementById('countryModal'));
            modal.show();
        }

        function editCountry(countryId) {
            // Find country data from current loaded countries
            const tbody = document.getElementById('countriesTableBody');
            const row = tbody.querySelector(`button[onclick="editCountry(${countryId})"]`).closest('tr');
            const cells = row.querySelectorAll('td');

            const countryData = {
                id: countryId,
                name: cells[1].textContent,
                code: cells[2].querySelector('.badge').textContent,
                currency: cells[3].textContent,
                exchange_rate: parseFloat(cells[4].textContent),
                status: cells[7].querySelector('.status-badge').classList.contains('status-active') ? 'active' : 'inactive',
                flag_url: cells[0].querySelector('img') ? cells[0].querySelector('img').src : ''
            };

            openCountryModal(countryData);
        }

        function saveCountry() {
            const form = document.getElementById('countryForm');
            const formData = new FormData(form);
            formData.append('action', isEditing ? 'update_country' : 'create_country');

            $.ajax({
                url: '',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        bootstrap.Modal.getInstance(document.getElementById('countryModal')).hide();
                        loadCountries();
                        loadStats();

                        Swal.fire({
                            icon: 'success',
                            title: 'نجح!',
                            text: response.message,
                            confirmButtonText: 'ممتاز'
                        });
                    } else {
                        showError(response.message || 'فشل في حفظ البلد');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                }
            });
        }

        function deleteCountry(countryId) {
            Swal.fire({
                title: 'تأكيد الحذف',
                text: 'هل أنت متأكد من حذف هذا البلد؟ لا يمكن التراجع عن هذا الإجراء.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '',
                        method: 'POST',
                        data: {
                            action: 'delete_country',
                            country_id: countryId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response && response.success) {
                                loadCountries();
                                loadStats();

                                Swal.fire({
                                    icon: 'success',
                                    title: 'تم الحذف',
                                    text: response.message,
                                    confirmButtonText: 'حسناً'
                                });
                            } else {
                                showError(response.message || 'فشل في حذف البلد');
                            }
                        },
                        error: function() {
                            showError('خطأ في الاتصال بالخادم');
                        }
                    });
                }
            });
        }

        function toggleCountryStatus(countryId, newStatus) {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'toggle_status',
                    country_id: countryId,
                    status: newStatus
                },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        loadCountries();
                        loadStats();

                        Swal.fire({
                            icon: 'success',
                            title: 'تم التحديث',
                            text: response.message,
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        showError(response.message || 'فشل في تغيير الحالة');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                }
            });
        }

        function importCountries() {
            Swal.fire({
                title: 'استيراد البلدان',
                text: 'سيتم استيراد قائمة البلدان العربية الافتراضية. هل تريد المتابعة؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، استورد',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '',
                        method: 'POST',
                        data: { action: 'import_countries' },
                        dataType: 'json',
                        success: function(response) {
                            if (response && response.success) {
                                loadCountries();
                                loadStats();

                                Swal.fire({
                                    icon: 'success',
                                    title: 'تم الاستيراد',
                                    text: response.message,
                                    confirmButtonText: 'ممتاز'
                                });
                            } else {
                                showError(response.message || 'فشل في استيراد البلدان');
                            }
                        },
                        error: function() {
                            showError('خطأ في الاتصال بالخادم');
                        }
                    });
                }
            });
        }

        function refreshCountries() {
            loadCountries();
            loadStats();

            Swal.fire({
                icon: 'success',
                title: 'تم التحديث',
                text: 'تم تحديث قائمة البلدان',
                timer: 2000,
                showConfirmButton: false
            });
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: message,
                confirmButtonText: 'حسناً'
            });
        }
    </script>
</body>
</html>
