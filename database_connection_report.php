<?php

/**
 * Database Connection Report
 * Elite Transfer System - Comprehensive Database Status Report
 */

echo "📊 DATABASE CONNECTION REPORT\n";
echo "Elite Transfer System\n";
echo str_repeat("=", 70) . "\n";
echo "Generated: " . date('Y-m-d H:i:s') . "\n\n";

// Load environment
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Current configuration
$currentDB = $_ENV['DB_CONNECTION'] ?? 'sqlite';

echo "🔧 CURRENT CONFIGURATION\n";
echo str_repeat("-", 40) . "\n";
echo "Database Type: " . strtoupper($currentDB) . "\n";
echo "Environment: " . ($_ENV['APP_ENV'] ?? 'production') . "\n";
echo "Debug Mode: " . ($_ENV['APP_DEBUG'] ?? 'false') . "\n";
echo "App URL: " . ($_ENV['APP_URL'] ?? 'http://localhost') . "\n\n";

// Test SQLite
echo "🗄️  SQLITE STATUS\n";
echo str_repeat("-", 40) . "\n";

$sqliteFile = __DIR__ . '/database/elite_transfer_production.db';
$sqliteStatus = [
    'available' => false,
    'file_exists' => file_exists($sqliteFile),
    'file_size' => 0,
    'version' => 'Unknown',
    'tables' => 0,
    'records' => 0,
    'performance' => 0
];

if ($sqliteStatus['file_exists']) {
    $sqliteStatus['file_size'] = filesize($sqliteFile);
    
    try {
        $pdo = new PDO("sqlite:$sqliteFile", null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        $sqliteStatus['version'] = $pdo->query("SELECT sqlite_version()")->fetchColumn();
        $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
        $sqliteStatus['tables'] = count($tables);
        
        if ($sqliteStatus['tables'] > 0) {
            $sqliteStatus['records'] = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
        }
        
        // Performance test
        $start = microtime(true);
        for ($i = 0; $i < 5; $i++) {
            $pdo->query("SELECT 1")->fetchColumn();
        }
        $end = microtime(true);
        $sqliteStatus['performance'] = round(($end - $start) * 1000, 2);
        
        $sqliteStatus['available'] = true;
        
    } catch (Exception $e) {
        $sqliteStatus['error'] = $e->getMessage();
    }
}

echo "File Exists: " . ($sqliteStatus['file_exists'] ? "✅ Yes" : "❌ No") . "\n";
echo "File Size: " . number_format($sqliteStatus['file_size']) . " bytes\n";
echo "Version: " . $sqliteStatus['version'] . "\n";
echo "Tables: " . $sqliteStatus['tables'] . "\n";
echo "Records: " . $sqliteStatus['records'] . "\n";
echo "Performance: " . $sqliteStatus['performance'] . "ms (5 queries)\n";
echo "Status: " . ($sqliteStatus['available'] ? "🟢 Available" : "🔴 Not Available") . "\n";
if (isset($sqliteStatus['error'])) {
    echo "Error: " . $sqliteStatus['error'] . "\n";
}
echo "\n";

// Test MySQL
echo "🐬 MYSQL STATUS\n";
echo str_repeat("-", 40) . "\n";

$mysqlStatus = [
    'server_available' => false,
    'database_exists' => false,
    'version' => 'Unknown',
    'tables' => 0,
    'records' => 0,
    'performance' => 0,
    'charset' => 'Unknown'
];

try {
    // Test server connection
    $pdo = new PDO("mysql:host=localhost;port=3306", 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    $mysqlStatus['server_available'] = true;
    $mysqlStatus['version'] = $pdo->query("SELECT VERSION()")->fetchColumn();
    
    // Check database
    $databases = $pdo->query("SHOW DATABASES LIKE 'elite_transfer'")->fetchAll();
    $mysqlStatus['database_exists'] = !empty($databases);
    
    if ($mysqlStatus['database_exists']) {
        // Connect to database
        $pdo = new PDO("mysql:host=localhost;port=3306;dbname=elite_transfer", 'root', '', [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        $tables = $pdo->query("SHOW TABLES")->fetchAll();
        $mysqlStatus['tables'] = count($tables);
        
        if ($mysqlStatus['tables'] > 0) {
            $mysqlStatus['records'] = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
        }
        
        $mysqlStatus['charset'] = $pdo->query("SELECT @@character_set_database")->fetchColumn();
        
        // Performance test
        $start = microtime(true);
        for ($i = 0; $i < 5; $i++) {
            $pdo->query("SELECT 1")->fetchColumn();
        }
        $end = microtime(true);
        $mysqlStatus['performance'] = round(($end - $start) * 1000, 2);
    }
    
} catch (Exception $e) {
    $mysqlStatus['error'] = $e->getMessage();
}

echo "Server: " . ($mysqlStatus['server_available'] ? "✅ Available" : "❌ Not Available") . "\n";
echo "Database: " . ($mysqlStatus['database_exists'] ? "✅ Exists" : "❌ Not Found") . "\n";
echo "Version: " . $mysqlStatus['version'] . "\n";
echo "Tables: " . $mysqlStatus['tables'] . "\n";
echo "Records: " . $mysqlStatus['records'] . "\n";
echo "Charset: " . $mysqlStatus['charset'] . "\n";
echo "Performance: " . $mysqlStatus['performance'] . "ms (5 queries)\n";
echo "Status: " . ($mysqlStatus['server_available'] && $mysqlStatus['database_exists'] ? "🟢 Available" : "🔴 Not Available") . "\n";
if (isset($mysqlStatus['error'])) {
    echo "Error: " . $mysqlStatus['error'] . "\n";
}
echo "\n";

// Current connection test
echo "🔗 CURRENT CONNECTION TEST\n";
echo str_repeat("-", 40) . "\n";

$currentStatus = [
    'connected' => false,
    'type' => 'Unknown',
    'database' => 'Unknown',
    'tables' => 0,
    'users' => 0,
    'countries' => 0,
    'transfers' => 0
];

try {
    require_once 'public/includes/database_manager.php';
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    $connectionInfo = $dbManager->getConnectionInfo();
    $currentStatus['connected'] = true;
    $currentStatus['type'] = $connectionInfo['type'];
    $currentStatus['database'] = $connectionInfo['database'];
    
    // Get statistics
    $currentStatus['users'] = $db->query("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL")->fetchColumn();
    $currentStatus['countries'] = $db->query("SELECT COUNT(*) FROM countries WHERE is_active = 1")->fetchColumn();
    $currentStatus['transfers'] = $db->query("SELECT COUNT(*) FROM transfers WHERE deleted_at IS NULL")->fetchColumn();
    
} catch (Exception $e) {
    $currentStatus['error'] = $e->getMessage();
}

echo "Status: " . ($currentStatus['connected'] ? "🟢 Connected" : "🔴 Disconnected") . "\n";
echo "Type: " . $currentStatus['type'] . "\n";
echo "Database: " . $currentStatus['database'] . "\n";
echo "Users: " . number_format($currentStatus['users']) . "\n";
echo "Countries: " . number_format($currentStatus['countries']) . "\n";
echo "Transfers: " . number_format($currentStatus['transfers']) . "\n";
if (isset($currentStatus['error'])) {
    echo "Error: " . $currentStatus['error'] . "\n";
}
echo "\n";

// Recommendations
echo "💡 RECOMMENDATIONS\n";
echo str_repeat("-", 40) . "\n";

if ($currentDB === 'sqlite') {
    echo "Current: SQLite Database\n";
    if ($sqliteStatus['available']) {
        echo "✅ SQLite is working perfectly\n";
        echo "📊 Performance: Good for current setup\n";
        echo "🎯 Suitable for: Development, Small-Medium apps\n";
        
        if ($mysqlStatus['server_available']) {
            echo "\n🚀 Upgrade Option Available:\n";
            echo "   MySQL is available and ready\n";
            echo "   Better for: Production, High-traffic apps\n";
            echo "   To switch: php switch_to_mysql.php\n";
        }
    } else {
        echo "❌ SQLite has issues\n";
        echo "🔧 Fix: php setup_production_database.php\n";
    }
} else {
    echo "Current: MySQL Database\n";
    if ($mysqlStatus['server_available'] && $mysqlStatus['database_exists']) {
        echo "✅ MySQL is working perfectly\n";
        echo "📊 Performance: Excellent for production\n";
        echo "🎯 Suitable for: Production, High-traffic apps\n";
    } else {
        echo "❌ MySQL has issues\n";
        if (!$mysqlStatus['server_available']) {
            echo "🔧 Fix: Start MySQL in XAMPP Control Panel\n";
        }
        if (!$mysqlStatus['database_exists']) {
            echo "🔧 Fix: php switch_to_mysql.php\n";
        }
        
        if ($sqliteStatus['available']) {
            echo "\n🔄 Fallback Option:\n";
            echo "   SQLite is available as backup\n";
            echo "   To switch: php switch_to_sqlite.php\n";
        }
    }
}

echo "\n🛠️  AVAILABLE TOOLS\n";
echo str_repeat("-", 40) . "\n";
echo "• Quick Test: php quick_db_test.php\n";
echo "• Full Test: php test_database_connection.php\n";
echo "• MySQL Test: php test_mysql_connection.php\n";
echo "• Interactive Manager: php database_connection_manager.php\n";
echo "• Switch to MySQL: php switch_to_mysql.php\n";
echo "• Switch to SQLite: php switch_to_sqlite.php\n";
echo "• Web Interface: http://localhost/database_status.php\n";

echo "\n📈 PERFORMANCE COMPARISON\n";
echo str_repeat("-", 40) . "\n";
echo "SQLite Performance: " . $sqliteStatus['performance'] . "ms\n";
echo "MySQL Performance: " . $mysqlStatus['performance'] . "ms\n";

if ($sqliteStatus['performance'] > 0 && $mysqlStatus['performance'] > 0) {
    if ($sqliteStatus['performance'] < $mysqlStatus['performance']) {
        echo "🏆 SQLite is faster for simple queries\n";
    } else {
        echo "🏆 MySQL is faster for complex operations\n";
    }
}

echo "\n" . str_repeat("=", 70) . "\n";
echo "🎯 SUMMARY\n";
echo str_repeat("=", 70) . "\n";

echo "Current Database: " . strtoupper($currentDB) . " ";
echo ($currentStatus['connected'] ? "🟢 WORKING" : "🔴 ISSUES") . "\n";

echo "SQLite: " . ($sqliteStatus['available'] ? "🟢 Available" : "🔴 Issues") . "\n";
echo "MySQL: " . ($mysqlStatus['server_available'] && $mysqlStatus['database_exists'] ? "🟢 Available" : "🔴 Issues") . "\n";

echo "\nSystem Status: ";
if ($currentStatus['connected']) {
    echo "🟢 READY FOR USE\n";
} else {
    echo "🔴 NEEDS ATTENTION\n";
}

echo "\nReport completed at: " . date('Y-m-d H:i:s') . "\n";

?>
