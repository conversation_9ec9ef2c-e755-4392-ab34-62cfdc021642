<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transfers', function (Blueprint $table) {
            $table->id();
            $table->string('transfer_code')->unique();
            $table->foreignId('sender_id')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('receiver_id')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('agent_id')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('branch_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('sender_country_id')->constrained('countries');
            $table->foreignId('receiver_country_id')->constrained('countries');
            $table->foreignId('sender_currency_id')->constrained('currencies');
            $table->foreignId('receiver_currency_id')->constrained('currencies');
            $table->decimal('amount', 15, 2);
            $table->decimal('exchange_rate', 15, 6);
            $table->decimal('converted_amount', 15, 2);
            $table->decimal('fee_amount', 15, 2)->default(0.00);
            $table->decimal('total_amount', 15, 2);
            $table->string('purpose')->nullable();
            $table->text('notes')->nullable();
            $table->enum('status', [
                'pending', 'processing', 'ready_for_pickup', 'completed', 
                'cancelled', 'failed', 'refunded', 'on_hold'
            ])->default('pending');
            $table->enum('type', [
                'individual', 'bulk', 'scheduled', 'anonymous', 'business'
            ])->default('individual');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->string('pickup_code', 20);
            $table->string('pickup_location')->nullable();
            $table->string('receiver_name');
            $table->string('receiver_phone');
            $table->text('receiver_address')->nullable();
            $table->string('receiver_id_number')->nullable();
            $table->string('sender_name');
            $table->string('sender_phone');
            $table->text('sender_address')->nullable();
            $table->string('sender_id_number')->nullable();
            $table->boolean('is_anonymous')->default(false);
            $table->integer('risk_score')->default(0);
            $table->enum('fraud_check_status', [
                'pending', 'approved', 'flagged', 'rejected'
            ])->default('pending');
            $table->enum('compliance_status', [
                'pending', 'approved', 'rejected', 'under_review'
            ])->default('pending');
            $table->json('attachments')->nullable();
            $table->json('tracking_data')->nullable();
            $table->json('ai_recommendations')->nullable();
            $table->decimal('commission_amount', 15, 2)->default(0.00);
            $table->decimal('profit_amount', 15, 2)->default(0.00);
            $table->string('external_reference')->nullable();
            $table->string('swift_code')->nullable();
            $table->json('bank_details')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['transfer_code']);
            $table->index(['pickup_code']);
            $table->index(['status']);
            $table->index(['type']);
            $table->index(['sender_id', 'status']);
            $table->index(['receiver_id', 'status']);
            $table->index(['agent_id', 'status']);
            $table->index(['branch_id', 'status']);
            $table->index(['sender_country_id', 'receiver_country_id']);
            $table->index(['created_at', 'status']);
            $table->index(['scheduled_at']);
            $table->index(['fraud_check_status']);
            $table->index(['compliance_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transfers');
    }
};
