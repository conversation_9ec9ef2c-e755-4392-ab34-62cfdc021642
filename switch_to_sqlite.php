<?php

/**
 * Switch to SQLite Database
 * Elite Transfer System - Switch back to SQLite
 */

echo "🔄 Switching to SQLite Database - Elite Transfer System\n";
echo str_repeat("=", 60) . "\n\n";

// Step 1: Check SQLite database file
echo "📋 Step 1: Checking SQLite database file...\n";
$sqliteFile = __DIR__ . '/database/elite_transfer_production.db';

if (!file_exists($sqliteFile)) {
    echo "❌ SQLite database file not found: database/elite_transfer_production.db\n";
    echo "🔧 Creating SQLite database...\n";
    
    // Create database directory if not exists
    $dbDir = dirname($sqliteFile);
    if (!is_dir($dbDir)) {
        mkdir($dbDir, 0755, true);
    }
    
    // Create empty database file
    touch($sqliteFile);
    echo "✅ SQLite database file created\n";
} else {
    $fileSize = filesize($sqliteFile);
    echo "✅ SQLite database file exists (" . number_format($fileSize) . " bytes)\n";
}

// Step 2: Test SQLite connection
echo "\n📋 Step 2: Testing SQLite connection...\n";
try {
    $pdo = new PDO("sqlite:$sqliteFile", null, null, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    $version = $pdo->query("SELECT sqlite_version()")->fetchColumn();
    echo "✅ SQLite connection successful\n";
    echo "   Version: $version\n";
    
    // Check tables
    $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
    echo "   Tables found: " . count($tables) . "\n";
    
} catch (Exception $e) {
    echo "❌ SQLite connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 3: Update .env file
echo "\n📋 Step 3: Updating .env file...\n";
$envFile = __DIR__ . '/.env';
$envContent = file_get_contents($envFile);

// Update database configuration
$updates = [
    'DB_CONNECTION=mysql' => 'DB_CONNECTION=sqlite',
    'DB_DATABASE=elite_transfer' => 'DB_DATABASE=database/elite_transfer_production.db',
    'DB_HOST=localhost' => '# DB_HOST=localhost',
    'DB_PORT=3306' => '# DB_PORT=3306',
    'DB_USERNAME=root' => '# DB_USERNAME=root',
    'DB_PASSWORD=' => '# DB_PASSWORD='
];

foreach ($updates as $old => $new) {
    $envContent = str_replace($old, $new, $envContent);
}

file_put_contents($envFile, $envContent);
echo "✅ .env file updated successfully\n";

// Step 4: Test new connection
echo "\n📋 Step 4: Testing new SQLite connection...\n";
try {
    // Reload environment
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
    
    $pdo = new PDO("sqlite:$sqliteFile", null, null, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ SQLite connection successful\n";
    
    // Check if tables exist
    $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
    echo "   Tables found: " . count($tables) . "\n";
    
} catch (Exception $e) {
    echo "❌ SQLite connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 5: Setup database if needed
echo "\n📋 Step 5: Setting up database structure...\n";
if (count($tables) < 10) {
    echo "⚠️  Database needs setup, running setup script...\n";
    
    // Run setup script
    $output = [];
    $returnCode = 0;
    exec('php setup_production_database.php 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ Database setup completed successfully\n";
    } else {
        echo "⚠️  Database setup had some issues, but continuing...\n";
        foreach ($output as $line) {
            echo "   $line\n";
        }
    }
} else {
    echo "✅ Database structure is already complete\n";
}

// Step 6: Final verification
echo "\n📋 Step 6: Final verification...\n";
try {
    require_once 'public/includes/database_manager.php';
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    $connectionInfo = $dbManager->getConnectionInfo();
    echo "✅ Database Manager connected successfully\n";
    echo "   Type: {$connectionInfo['type']}\n";
    echo "   Database: {$connectionInfo['database']}\n";
    echo "   Host: {$connectionInfo['host']}\n";
    
    // Test basic operations
    $userCount = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $countryCount = $db->query("SELECT COUNT(*) FROM countries")->fetchColumn();
    
    echo "   Users: $userCount\n";
    echo "   Countries: $countryCount\n";
    
} catch (Exception $e) {
    echo "❌ Final verification failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 Successfully switched to SQLite!\n\n";

echo "📝 Summary:\n";
echo "   ✅ SQLite file: Available\n";
echo "   ✅ Database: Configured\n";
echo "   ✅ .env file: Updated\n";
echo "   ✅ Database structure: Ready\n";
echo "   ✅ Connection: Working\n\n";

echo "🚀 Your application is now using SQLite database!\n";
echo "💡 SQLite is perfect for development and small to medium applications.\n\n";

echo "🔧 Next steps:\n";
echo "1. Test the application: http://localhost/dashboard.php\n";
echo "2. Check database status: http://localhost/database_status.php\n";
echo "3. Run quick test: php quick_db_test.php\n\n";

echo "📊 Benefits of SQLite:\n";
echo "   • No server setup required\n";
echo "   • Fast for read operations\n";
echo "   • Portable database file\n";
echo "   • Perfect for development\n";
echo "   • Zero configuration\n";

?>
