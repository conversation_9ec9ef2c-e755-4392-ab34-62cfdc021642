<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branches', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->foreignId('country_id')->constrained()->cascadeOnDelete();
            $table->string('city');
            $table->text('address');
            $table->string('phone');
            $table->string('email')->nullable();
            $table->foreignId('manager_id')->nullable()->constrained('users')->nullOnDelete();
            $table->boolean('is_active')->default(true);
            $table->json('operating_hours')->nullable();
            $table->json('supported_currencies')->nullable();
            $table->decimal('daily_limit', 15, 2)->default(100000.00);
            $table->decimal('monthly_limit', 15, 2)->default(1000000.00);
            $table->decimal('commission_rate', 5, 2)->default(2.50);
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->string('timezone')->default('UTC');
            $table->json('services_offered')->nullable();
            $table->string('contact_person')->nullable();
            $table->string('license_number')->nullable();
            $table->date('established_date')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['country_id', 'is_active']);
            $table->index(['code']);
            $table->index(['latitude', 'longitude']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branches');
    }
};
