<?php

/**
 * Print Transfer Receipt
 * Elite Transfer System - Professional Transfer Receipt Printing
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

$db = DatabaseManager::getInstance();
$transferId = intval($_GET['id'] ?? 0);

if (!$transferId) {
    die('معرف التحويل مطلوب');
}

// Get transfer details with related data
$transfer = $db->selectOne("
    SELECT t.*,
           COALESCE(u.name, 'غير محدد') as user_name,
           COALESCE(sc.name, 'غير محدد') as sender_country_name, 
           COALESCE(sc.currency, 'USD') as sender_currency,
           COALESCE(rc.name, 'غير محدد') as recipient_country_name, 
           COALESCE(rc.currency, 'USD') as recipient_currency
    FROM transfers t
    LEFT JOIN users u ON t.user_id = u.id AND (u.deleted_at IS NULL OR u.deleted_at = '')
    LEFT JOIN countries sc ON t.sender_country_id = sc.id
    LEFT JOIN countries rc ON t.recipient_country_id = rc.id
    WHERE t.id = :id AND (t.deleted_at IS NULL OR t.deleted_at = '')
", ['id' => $transferId]);

if (!$transfer) {
    die('التحويل غير موجود');
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال التحويل - <?= $transfer['transfer_code'] ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: white;
            color: #333;
        }
        
        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .receipt-header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-logo {
            font-size: 2.5rem;
            font-weight: 800;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .receipt-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #495057;
            margin-bottom: 10px;
        }
        
        .receipt-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px dotted #dee2e6;
        }
        
        .info-label {
            font-weight: 600;
            color: #6c757d;
        }
        
        .info-value {
            font-weight: 700;
            color: #495057;
        }
        
        .amount-section {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin: 30px 0;
        }
        
        .amount-title {
            font-size: 1.1rem;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .amount-value {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 5px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 700;
            font-size: 0.9rem;
            margin: 10px 0;
        }
        
        .status-pending { background: rgba(255, 193, 7, 0.2); color: #ff8f00; border: 2px solid #ffc107; }
        .status-processing { background: rgba(13, 110, 253, 0.2); color: #0d6efd; border: 2px solid #007bff; }
        .status-completed { background: rgba(25, 135, 84, 0.2); color: #198754; border: 2px solid #28a745; }
        .status-cancelled { background: rgba(220, 53, 69, 0.2); color: #dc3545; border: 2px solid #dc3545; }
        
        .qr-section {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .footer-section {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            color: #6c757d;
        }
        
        .print-buttons {
            text-align: center;
            margin: 20px 0;
            gap: 10px;
        }
        
        .btn-print {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            margin: 0 10px;
        }
        
        .btn-back {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            margin: 0 10px;
        }
        
        @media print {
            .print-buttons {
                display: none !important;
            }
            
            body {
                background: white !important;
            }
            
            .receipt-container {
                box-shadow: none !important;
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }
        }
        
        @media (max-width: 768px) {
            .receipt-info {
                grid-template-columns: 1fr;
            }
            
            .amount-value {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Print Buttons -->
        <div class="print-buttons">
            <button class="btn btn-print" onclick="window.print()">
                <i class="bi bi-printer-fill me-2"></i>
                طباعة الإيصال
            </button>
            <button class="btn btn-back" onclick="window.close()">
                <i class="bi bi-arrow-left me-2"></i>
                إغلاق
            </button>
        </div>
        
        <!-- Receipt Header -->
        <div class="receipt-header">
            <div class="company-logo">
                <i class="bi bi-bank2 me-3"></i>
                <?= SYSTEM_NAME ?>
            </div>
            <div class="receipt-title">إيصال تحويل مالي</div>
            <div class="text-muted">تاريخ الطباعة: <?= date('Y-m-d H:i:s') ?></div>
        </div>
        
        <!-- Transfer Code and Status -->
        <div class="text-center mb-4">
            <h3 class="mb-3">
                <i class="bi bi-qr-code me-2"></i>
                رمز التحويل: <span class="text-primary"><?= $transfer['transfer_code'] ?></span>
            </h3>
            <h4 class="mb-3">
                <i class="bi bi-key me-2"></i>
                رمز الاستلام: <span class="text-success"><?= $transfer['pickup_code'] ?></span>
            </h4>
            <div class="status-badge status-<?= $transfer['status'] ?>">
                <?= getStatusLabel($transfer['status']) ?>
            </div>
        </div>
        
        <!-- Transfer Information -->
        <div class="receipt-info">
            <!-- Sender Information -->
            <div class="info-section">
                <div class="section-title">
                    <i class="bi bi-person-fill"></i>
                    معلومات المرسل
                </div>
                <div class="info-row">
                    <span class="info-label">الاسم:</span>
                    <span class="info-value"><?= htmlspecialchars($transfer['sender_name']) ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value"><?= htmlspecialchars($transfer['sender_phone']) ?></span>
                </div>
                <?php if (!empty($transfer['sender_email'])): ?>
                <div class="info-row">
                    <span class="info-label">البريد:</span>
                    <span class="info-value"><?= htmlspecialchars($transfer['sender_email']) ?></span>
                </div>
                <?php endif; ?>
                <div class="info-row">
                    <span class="info-label">البلد:</span>
                    <span class="info-value"><?= htmlspecialchars($transfer['sender_country_name']) ?></span>
                </div>
                <?php if (!empty($transfer['sender_address'])): ?>
                <div class="info-row">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value"><?= htmlspecialchars($transfer['sender_address']) ?></span>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Recipient Information -->
            <div class="info-section">
                <div class="section-title">
                    <i class="bi bi-person-check-fill"></i>
                    معلومات المستلم
                </div>
                <div class="info-row">
                    <span class="info-label">الاسم:</span>
                    <span class="info-value"><?= htmlspecialchars($transfer['recipient_name']) ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value"><?= htmlspecialchars($transfer['recipient_phone']) ?></span>
                </div>
                <?php if (!empty($transfer['recipient_email'])): ?>
                <div class="info-row">
                    <span class="info-label">البريد:</span>
                    <span class="info-value"><?= htmlspecialchars($transfer['recipient_email']) ?></span>
                </div>
                <?php endif; ?>
                <div class="info-row">
                    <span class="info-label">البلد:</span>
                    <span class="info-value"><?= htmlspecialchars($transfer['recipient_country_name']) ?></span>
                </div>
                <?php if (!empty($transfer['recipient_address'])): ?>
                <div class="info-row">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value"><?= htmlspecialchars($transfer['recipient_address']) ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Amount Section -->
        <div class="amount-section">
            <div class="amount-title">إجمالي المبلغ المحول</div>
            <div class="amount-value">
                $<?= number_format($transfer['total_amount'], 2) ?>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <small>المبلغ الأساسي</small><br>
                    <strong>$<?= number_format($transfer['amount'], 2) ?></strong>
                </div>
                <div class="col-md-4">
                    <small>الرسوم</small><br>
                    <strong>$<?= number_format($transfer['fee'], 2) ?></strong>
                </div>
                <div class="col-md-4">
                    <small>سعر الصرف</small><br>
                    <strong><?= $transfer['exchange_rate'] ?></strong>
                </div>
            </div>
        </div>
        
        <!-- Additional Information -->
        <div class="info-section">
            <div class="section-title">
                <i class="bi bi-info-circle-fill"></i>
                معلومات إضافية
            </div>
            <div class="info-row">
                <span class="info-label">الغرض من التحويل:</span>
                <span class="info-value"><?= htmlspecialchars($transfer['purpose'] ?? 'غير محدد') ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">طريقة الدفع:</span>
                <span class="info-value"><?= htmlspecialchars($transfer['payment_method'] ?? 'غير محدد') ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">تاريخ الإنشاء:</span>
                <span class="info-value"><?= formatDate($transfer['created_at']) ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">آخر تحديث:</span>
                <span class="info-value"><?= formatDate($transfer['updated_at'] ?? $transfer['created_at']) ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">المستخدم المسؤول:</span>
                <span class="info-value"><?= htmlspecialchars($transfer['user_name']) ?></span>
            </div>
            <?php if (!empty($transfer['notes'])): ?>
            <div class="info-row">
                <span class="info-label">ملاحظات:</span>
                <span class="info-value"><?= htmlspecialchars($transfer['notes']) ?></span>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- QR Code Section -->
        <div class="qr-section">
            <h5 class="mb-3">
                <i class="bi bi-qr-code me-2"></i>
                رمز الاستجابة السريعة
            </h5>
            <div id="qrcode" class="mb-3"></div>
            <small class="text-muted">امسح الرمز لتتبع التحويل</small>
        </div>
        
        <!-- Footer -->
        <div class="footer-section">
            <p class="mb-2">
                <strong><?= SYSTEM_NAME ?></strong> - نظام التحويلات المالية
            </p>
            <p class="mb-2">
                <i class="bi bi-telephone me-2"></i>
                للاستفسارات: ******-TRANSFER
            </p>
            <p class="mb-0">
                <i class="bi bi-envelope me-2"></i>
                البريد الإلكتروني: <EMAIL>
            </p>
            <hr class="my-3">
            <small>
                هذا الإيصال صالح لمدة 30 يوماً من تاريخ الإصدار. 
                يرجى الاحتفاظ بهذا الإيصال حتى اكتمال التحويل.
            </small>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    
    <script>
        // Generate QR Code
        document.addEventListener('DOMContentLoaded', function() {
            const qrData = {
                transfer_code: '<?= $transfer['transfer_code'] ?>',
                pickup_code: '<?= $transfer['pickup_code'] ?>',
                amount: '<?= $transfer['total_amount'] ?>',
                status: '<?= $transfer['status'] ?>',
                url: window.location.origin + '/WST_Transfir/public/track_transfer_fixed.php?code=<?= $transfer['transfer_code'] ?>'
            };
            
            QRCode.toCanvas(document.getElementById('qrcode'), JSON.stringify(qrData), {
                width: 200,
                height: 200,
                colorDark: '#007bff',
                colorLight: '#ffffff'
            }, function (error) {
                if (error) console.error(error);
            });
        });
        
        // Auto print on load (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
