<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Country extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_ar',
        'code',
        'iso_code',
        'phone_code',
        'currency_id',
        'flag_url',
        'is_active',
        'is_sender_country',
        'is_receiver_country',
        'timezone',
        'exchange_rate_margin',
        'min_transfer_amount',
        'max_transfer_amount',
        'daily_transfer_limit',
        'monthly_transfer_limit',
        'compliance_requirements',
        'required_documents',
        'processing_time_hours',
        'supported_languages',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_sender_country' => 'boolean',
        'is_receiver_country' => 'boolean',
        'exchange_rate_margin' => 'decimal:4',
        'min_transfer_amount' => 'decimal:2',
        'max_transfer_amount' => 'decimal:2',
        'daily_transfer_limit' => 'decimal:2',
        'monthly_transfer_limit' => 'decimal:2',
        'compliance_requirements' => 'array',
        'required_documents' => 'array',
        'processing_time_hours' => 'integer',
        'supported_languages' => 'array',
    ];

    /**
     * Get the primary currency for this country
     */
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * Get all branches in this country
     */
    public function branches(): HasMany
    {
        return $this->hasMany(Branch::class);
    }

    /**
     * Get all users from this country
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get transfers sent from this country
     */
    public function transfersSent(): HasMany
    {
        return $this->hasMany(Transfer::class, 'sender_country_id');
    }

    /**
     * Get transfers received in this country
     */
    public function transfersReceived(): HasMany
    {
        return $this->hasMany(Transfer::class, 'receiver_country_id');
    }

    /**
     * Get transfer fees for this country
     */
    public function transferFees(): HasMany
    {
        return $this->hasMany(TransferFee::class);
    }

    /**
     * Check if country supports sending transfers
     */
    public function canSendTransfers(): bool
    {
        return $this->is_active && $this->is_sender_country;
    }

    /**
     * Check if country supports receiving transfers
     */
    public function canReceiveTransfers(): bool
    {
        return $this->is_active && $this->is_receiver_country;
    }

    /**
     * Get country statistics
     */
    public function getStatistics(): array
    {
        $today = now()->startOfDay();
        $thisMonth = now()->startOfMonth();

        return [
            'total_branches' => $this->branches()->count(),
            'active_branches' => $this->branches()->where('is_active', true)->count(),
            'total_users' => $this->users()->count(),
            'today_transfers_sent' => $this->transfersSent()->whereDate('created_at', $today)->count(),
            'today_transfers_received' => $this->transfersReceived()->whereDate('created_at', $today)->count(),
            'today_amount_sent' => $this->transfersSent()->whereDate('created_at', $today)->sum('amount'),
            'today_amount_received' => $this->transfersReceived()->whereDate('created_at', $today)->sum('converted_amount'),
            'month_transfers_sent' => $this->transfersSent()->whereDate('created_at', '>=', $thisMonth)->count(),
            'month_transfers_received' => $this->transfersReceived()->whereDate('created_at', '>=', $thisMonth)->count(),
        ];
    }

    /**
     * Get estimated processing time
     */
    public function getEstimatedProcessingTime(): string
    {
        $hours = $this->processing_time_hours;
        
        if ($hours < 24) {
            return $hours . ' hours';
        }
        
        $days = ceil($hours / 24);
        return $days . ' days';
    }

    /**
     * Check if document is required
     */
    public function requiresDocument(string $documentType): bool
    {
        return in_array($documentType, $this->required_documents ?? []);
    }

    /**
     * Get compliance requirements for transfer amount
     */
    public function getComplianceRequirements(float $amount): array
    {
        $requirements = $this->compliance_requirements ?? [];
        $applicable = [];

        foreach ($requirements as $requirement) {
            if ($amount >= ($requirement['min_amount'] ?? 0) && 
                $amount <= ($requirement['max_amount'] ?? PHP_FLOAT_MAX)) {
                $applicable[] = $requirement;
            }
        }

        return $applicable;
    }
}
