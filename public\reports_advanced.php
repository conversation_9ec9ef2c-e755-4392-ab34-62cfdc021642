<?php

/**
 * Advanced Reports System
 * Elite Transfer System - Comprehensive Reporting & Analytics
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Check authentication
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = DatabaseManager::getInstance();

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'generate_financial_report':
                $startDate = $_POST['start_date'] ?? date('Y-m-01');
                $endDate = $_POST['end_date'] ?? date('Y-m-d');
                
                // Financial Summary
                $financialData = $db->selectOne("
                    SELECT 
                        COUNT(*) as total_transfers,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transfers,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transfers,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transfers,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount END), 0) as total_amount,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN fee END), 0) as total_fees,
                        COALESCE(AVG(CASE WHEN status = 'completed' THEN total_amount END), 0) as avg_amount,
                        COALESCE(MAX(CASE WHEN status = 'completed' THEN total_amount END), 0) as max_amount,
                        COALESCE(MIN(CASE WHEN status = 'completed' THEN total_amount END), 0) as min_amount
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Daily breakdown
                $dailyData = $db->select("
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as transfers,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount END), 0) as amount,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN fee END), 0) as fees
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY DATE(created_at)
                    ORDER BY DATE(created_at)
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Top countries
                $topCountries = $db->select("
                    SELECT 
                        c.name as country_name,
                        COUNT(t.id) as transfer_count,
                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_amount
                    FROM transfers t
                    LEFT JOIN countries c ON t.sender_country_id = c.id
                    WHERE DATE(t.created_at) BETWEEN :start_date AND :end_date
                    AND (t.deleted_at IS NULL OR t.deleted_at = '')
                    GROUP BY c.id, c.name
                    ORDER BY transfer_count DESC
                    LIMIT 10
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                echo json_encode([
                    'success' => true,
                    'financial_data' => $financialData,
                    'daily_data' => $dailyData,
                    'top_countries' => $topCountries,
                    'period' => ['start' => $startDate, 'end' => $endDate]
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'generate_user_report':
                $startDate = $_POST['start_date'] ?? date('Y-m-01');
                $endDate = $_POST['end_date'] ?? date('Y-m-d');
                
                // User statistics
                $userStats = $db->selectOne("
                    SELECT 
                        COUNT(*) as total_users,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
                        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_users,
                        COUNT(CASE WHEN DATE(created_at) BETWEEN :start_date AND :end_date THEN 1 END) as new_users
                    FROM users 
                    WHERE (deleted_at IS NULL OR deleted_at = '')
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // User roles breakdown
                $roleStats = $db->select("
                    SELECT 
                        role,
                        COUNT(*) as count,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count
                    FROM users 
                    WHERE (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY role
                    ORDER BY count DESC
                ");
                
                // User registration trend
                $registrationTrend = $db->select("
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as new_users
                    FROM users 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY DATE(created_at)
                    ORDER BY DATE(created_at)
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Most active users
                $activeUsers = $db->select("
                    SELECT 
                        u.name,
                        u.email,
                        u.role,
                        COUNT(t.id) as transfer_count,
                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_amount
                    FROM users u
                    LEFT JOIN transfers t ON u.id = t.user_id 
                        AND DATE(t.created_at) BETWEEN :start_date AND :end_date
                        AND (t.deleted_at IS NULL OR t.deleted_at = '')
                    WHERE (u.deleted_at IS NULL OR u.deleted_at = '')
                    GROUP BY u.id, u.name, u.email, u.role
                    HAVING transfer_count > 0
                    ORDER BY transfer_count DESC
                    LIMIT 10
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                echo json_encode([
                    'success' => true,
                    'user_stats' => $userStats,
                    'role_stats' => $roleStats,
                    'registration_trend' => $registrationTrend,
                    'active_users' => $activeUsers,
                    'period' => ['start' => $startDate, 'end' => $endDate]
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'generate_performance_report':
                $startDate = $_POST['start_date'] ?? date('Y-m-01');
                $endDate = $_POST['end_date'] ?? date('Y-m-d');
                
                // Performance metrics
                $performanceData = $db->selectOne("
                    SELECT 
                        COUNT(*) as total_transfers,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transfers,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transfers,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transfers,
                        ROUND(COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate,
                        ROUND(COUNT(CASE WHEN status = 'failed' THEN 1 END) * 100.0 / COUNT(*), 2) as failure_rate,
                        ROUND(AVG(CASE WHEN status = 'completed' AND updated_at IS NOT NULL 
                            THEN TIMESTAMPDIFF(HOUR, created_at, updated_at) END), 2) as avg_processing_time
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Hourly distribution
                $hourlyData = $db->select("
                    SELECT 
                        HOUR(created_at) as hour,
                        COUNT(*) as transfer_count
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY HOUR(created_at)
                    ORDER BY hour
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Weekly distribution
                $weeklyData = $db->select("
                    SELECT 
                        DAYNAME(created_at) as day_name,
                        DAYOFWEEK(created_at) as day_number,
                        COUNT(*) as transfer_count
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY DAYOFWEEK(created_at), DAYNAME(created_at)
                    ORDER BY day_number
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Status distribution over time
                $statusTrend = $db->select("
                    SELECT 
                        DATE(created_at) as date,
                        status,
                        COUNT(*) as count
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY DATE(created_at), status
                    ORDER BY DATE(created_at), status
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                echo json_encode([
                    'success' => true,
                    'performance_data' => $performanceData,
                    'hourly_data' => $hourlyData,
                    'weekly_data' => $weeklyData,
                    'status_trend' => $statusTrend,
                    'period' => ['start' => $startDate, 'end' => $endDate]
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'export_report':
                $reportType = $_POST['report_type'] ?? 'financial';
                $format = $_POST['format'] ?? 'csv';
                $startDate = $_POST['start_date'] ?? date('Y-m-01');
                $endDate = $_POST['end_date'] ?? date('Y-m-d');
                
                // Generate export data based on report type
                switch ($reportType) {
                    case 'financial':
                        $data = $db->select("
                            SELECT 
                                transfer_code,
                                sender_name,
                                recipient_name,
                                total_amount,
                                fee,
                                status,
                                created_at
                            FROM transfers 
                            WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                            AND (deleted_at IS NULL OR deleted_at = '')
                            ORDER BY created_at DESC
                        ", ['start_date' => $startDate, 'end_date' => $endDate]);
                        break;
                        
                    case 'users':
                        $data = $db->select("
                            SELECT 
                                name,
                                email,
                                role,
                                status,
                                created_at
                            FROM users 
                            WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                            AND (deleted_at IS NULL OR deleted_at = '')
                            ORDER BY created_at DESC
                        ", ['start_date' => $startDate, 'end_date' => $endDate]);
                        break;
                        
                    default:
                        throw new Exception('نوع التقرير غير صحيح');
                }
                
                // Generate filename
                $filename = $reportType . '_report_' . $startDate . '_to_' . $endDate . '.' . $format;
                
                if ($format === 'csv') {
                    // Generate CSV content
                    $csvContent = '';
                    if (!empty($data)) {
                        // Headers
                        $csvContent .= implode(',', array_keys($data[0])) . "\n";
                        
                        // Data rows
                        foreach ($data as $row) {
                            $csvContent .= implode(',', array_map(function($value) {
                                return '"' . str_replace('"', '""', $value) . '"';
                            }, $row)) . "\n";
                        }
                    }
                    
                    echo json_encode([
                        'success' => true,
                        'filename' => $filename,
                        'content' => base64_encode($csvContent),
                        'mime_type' => 'text/csv'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception('تنسيق التصدير غير مدعوم');
                }
                break;
                
            case 'get_report_templates':
                $templates = [
                    [
                        'id' => 'financial_summary',
                        'name' => 'ملخص مالي',
                        'description' => 'تقرير شامل عن الإيرادات والتحويلات',
                        'icon' => 'bi-currency-dollar',
                        'color' => 'success'
                    ],
                    [
                        'id' => 'user_activity',
                        'name' => 'نشاط المستخدمين',
                        'description' => 'تقرير عن نشاط وإحصائيات المستخدمين',
                        'icon' => 'bi-people',
                        'color' => 'info'
                    ],
                    [
                        'id' => 'performance_metrics',
                        'name' => 'مقاييس الأداء',
                        'description' => 'تحليل أداء النظام ومعدلات النجاح',
                        'icon' => 'bi-graph-up',
                        'color' => 'warning'
                    ],
                    [
                        'id' => 'country_analysis',
                        'name' => 'تحليل البلدان',
                        'description' => 'إحصائيات التحويلات حسب البلدان',
                        'icon' => 'bi-globe',
                        'color' => 'primary'
                    ],
                    [
                        'id' => 'trend_analysis',
                        'name' => 'تحليل الاتجاهات',
                        'description' => 'تحليل اتجاهات النمو والتطور',
                        'icon' => 'bi-trending-up',
                        'color' => 'danger'
                    ],
                    [
                        'id' => 'compliance_report',
                        'name' => 'تقرير الامتثال',
                        'description' => 'تقرير الامتثال واللوائح',
                        'icon' => 'bi-shield-check',
                        'color' => 'dark'
                    ]
                ];
                
                echo json_encode([
                    'success' => true,
                    'templates' => $templates
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التقارير المتقدم - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
</head>
