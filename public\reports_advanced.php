<?php

/**
 * Advanced Reports System
 * Elite Transfer System - Comprehensive Reporting & Analytics
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Check authentication
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = DatabaseManager::getInstance();

// Ensure countries table exists
try {
    $db->query("
        CREATE TABLE IF NOT EXISTS countries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            currency VARCHAR(3) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            status ENUM('active', 'inactive') DEFAULT 'active',
            flag_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP NULL,
            INDEX idx_code (code),
            INDEX idx_status (status),
            INDEX idx_deleted_at (deleted_at)
        )
    ");
} catch (Exception $e) {
    // Table might already exist
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'generate_financial_report':
                $startDate = $_POST['start_date'] ?? date('Y-m-01');
                $endDate = $_POST['end_date'] ?? date('Y-m-d');
                
                // Financial Summary
                $financialData = $db->selectOne("
                    SELECT 
                        COUNT(*) as total_transfers,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transfers,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transfers,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transfers,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount END), 0) as total_amount,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN fee END), 0) as total_fees,
                        COALESCE(AVG(CASE WHEN status = 'completed' THEN total_amount END), 0) as avg_amount,
                        COALESCE(MAX(CASE WHEN status = 'completed' THEN total_amount END), 0) as max_amount,
                        COALESCE(MIN(CASE WHEN status = 'completed' THEN total_amount END), 0) as min_amount
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Daily breakdown
                $dailyData = $db->select("
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as transfers,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount END), 0) as amount,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN fee END), 0) as fees
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY DATE(created_at)
                    ORDER BY DATE(created_at)
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Top countries
                $topCountries = [];
                try {
                    $topCountries = $db->select("
                        SELECT
                            COALESCE(c.name, 'غير محدد') as country_name,
                            COUNT(t.id) as transfer_count,
                            COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_amount
                        FROM transfers t
                        LEFT JOIN countries c ON t.sender_country_id = c.id
                        WHERE DATE(t.created_at) BETWEEN :start_date AND :end_date
                        AND (t.deleted_at IS NULL OR t.deleted_at = '')
                        GROUP BY c.id, c.name
                        ORDER BY transfer_count DESC
                        LIMIT 10
                    ", ['start_date' => $startDate, 'end_date' => $endDate]);
                } catch (Exception $e) {
                    // If countries table doesn't exist, return empty array
                    $topCountries = [];
                }
                
                echo json_encode([
                    'success' => true,
                    'financial_data' => $financialData,
                    'daily_data' => $dailyData,
                    'top_countries' => $topCountries,
                    'period' => ['start' => $startDate, 'end' => $endDate]
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'generate_user_report':
                $startDate = $_POST['start_date'] ?? date('Y-m-01');
                $endDate = $_POST['end_date'] ?? date('Y-m-d');
                
                // User statistics
                $userStats = $db->selectOne("
                    SELECT 
                        COUNT(*) as total_users,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
                        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_users,
                        COUNT(CASE WHEN DATE(created_at) BETWEEN :start_date AND :end_date THEN 1 END) as new_users
                    FROM users 
                    WHERE (deleted_at IS NULL OR deleted_at = '')
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // User roles breakdown
                $roleStats = $db->select("
                    SELECT 
                        role,
                        COUNT(*) as count,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count
                    FROM users 
                    WHERE (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY role
                    ORDER BY count DESC
                ");
                
                // User registration trend
                $registrationTrend = $db->select("
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as new_users
                    FROM users 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY DATE(created_at)
                    ORDER BY DATE(created_at)
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Most active users
                $activeUsers = $db->select("
                    SELECT 
                        u.name,
                        u.email,
                        u.role,
                        COUNT(t.id) as transfer_count,
                        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_amount END), 0) as total_amount
                    FROM users u
                    LEFT JOIN transfers t ON u.id = t.user_id 
                        AND DATE(t.created_at) BETWEEN :start_date AND :end_date
                        AND (t.deleted_at IS NULL OR t.deleted_at = '')
                    WHERE (u.deleted_at IS NULL OR u.deleted_at = '')
                    GROUP BY u.id, u.name, u.email, u.role
                    HAVING transfer_count > 0
                    ORDER BY transfer_count DESC
                    LIMIT 10
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                echo json_encode([
                    'success' => true,
                    'user_stats' => $userStats,
                    'role_stats' => $roleStats,
                    'registration_trend' => $registrationTrend,
                    'active_users' => $activeUsers,
                    'period' => ['start' => $startDate, 'end' => $endDate]
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'generate_performance_report':
                $startDate = $_POST['start_date'] ?? date('Y-m-01');
                $endDate = $_POST['end_date'] ?? date('Y-m-d');
                
                // Performance metrics
                $performanceData = $db->selectOne("
                    SELECT 
                        COUNT(*) as total_transfers,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transfers,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transfers,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transfers,
                        ROUND(COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate,
                        ROUND(COUNT(CASE WHEN status = 'failed' THEN 1 END) * 100.0 / COUNT(*), 2) as failure_rate,
                        ROUND(AVG(CASE WHEN status = 'completed' AND updated_at IS NOT NULL 
                            THEN TIMESTAMPDIFF(HOUR, created_at, updated_at) END), 2) as avg_processing_time
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Hourly distribution
                $hourlyData = $db->select("
                    SELECT 
                        HOUR(created_at) as hour,
                        COUNT(*) as transfer_count
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY HOUR(created_at)
                    ORDER BY hour
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Weekly distribution
                $weeklyData = $db->select("
                    SELECT 
                        DAYNAME(created_at) as day_name,
                        DAYOFWEEK(created_at) as day_number,
                        COUNT(*) as transfer_count
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY DAYOFWEEK(created_at), DAYNAME(created_at)
                    ORDER BY day_number
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                // Status distribution over time
                $statusTrend = $db->select("
                    SELECT 
                        DATE(created_at) as date,
                        status,
                        COUNT(*) as count
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY DATE(created_at), status
                    ORDER BY DATE(created_at), status
                ", ['start_date' => $startDate, 'end_date' => $endDate]);
                
                echo json_encode([
                    'success' => true,
                    'performance_data' => $performanceData,
                    'hourly_data' => $hourlyData,
                    'weekly_data' => $weeklyData,
                    'status_trend' => $statusTrend,
                    'period' => ['start' => $startDate, 'end' => $endDate]
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'export_report':
                $reportType = $_POST['report_type'] ?? 'financial';
                $format = $_POST['format'] ?? 'csv';
                $startDate = $_POST['start_date'] ?? date('Y-m-01');
                $endDate = $_POST['end_date'] ?? date('Y-m-d');
                
                // Generate export data based on report type
                switch ($reportType) {
                    case 'financial':
                        $data = $db->select("
                            SELECT 
                                transfer_code,
                                sender_name,
                                recipient_name,
                                total_amount,
                                fee,
                                status,
                                created_at
                            FROM transfers 
                            WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                            AND (deleted_at IS NULL OR deleted_at = '')
                            ORDER BY created_at DESC
                        ", ['start_date' => $startDate, 'end_date' => $endDate]);
                        break;
                        
                    case 'users':
                        $data = $db->select("
                            SELECT 
                                name,
                                email,
                                role,
                                status,
                                created_at
                            FROM users 
                            WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                            AND (deleted_at IS NULL OR deleted_at = '')
                            ORDER BY created_at DESC
                        ", ['start_date' => $startDate, 'end_date' => $endDate]);
                        break;
                        
                    default:
                        throw new Exception('نوع التقرير غير صحيح');
                }
                
                // Generate filename
                $filename = $reportType . '_report_' . $startDate . '_to_' . $endDate . '.' . $format;
                
                if ($format === 'csv') {
                    // Generate CSV content
                    $csvContent = '';
                    if (!empty($data)) {
                        // Headers
                        $csvContent .= implode(',', array_keys($data[0])) . "\n";
                        
                        // Data rows
                        foreach ($data as $row) {
                            $csvContent .= implode(',', array_map(function($value) {
                                return '"' . str_replace('"', '""', $value) . '"';
                            }, $row)) . "\n";
                        }
                    }
                    
                    echo json_encode([
                        'success' => true,
                        'filename' => $filename,
                        'content' => base64_encode($csvContent),
                        'mime_type' => 'text/csv'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception('تنسيق التصدير غير مدعوم');
                }
                break;
                
            case 'get_report_templates':
                $templates = [
                    [
                        'id' => 'financial_summary',
                        'name' => 'ملخص مالي',
                        'description' => 'تقرير شامل عن الإيرادات والتحويلات',
                        'icon' => 'bi-currency-dollar',
                        'color' => 'success'
                    ],
                    [
                        'id' => 'user_activity',
                        'name' => 'نشاط المستخدمين',
                        'description' => 'تقرير عن نشاط وإحصائيات المستخدمين',
                        'icon' => 'bi-people',
                        'color' => 'info'
                    ],
                    [
                        'id' => 'performance_metrics',
                        'name' => 'مقاييس الأداء',
                        'description' => 'تحليل أداء النظام ومعدلات النجاح',
                        'icon' => 'bi-graph-up',
                        'color' => 'warning'
                    ],
                    [
                        'id' => 'country_analysis',
                        'name' => 'تحليل البلدان',
                        'description' => 'إحصائيات التحويلات حسب البلدان',
                        'icon' => 'bi-globe',
                        'color' => 'primary'
                    ],
                    [
                        'id' => 'trend_analysis',
                        'name' => 'تحليل الاتجاهات',
                        'description' => 'تحليل اتجاهات النمو والتطور',
                        'icon' => 'bi-trending-up',
                        'color' => 'danger'
                    ],
                    [
                        'id' => 'compliance_report',
                        'name' => 'تقرير الامتثال',
                        'description' => 'تقرير الامتثال واللوائح',
                        'icon' => 'bi-shield-check',
                        'color' => 'dark'
                    ]
                ];
                
                echo json_encode([
                    'success' => true,
                    'templates' => $templates
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التقارير المتقدم - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <style>
        :root {
            /* Elite Color Palette - Unified Design */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

            /* Glass Morphism - Premium */
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-bg-strong: rgba(255, 255, 255, 0.12);
            --glass-border: rgba(255, 255, 255, 0.15);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

            /* Spacing & Borders */
            --border-radius: 24px;
            --border-radius-small: 16px;
            --border-radius-large: 32px;

            /* Typography */
            --font-primary: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .reports-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .reports-header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            color: white;
            text-align: center;
        }

        .reports-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .template-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .template-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin: 0 auto 15px;
        }

        .template-card.success .template-icon { background: var(--success-gradient); }
        .template-card.info .template-icon { background: var(--info-gradient); }
        .template-card.warning .template-icon { background: var(--warning-gradient); }
        .template-card.primary .template-icon { background: var(--primary-gradient); }
        .template-card.danger .template-icon { background: var(--danger-gradient); }
        .template-card.dark .template-icon { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); }

        .template-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .template-description {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-btn:hover {
            background: var(--info-gradient);
            color: white;
            transform: translateY(-2px);
        }

        .report-modal .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            color: white;
        }

        .report-modal .modal-header {
            border-bottom: 1px solid var(--glass-border);
        }

        .report-modal .modal-footer {
            border-top: 1px solid var(--glass-border);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            color: white;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--info-gradient);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-select option {
            background: #2c3e50;
            color: white;
        }

        @media (max-width: 768px) {
            .template-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Back Button -->
    <a href="dashboard_advanced.php" class="back-btn">
        <i class="bi bi-arrow-right me-2"></i>
        العودة للوحة التحكم
    </a>

    <div class="reports-container">
        <!-- Header -->
        <div class="reports-header">
            <h1><i class="bi bi-file-earmark-bar-graph me-3"></i>نظام التقارير المتقدم</h1>
            <p>إنشاء وتصدير التقارير الشاملة والتحليلات المفصلة</p>
        </div>

        <!-- Report Templates -->
        <div class="template-grid" id="templateGrid">
            <!-- Templates will be loaded here -->
        </div>
    </div>

    <!-- Report Generation Modal -->
    <div class="modal fade report-modal" id="reportModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reportModalTitle">إنشاء تقرير</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="reportForm">
                        <input type="hidden" id="reportType" name="report_type">

                        <div class="row">
                            <div class="col-md-6">
                                <label for="startDate" class="form-label">تاريخ البداية</label>
                                <input type="date" class="form-control" id="startDate" name="start_date" required>
                            </div>
                            <div class="col-md-6">
                                <label for="endDate" class="form-label">تاريخ النهاية</label>
                                <input type="date" class="form-control" id="endDate" name="end_date" required>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label for="reportFormat" class="form-label">تنسيق التقرير</label>
                                <select class="form-select" id="reportFormat" name="format">
                                    <option value="csv">CSV</option>
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="reportLanguage" class="form-label">لغة التقرير</label>
                                <select class="form-select" id="reportLanguage" name="language">
                                    <option value="ar">العربية</option>
                                    <option value="en">English</option>
                                </select>
                            </div>
                        </div>

                        <div class="mt-3">
                            <label for="reportDescription" class="form-label">وصف التقرير (اختياري)</label>
                            <textarea class="form-control" id="reportDescription" name="description" rows="3" placeholder="أدخل وصف مختصر للتقرير..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="generateReport()">
                        <i class="bi bi-download me-1"></i>
                        إنشاء وتحميل التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let reportTemplates = [];

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadReportTemplates();
            setDefaultDates();
        });

        function setDefaultDates() {
            const today = new Date();
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

            document.getElementById('startDate').value = lastMonth.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        }

        function loadReportTemplates() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_report_templates' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        reportTemplates = response.templates;
                        renderTemplates();
                    } else {
                        showError('فشل في تحميل قوالب التقارير');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                }
            });
        }

        function renderTemplates() {
            const grid = document.getElementById('templateGrid');

            grid.innerHTML = reportTemplates.map(template => `
                <div class="template-card ${template.color}" onclick="openReportModal('${template.id}', '${template.name}')">
                    <div class="template-icon">
                        <i class="${template.icon}"></i>
                    </div>
                    <div class="template-title">${template.name}</div>
                    <div class="template-description">${template.description}</div>
                </div>
            `).join('');
        }

        function openReportModal(reportType, reportName) {
            document.getElementById('reportType').value = reportType;
            document.getElementById('reportModalTitle').textContent = `إنشاء ${reportName}`;

            const modal = new bootstrap.Modal(document.getElementById('reportModal'));
            modal.show();
        }

        function generateReport() {
            const form = document.getElementById('reportForm');
            const formData = new FormData(form);
            formData.append('action', 'export_report');

            // Show loading
            Swal.fire({
                title: 'جاري إنشاء التقرير...',
                text: 'يرجى الانتظار حتى يتم إنشاء التقرير',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        // Create download link
                        const blob = new Blob([atob(response.content)], { type: response.mime_type });
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = response.filename;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        window.URL.revokeObjectURL(url);

                        // Close modal
                        bootstrap.Modal.getInstance(document.getElementById('reportModal')).hide();

                        Swal.fire({
                            icon: 'success',
                            title: 'تم إنشاء التقرير بنجاح',
                            text: 'تم تحميل التقرير على جهازك',
                            confirmButtonText: 'ممتاز'
                        });
                    } else {
                        showError(response.message || 'فشل في إنشاء التقرير');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                }
            });
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: message,
                confirmButtonText: 'حسناً'
            });
        }
    </script>
</body>
</html>
