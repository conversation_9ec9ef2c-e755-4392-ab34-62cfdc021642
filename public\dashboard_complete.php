<?php

/**
 * Complete Elite Dashboard - All-in-One Interface
 * Elite Transfer System - Comprehensive Dashboard with All Features
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Check authentication
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = DatabaseManager::getInstance();
$userData = getUserData();

// Ensure tables exist
try {
    // Countries table
    $db->query("
        CREATE TABLE IF NOT EXISTS countries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            currency VARCHAR(3) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            status ENUM('active', 'inactive') DEFAULT 'active',
            flag_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP NULL,
            INDEX idx_code (code),
            INDEX idx_status (status)
        )
    ");
    
    // Transfers table
    $db->query("
        CREATE TABLE IF NOT EXISTS transfers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            transfer_code VARCHAR(20) NOT NULL UNIQUE,
            user_id INT,
            sender_name VARCHAR(255) NOT NULL,
            sender_phone VARCHAR(20),
            sender_country_id INT,
            recipient_name VARCHAR(255) NOT NULL,
            recipient_phone VARCHAR(20),
            recipient_country_id INT,
            amount DECIMAL(15,2) NOT NULL,
            fees DECIMAL(15,2) DEFAULT 0.00,
            total_amount DECIMAL(15,2) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
            payment_method VARCHAR(50) DEFAULT 'cash',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP NULL,
            INDEX idx_transfer_code (transfer_code),
            INDEX idx_user_id (user_id),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at),
            INDEX idx_deleted_at (deleted_at)
        )
    ");

    // Add sample countries if table is empty
    $countryCount = $db->selectOne("SELECT COUNT(*) as count FROM countries")['count'];
    if ($countryCount == 0) {
        $sampleCountries = [
            ['name' => 'المملكة العربية السعودية', 'code' => 'SAR', 'currency' => 'SAR', 'exchange_rate' => 1.0000],
            ['name' => 'الإمارات العربية المتحدة', 'code' => 'AED', 'currency' => 'AED', 'exchange_rate' => 1.0200],
            ['name' => 'الكويت', 'code' => 'KWD', 'currency' => 'KWD', 'exchange_rate' => 0.3000],
            ['name' => 'قطر', 'code' => 'QAR', 'currency' => 'QAR', 'exchange_rate' => 3.6400],
            ['name' => 'البحرين', 'code' => 'BHD', 'currency' => 'BHD', 'exchange_rate' => 0.3770],
            ['name' => 'عمان', 'code' => 'OMR', 'currency' => 'OMR', 'exchange_rate' => 0.3850],
            ['name' => 'الأردن', 'code' => 'JOD', 'currency' => 'JOD', 'exchange_rate' => 0.7090],
            ['name' => 'لبنان', 'code' => 'LBP', 'currency' => 'LBP', 'exchange_rate' => 15000.0000],
            ['name' => 'مصر', 'code' => 'EGP', 'currency' => 'EGP', 'exchange_rate' => 30.9000],
            ['name' => 'المغرب', 'code' => 'MAD', 'currency' => 'MAD', 'exchange_rate' => 10.1000],
            ['name' => 'تونس', 'code' => 'TND', 'currency' => 'TND', 'exchange_rate' => 3.1000],
            ['name' => 'الجزائر', 'code' => 'DZD', 'currency' => 'DZD', 'exchange_rate' => 134.0000],
            ['name' => 'العراق', 'code' => 'IQD', 'currency' => 'IQD', 'exchange_rate' => 1310.0000],
            ['name' => 'سوريا', 'code' => 'SYP', 'currency' => 'SYP', 'exchange_rate' => 2512.0000],
            ['name' => 'اليمن', 'code' => 'YER', 'currency' => 'YER', 'exchange_rate' => 250.0000],
            ['name' => 'السودان', 'code' => 'SDG', 'currency' => 'SDG', 'exchange_rate' => 601.0000],
            ['name' => 'ليبيا', 'code' => 'LYD', 'currency' => 'LYD', 'exchange_rate' => 4.8000],
            ['name' => 'فلسطين', 'code' => 'ILS', 'currency' => 'ILS', 'exchange_rate' => 3.7000],
            ['name' => 'تركيا', 'code' => 'TRY', 'currency' => 'TRY', 'exchange_rate' => 29.4000],
            ['name' => 'إيران', 'code' => 'IRR', 'currency' => 'IRR', 'exchange_rate' => 42000.0000],
            ['name' => 'باكستان', 'code' => 'PKR', 'currency' => 'PKR', 'exchange_rate' => 278.0000],
            ['name' => 'الهند', 'code' => 'INR', 'currency' => 'INR', 'exchange_rate' => 83.2000],
            ['name' => 'بنغلاديش', 'code' => 'BDT', 'currency' => 'BDT', 'exchange_rate' => 110.0000],
            ['name' => 'الفلبين', 'code' => 'PHP', 'currency' => 'PHP', 'exchange_rate' => 56.0000],
            ['name' => 'إندونيسيا', 'code' => 'IDR', 'currency' => 'IDR', 'exchange_rate' => 15700.0000],
            ['name' => 'ماليزيا', 'code' => 'MYR', 'currency' => 'MYR', 'exchange_rate' => 4.7000],
            ['name' => 'سنغافورة', 'code' => 'SGD', 'currency' => 'SGD', 'exchange_rate' => 1.3400],
            ['name' => 'الولايات المتحدة', 'code' => 'USD', 'currency' => 'USD', 'exchange_rate' => 3.7500],
            ['name' => 'المملكة المتحدة', 'code' => 'GBP', 'currency' => 'GBP', 'exchange_rate' => 4.7000],
            ['name' => 'ألمانيا', 'code' => 'EUR', 'currency' => 'EUR', 'exchange_rate' => 4.1000],
            ['name' => 'فرنسا', 'code' => 'EUR', 'currency' => 'EUR', 'exchange_rate' => 4.1000],
            ['name' => 'إيطاليا', 'code' => 'EUR', 'currency' => 'EUR', 'exchange_rate' => 4.1000],
            ['name' => 'إسبانيا', 'code' => 'EUR', 'currency' => 'EUR', 'exchange_rate' => 4.1000],
            ['name' => 'كندا', 'code' => 'CAD', 'currency' => 'CAD', 'exchange_rate' => 2.8000],
            ['name' => 'أستراليا', 'code' => 'AUD', 'currency' => 'AUD', 'exchange_rate' => 2.5000],
            ['name' => 'اليابان', 'code' => 'JPY', 'currency' => 'JPY', 'exchange_rate' => 550.0000],
            ['name' => 'الصين', 'code' => 'CNY', 'currency' => 'CNY', 'exchange_rate' => 27.0000],
            ['name' => 'كوريا الجنوبية', 'code' => 'KRW', 'currency' => 'KRW', 'exchange_rate' => 5000.0000],
            ['name' => 'روسيا', 'code' => 'RUB', 'currency' => 'RUB', 'exchange_rate' => 340.0000],
            ['name' => 'البرازيل', 'code' => 'BRL', 'currency' => 'BRL', 'exchange_rate' => 18.5000],
            ['name' => 'جنوب أفريقيا', 'code' => 'ZAR', 'currency' => 'ZAR', 'exchange_rate' => 69.0000],
            ['name' => 'نيجيريا', 'code' => 'NGN', 'currency' => 'NGN', 'exchange_rate' => 1500.0000],
            ['name' => 'كينيا', 'code' => 'KES', 'currency' => 'KES', 'exchange_rate' => 480.0000],
            ['name' => 'إثيوبيا', 'code' => 'ETB', 'currency' => 'ETB', 'exchange_rate' => 200.0000],
            ['name' => 'غانا', 'code' => 'GHS', 'currency' => 'GHS', 'exchange_rate' => 45.0000],
            ['name' => 'المكسيك', 'code' => 'MXN', 'currency' => 'MXN', 'exchange_rate' => 67.0000],
            ['name' => 'الأرجنتين', 'code' => 'ARS', 'currency' => 'ARS', 'exchange_rate' => 1200.0000],
            ['name' => 'تشيلي', 'code' => 'CLP', 'currency' => 'CLP', 'exchange_rate' => 3200.0000],
            ['name' => 'كولومبيا', 'code' => 'COP', 'currency' => 'COP', 'exchange_rate' => 15000.0000],
            ['name' => 'بيرو', 'code' => 'PEN', 'currency' => 'PEN', 'exchange_rate' => 14.0000],
            ['name' => 'فنزويلا', 'code' => 'VES', 'currency' => 'VES', 'exchange_rate' => 130000.0000]
        ];

        foreach ($sampleCountries as $country) {
            $db->insert('countries', $country);
        }
    }

    // Add sample transfers if table is empty
    $transferCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers")['count'];
    if ($transferCount == 0) {
        $sampleTransfers = [
            [
                'transfer_code' => 'TR' . date('Ymd') . '0001',
                'user_id' => 1,
                'sender_name' => 'أحمد محمد علي',
                'sender_phone' => '+966501234567',
                'sender_country_id' => 1,
                'recipient_name' => 'فاطمة أحمد',
                'recipient_phone' => '+971501234567',
                'recipient_country_id' => 2,
                'amount' => 1000.00,
                'fees' => 25.00,
                'total_amount' => 1025.00,
                'exchange_rate' => 1.0200,
                'status' => 'completed',
                'payment_method' => 'cash',
                'notes' => 'تحويل عائلي',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
            ],
            [
                'transfer_code' => 'TR' . date('Ymd') . '0002',
                'user_id' => 1,
                'sender_name' => 'محمد عبدالله',
                'sender_phone' => '+966502345678',
                'sender_country_id' => 1,
                'recipient_name' => 'عبدالرحمن محمد',
                'recipient_phone' => '+965501234567',
                'recipient_country_id' => 3,
                'amount' => 500.00,
                'fees' => 15.00,
                'total_amount' => 515.00,
                'exchange_rate' => 0.3000,
                'status' => 'pending',
                'payment_method' => 'card',
                'notes' => 'دفع فاتورة',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
            ],
            [
                'transfer_code' => 'TR' . date('Ymd') . '0003',
                'user_id' => 1,
                'sender_name' => 'سارة أحمد',
                'sender_phone' => '+************',
                'sender_country_id' => 1,
                'recipient_name' => 'نورا عبدالله',
                'recipient_phone' => '+************',
                'recipient_country_id' => 4,
                'amount' => 750.00,
                'fees' => 20.00,
                'total_amount' => 770.00,
                'exchange_rate' => 3.6400,
                'status' => 'processing',
                'payment_method' => 'bank',
                'notes' => 'مساعدة مالية',
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 hours'))
            ],
            [
                'transfer_code' => 'TR' . date('Ymd') . '0004',
                'user_id' => 1,
                'sender_name' => 'خالد سعد',
                'sender_phone' => '+************',
                'sender_country_id' => 1,
                'recipient_name' => 'أمل خالد',
                'recipient_phone' => '+************',
                'recipient_country_id' => 5,
                'amount' => 300.00,
                'fees' => 10.00,
                'total_amount' => 310.00,
                'exchange_rate' => 0.3770,
                'status' => 'completed',
                'payment_method' => 'cash',
                'notes' => 'مصروف شخصي',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
            ],
            [
                'transfer_code' => 'TR' . date('Ymd') . '0005',
                'user_id' => 1,
                'sender_name' => 'عبدالعزيز محمد',
                'sender_phone' => '+966505678901',
                'sender_country_id' => 1,
                'recipient_name' => 'حسام عبدالعزيز',
                'recipient_phone' => '+968501234567',
                'recipient_country_id' => 6,
                'amount' => 1200.00,
                'fees' => 30.00,
                'total_amount' => 1230.00,
                'exchange_rate' => 0.3850,
                'status' => 'completed',
                'payment_method' => 'card',
                'notes' => 'استثمار',
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($sampleTransfers as $transfer) {
            $db->insert('transfers', $transfer);
        }
    }
} catch (Exception $e) {
    // Tables might already exist
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'get_dashboard_data':
                // Get comprehensive statistics
                try {
                    $stats = $db->getStatistics();
                } catch (Exception $e) {
                    $stats = [
                        'total_transfers' => 0,
                        'completed_transfers' => 0,
                        'pending_transfers' => 0,
                        'total_amount' => 0,
                        'today_transfers' => 0,
                        'today_amount' => 0
                    ];
                }
                
                try {
                    $recentTransfers = $db->getTransfers([], 5, 0);
                } catch (Exception $e) {
                    $recentTransfers = [];
                }
                
                try {
                    $recentUsers = $db->getUsers([], 3, 0);
                } catch (Exception $e) {
                    $recentUsers = [];
                }
                
                // Get detailed counts
                try {
                    $pendingCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'pending' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                    $processingCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'processing' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                    $completedToday = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'completed' AND DATE(created_at) = CURDATE() AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                    $failedCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'failed' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                } catch (Exception $e) {
                    $pendingCount = 0;
                    $processingCount = 0;
                    $completedToday = 0;
                    $failedCount = 0;
                }
                
                // Get revenue statistics
                try {
                    $todayRevenue = $db->selectOne("SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
                    $monthRevenue = $db->selectOne("SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE()) AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
                    $yearRevenue = $db->selectOne("SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE YEAR(created_at) = YEAR(CURDATE()) AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
                } catch (Exception $e) {
                    $todayRevenue = 0;
                    $monthRevenue = 0;
                    $yearRevenue = 0;
                }
                
                // Get user statistics
                try {
                    $totalUsers = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE (deleted_at IS NULL OR deleted_at = '')")['count'];
                    $activeUsers = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1 AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                    $newUsersToday = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE() AND (deleted_at IS NULL OR deleted_at = '')")['count'];
                } catch (Exception $e) {
                    $totalUsers = 1;
                    $activeUsers = 1;
                    $newUsersToday = 0;
                }
                
                // Get chart data for last 7 days
                $chartData = [];
                for ($i = 6; $i >= 0; $i--) {
                    $date = date('Y-m-d', strtotime("-$i days"));
                    try {
                        $dayData = $db->selectOne("
                            SELECT 
                                COUNT(*) as total,
                                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
                                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
                            FROM transfers 
                            WHERE DATE(created_at) = :date
                            AND (deleted_at IS NULL OR deleted_at = '')
                        ", ['date' => $date]);
                    } catch (Exception $e) {
                        $dayData = ['total' => 0, 'completed' => 0, 'pending' => 0, 'failed' => 0];
                    }
                    
                    $chartData[] = [
                        'date' => $date,
                        'total' => intval($dayData['total']),
                        'completed' => intval($dayData['completed']),
                        'pending' => intval($dayData['pending']),
                        'failed' => intval($dayData['failed'])
                    ];
                }
                
                // Get top countries
                $topCountries = [];
                try {
                    $topCountries = $db->select("
                        SELECT 
                            COALESCE(c.name, 'غير محدد') as country_name,
                            COUNT(t.id) as transfer_count,
                            COALESCE(SUM(t.total_amount), 0) as total_amount
                        FROM transfers t
                        LEFT JOIN countries c ON t.sender_country_id = c.id
                        WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                        AND (t.deleted_at IS NULL OR t.deleted_at = '')
                        GROUP BY c.id, c.name
                        ORDER BY transfer_count DESC
                        LIMIT 5
                    ");
                } catch (Exception $e) {
                    $topCountries = [];
                }
                
                echo json_encode([
                    'success' => true,
                    'stats' => $stats,
                    'recent_transfers' => $recentTransfers,
                    'recent_users' => $recentUsers,
                    'counts' => [
                        'pending' => intval($pendingCount),
                        'processing' => intval($processingCount),
                        'completed_today' => intval($completedToday),
                        'failed' => intval($failedCount),
                        'total_users' => intval($totalUsers),
                        'active_users' => intval($activeUsers),
                        'new_users_today' => intval($newUsersToday)
                    ],
                    'revenue' => [
                        'today' => floatval($todayRevenue),
                        'month' => floatval($monthRevenue),
                        'year' => floatval($yearRevenue)
                    ],
                    'chart_data' => $chartData,
                    'top_countries' => $topCountries
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'get_countries':
                // Countries management functionality
                $page = intval($_POST['page'] ?? 1);
                $limit = intval($_POST['limit'] ?? 10);
                $search = $_POST['search'] ?? '';
                $status = $_POST['status'] ?? '';
                
                $offset = ($page - 1) * $limit;
                
                // Build conditions
                $conditions = ["(deleted_at IS NULL OR deleted_at = '')"];
                $params = [];
                
                if (!empty($search)) {
                    $conditions[] = "(name LIKE :search OR code LIKE :search OR currency LIKE :search)";
                    $params['search'] = "%$search%";
                }
                
                if (!empty($status)) {
                    $conditions[] = "status = :status";
                    $params['status'] = $status;
                }
                
                $whereClause = implode(' AND ', $conditions);
                
                // Get total count
                $totalQuery = "SELECT COUNT(*) as total FROM countries WHERE $whereClause";
                $total = $db->selectOne($totalQuery, $params)['total'];
                
                // Get countries
                $countriesQuery = "
                    SELECT 
                        c.*,
                        0 as transfer_count,
                        0 as total_volume
                    FROM countries c
                    WHERE $whereClause
                    ORDER BY c.name ASC
                    LIMIT $limit OFFSET $offset
                ";
                $countries = $db->select($countriesQuery, $params);
                
                echo json_encode([
                    'success' => true,
                    'countries' => $countries,
                    'total' => intval($total),
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'get_transfers':
                // Transfers management functionality
                $page = intval($_POST['page'] ?? 1);
                $limit = intval($_POST['limit'] ?? 10);
                $search = $_POST['search'] ?? '';
                $status = $_POST['status'] ?? '';
                
                $offset = ($page - 1) * $limit;
                
                // Build conditions
                $conditions = ["(t.deleted_at IS NULL OR t.deleted_at = '')"];
                $params = [];
                
                if (!empty($search)) {
                    $conditions[] = "(t.transfer_code LIKE :search OR t.sender_name LIKE :search OR t.recipient_name LIKE :search)";
                    $params['search'] = "%$search%";
                }
                
                if (!empty($status)) {
                    $conditions[] = "t.status = :status";
                    $params['status'] = $status;
                }
                
                $whereClause = implode(' AND ', $conditions);
                
                // Get total count
                $totalQuery = "SELECT COUNT(*) as total FROM transfers t WHERE $whereClause";
                $total = $db->selectOne($totalQuery, $params)['total'];
                
                // Get transfers with country names
                $transfersQuery = "
                    SELECT 
                        t.*,
                        sc.name as sender_country_name,
                        rc.name as recipient_country_name
                    FROM transfers t
                    LEFT JOIN countries sc ON t.sender_country_id = sc.id
                    LEFT JOIN countries rc ON t.recipient_country_id = rc.id
                    WHERE $whereClause
                    ORDER BY t.created_at DESC
                    LIMIT $limit OFFSET $offset
                ";
                $transfers = $db->select($transfersQuery, $params);
                
                echo json_encode([
                    'success' => true,
                    'transfers' => $transfers,
                    'total' => intval($total),
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'create_transfer':
                // Create new transfer
                $transferCode = 'TR' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
                
                $transferData = [
                    'transfer_code' => $transferCode,
                    'user_id' => $userData['id'],
                    'sender_name' => $_POST['sender_name'],
                    'sender_phone' => $_POST['sender_phone'],
                    'sender_country_id' => $_POST['sender_country_id'],
                    'recipient_name' => $_POST['recipient_name'],
                    'recipient_phone' => $_POST['recipient_phone'],
                    'recipient_country_id' => $_POST['recipient_country_id'],
                    'amount' => floatval($_POST['amount']),
                    'fees' => floatval($_POST['fees']),
                    'total_amount' => floatval($_POST['amount']) + floatval($_POST['fees']),
                    'exchange_rate' => floatval($_POST['exchange_rate'] ?? 1.0),
                    'status' => 'pending',
                    'payment_method' => $_POST['payment_method'] ?? 'cash',
                    'notes' => $_POST['notes'] ?? ''
                ];
                
                if ($db->insert('transfers', $transferData)) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم إنشاء التحويل بنجاح',
                        'transfer_code' => $transferCode
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => 'فشل في إنشاء التحويل'
                    ], JSON_UNESCAPED_UNICODE);
                }
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الشاملة - <?= SYSTEM_NAME ?></title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Particles.js -->
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

    <style>
        /* ===== ELITE COMPLETE DASHBOARD DESIGN ===== */
        :root {
            /* Elite Color Palette */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

            /* Glass Morphism */
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-bg-strong: rgba(255, 255, 255, 0.12);
            --glass-border: rgba(255, 255, 255, 0.15);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

            /* Spacing & Typography */
            --border-radius: 24px;
            --border-radius-small: 16px;
            --border-radius-large: 32px;
            --font-primary: 'Cairo', 'Inter', sans-serif;
            --transition-normal: 0.3s ease-out;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            background: var(--primary-gradient);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Particles Background */
        #particles-js {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.6;
        }

        /* Main Container */
        .dashboard-container {
            position: relative;
            z-index: 1;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            height: 100vh;
            background: var(--glass-bg-strong);
            backdrop-filter: blur(20px);
            border-left: 1px solid var(--glass-border);
            padding: 20px;
            z-index: 1000;
            overflow-y: auto;
            transition: transform var(--transition-normal);
        }

        .sidebar.collapsed {
            transform: translateX(100%);
        }

        /* Logo Section */
        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--glass-border);
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: var(--success-gradient);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: logoShine 3s infinite;
        }

        @keyframes logoShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }

        .logo-text h3 {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .logo-text p {
            font-size: 0.8rem;
            opacity: 0.7;
            margin: 0;
        }

        /* Navigation */
        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 8px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: white;
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: all var(--transition-normal);
            cursor: pointer;
        }

        .nav-link:hover,
        .nav-link.active {
            background: var(--glass-bg);
            color: white;
            transform: translateX(-4px);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-left: 0;
            margin-right: 300px;
            padding: 20px;
            transition: margin-right var(--transition-normal);
        }

        .main-content.expanded {
            margin-right: 0;
        }

        /* Header */
        .main-header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-large);
            padding: 20px 30px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
        }

        .header-controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .sidebar-toggle {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            color: white;
            padding: 10px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .sidebar-toggle:hover {
            background: var(--primary-gradient);
        }

        /* Content Sections */
        .content-section {
            display: none;
            animation: fadeIn 0.5s ease-out;
        }

        .content-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Cards */
        .elite-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-large);
            padding: 24px;
            margin-bottom: 24px;
            transition: all var(--transition-normal);
        }

        .elite-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--glass-shadow);
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-large);
            padding: 24px;
            text-align: center;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
            opacity: 0;
            transition: opacity var(--transition-normal);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--glass-shadow);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card.success::before { background: var(--success-gradient); }
        .stat-card.warning::before { background: var(--warning-gradient); }
        .stat-card.danger::before { background: var(--danger-gradient); }
        .stat-card.info::before { background: var(--info-gradient); }

        .stat-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 16px;
        }

        .stat-card.success .stat-icon { background: var(--success-gradient); }
        .stat-card.warning .stat-icon { background: var(--warning-gradient); }
        .stat-card.danger .stat-icon { background: var(--danger-gradient); }
        .stat-card.info .stat-icon { background: var(--info-gradient); }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.8;
        }

        /* Buttons */
        .elite-btn {
            padding: 12px 24px;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            background: transparent;
            color: white;
            font-family: var(--font-primary);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-normal);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .elite-btn:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .elite-btn-primary { background: var(--primary-gradient); border-color: transparent; }
        .elite-btn-success { background: var(--success-gradient); border-color: transparent; }
        .elite-btn-warning { background: var(--warning-gradient); border-color: transparent; }
        .elite-btn-danger { background: var(--danger-gradient); border-color: transparent; }
        .elite-btn-info { background: var(--info-gradient); border-color: transparent; }

        /* Forms */
        .form-control {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            color: white;
            padding: 12px;
        }

        .form-control:focus {
            background: var(--glass-bg-strong);
            border-color: #4facfe;
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-select {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            color: white;
        }

        .form-select:focus {
            background: var(--glass-bg-strong);
            border-color: #4facfe;
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }

        .form-select option {
            background: #2c3e50;
            color: white;
        }

        /* Tables */
        .table {
            color: white;
        }

        .table th {
            border-color: var(--glass-border);
            background: var(--glass-bg);
            font-weight: 600;
        }

        .table td {
            border-color: var(--glass-border);
        }

        .table tbody tr:hover {
            background: var(--glass-bg);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Particles Background -->
    <div id="particles-js"></div>

    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <!-- Logo Section -->
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-gem"></i>
                </div>
                <div class="logo-text">
                    <h3>Elite Transfer</h3>
                    <p>نظام التحويلات الفاخر</p>
                </div>
            </div>

            <!-- Navigation Menu -->
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="new-transfer">
                        <i class="fas fa-plus-circle"></i>
                        <span>تحويل جديد</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="transfers">
                        <i class="fas fa-exchange-alt"></i>
                        <span>إدارة التحويلات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="countries">
                        <i class="fas fa-globe"></i>
                        <span>إدارة البلدان</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="analytics">
                        <i class="fas fa-chart-line"></i>
                        <span>التحليلات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="reports">
                        <i class="fas fa-file-alt"></i>
                        <span>التقارير</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="users">
                        <i class="fas fa-users"></i>
                        <span>إدارة المستخدمين</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل الخروج</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <!-- Header -->
            <div class="main-header">
                <h1 class="header-title" id="pageTitle">لوحة التحكم الرئيسية</h1>
                <div class="header-controls">
                    <span>مرحباً، <?= htmlspecialchars($userData['name'] ?? 'المدير') ?></span>
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>

            <!-- Dashboard Section -->
            <div class="content-section active" id="dashboard-section">
                <!-- Stats Grid -->
                <div class="stats-grid" id="statsGrid">
                    <!-- Stats will be loaded here -->
                </div>

                <!-- Charts and Activity -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="elite-card">
                            <h4>اتجاه التحويلات</h4>
                            <canvas id="transfersChart" height="300"></canvas>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="elite-card">
                            <h4>النشاط الحديث</h4>
                            <div id="recentActivity">
                                <!-- Activity will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- New Transfer Section -->
            <div class="content-section" id="new-transfer-section">
                <div class="elite-card">
                    <h4>إنشاء تحويل جديد</h4>
                    <form id="newTransferForm">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>بيانات المرسل</h5>
                                <div class="mb-3">
                                    <label class="form-label">اسم المرسل</label>
                                    <input type="text" class="form-control" name="sender_name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" name="sender_phone" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البلد</label>
                                    <select class="form-select" name="sender_country_id" required>
                                        <option value="">اختر البلد</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>بيانات المستقبل</h5>
                                <div class="mb-3">
                                    <label class="form-label">اسم المستقبل</label>
                                    <input type="text" class="form-control" name="recipient_name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" name="recipient_phone" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البلد</label>
                                    <select class="form-select" name="recipient_country_id" required>
                                        <option value="">اختر البلد</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" name="amount" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الرسوم</label>
                                    <input type="number" class="form-control" name="fees" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">طريقة الدفع</label>
                                    <select class="form-select" name="payment_method">
                                        <option value="cash">نقداً</option>
                                        <option value="card">بطاقة</option>
                                        <option value="bank">تحويل بنكي</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3"></textarea>
                        </div>
                        <button type="submit" class="elite-btn elite-btn-primary">
                            <i class="fas fa-plus"></i>
                            إنشاء التحويل
                        </button>
                    </form>
                </div>
            </div>

            <!-- Transfers Management Section -->
            <div class="content-section" id="transfers-section">
                <div class="elite-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4>إدارة التحويلات</h4>
                        <div class="d-flex gap-2">
                            <input type="text" class="form-control" id="transfersSearch" placeholder="البحث..." style="width: 200px;">
                            <select class="form-select" id="transfersStatus" style="width: 150px;">
                                <option value="">جميع الحالات</option>
                                <option value="pending">معلق</option>
                                <option value="processing">قيد المعالجة</option>
                                <option value="completed">مكتمل</option>
                                <option value="failed">فاشل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                            <button class="elite-btn elite-btn-primary" onclick="loadTransfers()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>كود التحويل</th>
                                    <th>المرسل</th>
                                    <th>المستقبل</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="transfersTableBody">
                                <!-- Transfers will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                    <div id="transfersPagination" class="d-flex justify-content-center mt-3">
                        <!-- Pagination will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Countries Management Section -->
            <div class="content-section" id="countries-section">
                <div class="elite-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4>إدارة البلدان</h4>
                        <div class="d-flex gap-2">
                            <input type="text" class="form-control" id="countriesSearch" placeholder="البحث..." style="width: 200px;">
                            <select class="form-select" id="countriesStatus" style="width: 150px;">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                            <button class="elite-btn elite-btn-primary" onclick="loadCountries()">
                                <i class="fas fa-search"></i>
                            </button>
                            <button class="elite-btn elite-btn-success" onclick="showAddCountryModal()">
                                <i class="fas fa-plus"></i>
                                إضافة بلد
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>الرمز</th>
                                    <th>العملة</th>
                                    <th>سعر الصرف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="countriesTableBody">
                                <!-- Countries will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                    <div id="countriesPagination" class="d-flex justify-content-center mt-3">
                        <!-- Pagination will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div class="content-section" id="analytics-section">
                <div class="elite-card">
                    <h4>التحليلات المتقدمة</h4>
                    <p>قريباً - سيتم تطوير هذا القسم</p>
                </div>
            </div>

            <!-- Reports Section -->
            <div class="content-section" id="reports-section">
                <div class="elite-card">
                    <h4>التقارير المتقدمة</h4>
                    <p>قريباً - سيتم تطوير هذا القسم</p>
                </div>
            </div>

            <!-- Users Section -->
            <div class="content-section" id="users-section">
                <div class="elite-card">
                    <h4>إدارة المستخدمين</h4>
                    <p>قريباً - سيتم تطوير هذا القسم</p>
                </div>
            </div>

            <!-- Settings Section -->
            <div class="content-section" id="settings-section">
                <div class="elite-card">
                    <h4>إعدادات النظام</h4>
                    <p>قريباً - سيتم تطوير هذا القسم</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript -->
    <script>
        // ===== GLOBAL VARIABLES =====
        let currentSection = 'dashboard';
        let dashboardData = {};
        let transfersChart = null;

        // ===== INITIALIZATION =====
        document.addEventListener('DOMContentLoaded', function() {
            initializeParticles();
            setupEventListeners();
            loadDashboardData();
            loadCountriesForSelect();
        });

        // ===== PARTICLES BACKGROUND =====
        function initializeParticles() {
            particlesJS('particles-js', {
                particles: {
                    number: { value: 80, density: { enable: true, value_area: 800 } },
                    color: { value: '#ffffff' },
                    shape: { type: 'circle' },
                    opacity: { value: 0.1, random: false },
                    size: { value: 3, random: true },
                    line_linked: {
                        enable: true,
                        distance: 150,
                        color: '#ffffff',
                        opacity: 0.1,
                        width: 1
                    },
                    move: {
                        enable: true,
                        speed: 2,
                        direction: 'none',
                        random: false,
                        straight: false,
                        out_mode: 'out',
                        bounce: false
                    }
                },
                interactivity: {
                    detect_on: 'canvas',
                    events: {
                        onhover: { enable: true, mode: 'repulse' },
                        onclick: { enable: true, mode: 'push' },
                        resize: true
                    }
                },
                retina_detect: true
            });
        }

        // ===== EVENT LISTENERS =====
        function setupEventListeners() {
            // Navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.getAttribute('href') === '#') {
                        e.preventDefault();
                        const section = this.dataset.section;
                        if (section) {
                            switchSection(section);
                        }
                    }
                });
            });

            // Sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.getElementById('mainContent');

                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });

            // New transfer form
            document.getElementById('newTransferForm').addEventListener('submit', function(e) {
                e.preventDefault();
                createTransfer();
            });

            // Search and filter events
            document.getElementById('transfersSearch').addEventListener('input', debounce(loadTransfers, 500));
            document.getElementById('transfersStatus').addEventListener('change', loadTransfers);
            document.getElementById('countriesSearch').addEventListener('input', debounce(loadCountries, 500));
            document.getElementById('countriesStatus').addEventListener('change', loadCountries);
        }

        // ===== SECTION SWITCHING =====
        function switchSection(sectionName) {
            // Update navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

            // Update content
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(`${sectionName}-section`).classList.add('active');

            // Update title
            const titles = {
                'dashboard': 'لوحة التحكم الرئيسية',
                'new-transfer': 'تحويل جديد',
                'transfers': 'إدارة التحويلات',
                'countries': 'إدارة البلدان',
                'analytics': 'التحليلات المتقدمة',
                'reports': 'التقارير المتقدمة',
                'users': 'إدارة المستخدمين',
                'settings': 'إعدادات النظام'
            };
            document.getElementById('pageTitle').textContent = titles[sectionName] || 'لوحة التحكم';

            currentSection = sectionName;

            // Load section-specific data
            switch (sectionName) {
                case 'transfers':
                    loadTransfers();
                    break;
                case 'countries':
                    loadCountries();
                    break;
            }
        }

        // ===== DASHBOARD DATA =====
        function loadDashboardData() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_dashboard_data' },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        dashboardData = response;
                        renderStats(response.stats, response.counts, response.revenue);
                        renderChart(response.chart_data);
                        renderRecentActivity(response.recent_transfers);
                    } else {
                        showError('فشل في تحميل البيانات: ' + (response.message || 'خطأ غير معروف'));
                    }
                },
                error: function(xhr, status, error) {
                    showError('خطأ في الاتصال بالخادم: ' + error);
                }
            });
        }

        // ===== RENDER STATS =====
        function renderStats(stats, counts, revenue) {
            const statsGrid = document.getElementById('statsGrid');

            const statsCards = [
                {
                    icon: 'fas fa-exchange-alt',
                    value: stats.total_transfers || 0,
                    label: 'إجمالي التحويلات',
                    type: 'primary'
                },
                {
                    icon: 'fas fa-check-circle',
                    value: stats.completed_transfers || 0,
                    label: 'التحويلات المكتملة',
                    type: 'success'
                },
                {
                    icon: 'fas fa-clock',
                    value: counts.pending || 0,
                    label: 'التحويلات المعلقة',
                    type: 'warning'
                },
                {
                    icon: 'fas fa-dollar-sign',
                    value: '$' + (revenue.today || 0).toLocaleString(),
                    label: 'إيرادات اليوم',
                    type: 'info'
                },
                {
                    icon: 'fas fa-users',
                    value: counts.total_users || 0,
                    label: 'إجمالي المستخدمين',
                    type: 'success'
                },
                {
                    icon: 'fas fa-times-circle',
                    value: counts.failed || 0,
                    label: 'التحويلات الفاشلة',
                    type: 'danger'
                }
            ];

            statsGrid.innerHTML = statsCards.map(card => `
                <div class="stat-card ${card.type}">
                    <div class="stat-icon">
                        <i class="${card.icon}"></i>
                    </div>
                    <div class="stat-value">${card.value}</div>
                    <div class="stat-label">${card.label}</div>
                </div>
            `).join('');
        }

        // ===== RENDER CHART =====
        function renderChart(chartData) {
            const ctx = document.getElementById('transfersChart').getContext('2d');

            if (transfersChart) {
                transfersChart.destroy();
            }

            const labels = chartData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' });
            });

            transfersChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'مكتملة',
                            data: chartData.map(item => item.completed),
                            borderColor: '#4facfe',
                            backgroundColor: 'rgba(79, 172, 254, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'معلقة',
                            data: chartData.map(item => item.pending),
                            borderColor: '#43e97b',
                            backgroundColor: 'rgba(67, 233, 123, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: 'white',
                                font: { family: 'Cairo', size: 12 }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' }
                        },
                        y: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: 'rgba(255, 255, 255, 0.7)' }
                        }
                    }
                }
            });
        }

        // ===== RENDER RECENT ACTIVITY =====
        function renderRecentActivity(transfers) {
            const activityDiv = document.getElementById('recentActivity');

            if (!transfers || transfers.length === 0) {
                activityDiv.innerHTML = '<p class="text-center">لا توجد أنشطة حديثة</p>';
                return;
            }

            activityDiv.innerHTML = transfers.slice(0, 5).map(transfer => `
                <div class="d-flex align-items-center mb-3 p-2 rounded" style="background: var(--glass-bg);">
                    <div class="me-3">
                        <i class="fas fa-exchange-alt text-primary"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${transfer.transfer_code}</div>
                        <small class="text-muted">${transfer.sender_name} → ${transfer.recipient_name}</small>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold">$${transfer.total_amount}</div>
                        <small class="badge bg-${getStatusColor(transfer.status)}">${getStatusText(transfer.status)}</small>
                    </div>
                </div>
            `).join('');
        }

        // ===== TRANSFERS MANAGEMENT =====
        function loadTransfers(page = 1) {
            const search = document.getElementById('transfersSearch').value;
            const status = document.getElementById('transfersStatus').value;

            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_transfers',
                    page: page,
                    limit: 10,
                    search: search,
                    status: status
                },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        renderTransfersTable(response.transfers);
                        renderPagination('transfers', response.page, response.pages);
                    } else {
                        showError('فشل في تحميل التحويلات');
                    }
                },
                error: function() {
                    showError('خطأ في تحميل التحويلات');
                }
            });
        }

        function renderTransfersTable(transfers) {
            const tbody = document.getElementById('transfersTableBody');

            if (!transfers || transfers.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد تحويلات</td></tr>';
                return;
            }

            tbody.innerHTML = transfers.map(transfer => `
                <tr>
                    <td>${transfer.transfer_code}</td>
                    <td>
                        <div>${transfer.sender_name}</div>
                        <small class="text-muted">${transfer.sender_country_name || 'غير محدد'}</small>
                    </td>
                    <td>
                        <div>${transfer.recipient_name}</div>
                        <small class="text-muted">${transfer.recipient_country_name || 'غير محدد'}</small>
                    </td>
                    <td>$${transfer.total_amount}</td>
                    <td><span class="badge bg-${getStatusColor(transfer.status)}">${getStatusText(transfer.status)}</span></td>
                    <td>${formatDate(transfer.created_at)}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="viewTransfer(${transfer.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="editTransfer(${transfer.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // ===== COUNTRIES MANAGEMENT =====
        function loadCountries(page = 1) {
            const search = document.getElementById('countriesSearch').value;
            const status = document.getElementById('countriesStatus').value;

            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_countries',
                    page: page,
                    limit: 10,
                    search: search,
                    status: status
                },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        renderCountriesTable(response.countries);
                        renderPagination('countries', response.page, response.pages);
                    } else {
                        showError('فشل في تحميل البلدان');
                    }
                },
                error: function() {
                    showError('خطأ في تحميل البلدان');
                }
            });
        }

        function renderCountriesTable(countries) {
            const tbody = document.getElementById('countriesTableBody');

            if (!countries || countries.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد بلدان</td></tr>';
                return;
            }

            tbody.innerHTML = countries.map(country => `
                <tr>
                    <td>${country.name}</td>
                    <td>${country.code}</td>
                    <td>${country.currency}</td>
                    <td>${country.exchange_rate}</td>
                    <td><span class="badge bg-${country.status === 'active' ? 'success' : 'secondary'}">${country.status === 'active' ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-warning me-1" onclick="editCountry(${country.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCountry(${country.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // ===== NEW TRANSFER =====
        function loadCountriesForSelect() {
            $.ajax({
                url: '',
                method: 'POST',
                data: { action: 'get_countries', page: 1, limit: 100 },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        const options = response.countries.map(country =>
                            `<option value="${country.id}">${country.name} (${country.code})</option>`
                        ).join('');

                        document.querySelectorAll('select[name="sender_country_id"], select[name="recipient_country_id"]').forEach(select => {
                            select.innerHTML = '<option value="">اختر البلد</option>' + options;
                        });
                    }
                }
            });
        }

        function createTransfer() {
            const formData = new FormData(document.getElementById('newTransferForm'));
            formData.append('action', 'create_transfer');

            $.ajax({
                url: '',
                method: 'POST',
                data: Object.fromEntries(formData),
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'نجح!',
                            text: response.message,
                            confirmButtonText: 'حسناً'
                        });
                        document.getElementById('newTransferForm').reset();
                        switchSection('transfers');
                    } else {
                        showError(response.message || 'فشل في إنشاء التحويل');
                    }
                },
                error: function() {
                    showError('خطأ في إنشاء التحويل');
                }
            });
        }

        // ===== UTILITY FUNCTIONS =====
        function renderPagination(type, currentPage, totalPages) {
            const paginationDiv = document.getElementById(`${type}Pagination`);

            if (totalPages <= 1) {
                paginationDiv.innerHTML = '';
                return;
            }

            let pagination = '<nav><ul class="pagination">';

            // Previous button
            if (currentPage > 1) {
                pagination += `<li class="page-item"><a class="page-link" href="#" onclick="load${type.charAt(0).toUpperCase() + type.slice(1)}(${currentPage - 1})">السابق</a></li>`;
            }

            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                pagination += `<li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="load${type.charAt(0).toUpperCase() + type.slice(1)}(${i})">${i}</a>
                </li>`;
            }

            // Next button
            if (currentPage < totalPages) {
                pagination += `<li class="page-item"><a class="page-link" href="#" onclick="load${type.charAt(0).toUpperCase() + type.slice(1)}(${currentPage + 1})">التالي</a></li>`;
            }

            pagination += '</ul></nav>';
            paginationDiv.innerHTML = pagination;
        }

        function getStatusColor(status) {
            const colors = {
                'pending': 'warning',
                'processing': 'info',
                'completed': 'success',
                'failed': 'danger',
                'cancelled': 'secondary'
            };
            return colors[status] || 'secondary';
        }

        function getStatusText(status) {
            const texts = {
                'pending': 'معلق',
                'processing': 'قيد المعالجة',
                'completed': 'مكتمل',
                'failed': 'فاشل',
                'cancelled': 'ملغي'
            };
            return texts[status] || status;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: message,
                confirmButtonText: 'حسناً'
            });
        }

        function showSuccess(message) {
            Swal.fire({
                icon: 'success',
                title: 'نجح!',
                text: message,
                confirmButtonText: 'حسناً'
            });
        }

        // Placeholder functions for future development
        function viewTransfer(id) {
            showError('هذه الوظيفة قيد التطوير');
        }

        function editTransfer(id) {
            showError('هذه الوظيفة قيد التطوير');
        }

        function editCountry(id) {
            showError('هذه الوظيفة قيد التطوير');
        }

        function deleteCountry(id) {
            showError('هذه الوظيفة قيد التطوير');
        }

        function showAddCountryModal() {
            showError('هذه الوظيفة قيد التطوير');
        }
    </script>
</body>
</html>
