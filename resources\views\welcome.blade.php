@extends('layouts.app')

@section('title', 'Elite Financial Transfer System - Secure Money Transfer')

@section('content')
<div class="container-fluid">
    <!-- Hero Section -->
    <div class="row min-vh-100 align-items-center">
        <div class="col-lg-6">
            <div class="px-5">
                <h1 class="display-4 fw-bold text-white mb-4">
                    {{ __('Elite Financial Transfer System') }}
                    <span class="text-warning">6.0</span>
                </h1>
                <p class="lead text-white-50 mb-4">
                    {{ __('Send money worldwide with confidence. Fast, secure, and reliable international money transfer service with AI-powered fraud detection and real-time tracking.') }}
                </p>
                
                <div class="d-flex gap-3 mb-5">
                    @guest
                        <a href="{{ route('register') }}" class="btn btn-primary btn-lg">
                            <i class="bi bi-person-plus me-2"></i>
                            {{ __('Get Started') }}
                        </a>
                        <a href="{{ route('login') }}" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            {{ __('Sign In') }}
                        </a>
                    @else
                        <a href="{{ route('transfers.create') }}" class="btn btn-primary btn-lg">
                            <i class="bi bi-send me-2"></i>
                            {{ __('Send Money Now') }}
                        </a>
                        <a href="{{ route('dashboard') }}" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-speedometer2 me-2"></i>
                            {{ __('Dashboard') }}
                        </a>
                    @endguest
                </div>
                
                <!-- Quick Track -->
                <div class="card bg-white bg-opacity-10 border-0">
                    <div class="card-body">
                        <h5 class="text-white mb-3">
                            <i class="bi bi-search me-2"></i>
                            {{ __('Track Your Transfer') }}
                        </h5>
                        <form action="{{ route('track.transfer') }}" method="POST" id="quickTrackForm">
                            @csrf
                            <div class="input-group">
                                <input type="text" 
                                       class="form-control" 
                                       name="transfer_code" 
                                       placeholder="{{ __('Enter transfer code (e.g., TRF20240125ABC123)') }}"
                                       required>
                                <button class="btn btn-warning" type="submit">
                                    <i class="bi bi-search"></i>
                                    {{ __('Track') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="text-center">
                <div class="card mx-auto" style="max-width: 500px;">
                    <div class="card-body p-5">
                        <div class="mb-4">
                            <i class="bi bi-bank2 text-primary" style="font-size: 4rem;"></i>
                        </div>
                        <h3 class="mb-4">{{ __('Why Choose Elite Transfer?') }}</h3>
                        
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="text-center">
                                    <i class="bi bi-shield-check text-success fs-2"></i>
                                    <h6 class="mt-2">{{ __('Secure') }}</h6>
                                    <small class="text-muted">{{ __('Bank-level security') }}</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <i class="bi bi-lightning text-warning fs-2"></i>
                                    <h6 class="mt-2">{{ __('Fast') }}</h6>
                                    <small class="text-muted">{{ __('Minutes not days') }}</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <i class="bi bi-globe text-info fs-2"></i>
                                    <h6 class="mt-2">{{ __('Global') }}</h6>
                                    <small class="text-muted">{{ __('200+ countries') }}</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <i class="bi bi-cpu text-primary fs-2"></i>
                                    <h6 class="mt-2">{{ __('AI-Powered') }}</h6>
                                    <small class="text-muted">{{ __('Smart fraud detection') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="container my-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="display-5 fw-bold text-white">{{ __('Advanced Features') }}</h2>
            <p class="lead text-white-50">{{ __('Experience the future of money transfer') }}</p>
        </div>
    </div>
    
    <div class="row g-4">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-eye text-primary fs-1 mb-3"></i>
                    <h5>{{ __('Real-time Tracking') }}</h5>
                    <p class="text-muted">{{ __('Track your transfer every step of the way with live updates and notifications.') }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-people text-success fs-1 mb-3"></i>
                    <h5>{{ __('Multi-Branch Network') }}</h5>
                    <p class="text-muted">{{ __('Access our extensive network of branches and agents worldwide.') }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-currency-exchange text-warning fs-1 mb-3"></i>
                    <h5>{{ __('Multi-Currency Support') }}</h5>
                    <p class="text-muted">{{ __('Send and receive money in multiple currencies with competitive exchange rates.') }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-check text-info fs-1 mb-3"></i>
                    <h5>{{ __('Scheduled Transfers') }}</h5>
                    <p class="text-muted">{{ __('Schedule transfers for future dates and set up recurring payments.') }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-phone text-primary fs-1 mb-3"></i>
                    <h5>{{ __('OmniChannel Notifications') }}</h5>
                    <p class="text-muted">{{ __('Get updates via SMS, email, WhatsApp, and push notifications.') }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-qr-code text-success fs-1 mb-3"></i>
                    <h5>{{ __('QR Code Pickup') }}</h5>
                    <p class="text-muted">{{ __('Quick and secure pickup using QR codes and digital verification.') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Section -->
<div class="container my-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="display-5 fw-bold text-white">{{ __('Trusted by Millions') }}</h2>
        </div>
    </div>
    
    <div class="row g-4">
        <div class="col-md-3">
            <div class="stats-card">
                <h3>2M+</h3>
                <p class="mb-0">{{ __('Happy Customers') }}</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h3>200+</h3>
                <p class="mb-0">{{ __('Countries Served') }}</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h3>$50B+</h3>
                <p class="mb-0">{{ __('Money Transferred') }}</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h3>99.9%</h3>
                <p class="mb-0">{{ __('Success Rate') }}</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.getElementById('quickTrackForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    
    setLoading(submitBtn, true);
    
    axios.post(this.action, formData)
        .then(response => {
            if (response.data.success) {
                // Show tracking results in a modal or redirect
                showTrackingResults(response.data.data);
            } else {
                showNotification(response.data.message, 'error');
            }
        })
        .catch(error => {
            const message = error.response?.data?.message || 'An error occurred while tracking the transfer.';
            showNotification(message, 'error');
        })
        .finally(() => {
            setLoading(submitBtn, false);
        });
});

function showTrackingResults(data) {
    // Create and show modal with tracking information
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-search me-2"></i>
                        Transfer Tracking: ${data.transfer_code}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Transfer Details</h6>
                            <p><strong>Status:</strong> <span class="status-badge status-${data.status}">${data.status}</span></p>
                            <p><strong>Amount:</strong> ${data.amount} ${data.sender_currency}</p>
                            <p><strong>Converted Amount:</strong> ${data.converted_amount} ${data.receiver_currency}</p>
                            <p><strong>From:</strong> ${data.sender_country}</p>
                            <p><strong>To:</strong> ${data.receiver_country}</p>
                            <p><strong>Receiver:</strong> ${data.receiver_name}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Timeline</h6>
                            <div class="timeline">
                                ${data.timeline.map(item => `
                                    <div class="timeline-item">
                                        <small class="text-muted">${new Date(item.timestamp).toLocaleString()}</small>
                                        <p class="mb-1"><strong>${item.status}</strong></p>
                                        ${item.notes ? `<small>${item.notes}</small>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}
</script>
@endpush
