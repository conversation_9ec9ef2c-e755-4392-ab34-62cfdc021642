<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_ar')->nullable();
            $table->string('code', 3)->unique();
            $table->string('symbol', 10);
            $table->integer('decimal_places')->default(2);
            $table->decimal('exchange_rate_to_usd', 15, 8)->default(1.00000000);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_base_currency')->default(false);
            $table->timestamp('last_updated')->nullable();
            $table->decimal('volatility_index', 8, 4)->default(0.0000);
            $table->decimal('daily_change_percentage', 8, 4)->default(0.0000);
            $table->decimal('market_cap', 20, 2)->nullable();
            $table->decimal('trading_volume', 20, 2)->nullable();
            $table->timestamps();

            $table->index(['is_active']);
            $table->index(['is_base_currency']);
            $table->index(['code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currencies');
    }
};
