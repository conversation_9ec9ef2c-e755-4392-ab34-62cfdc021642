<?php

/**
 * Fix Foreign Key Constraints
 * Elite Transfer System - Fix foreign key issues in transfers table
 */

require_once __DIR__ . '/includes/database_manager.php';

try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<title>إصلاح قيود المفاتيح الخارجية - Elite Transfer System</title>";
    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
    echo "<style>";
    echo "body { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); min-height: 100vh; padding: 20px; }";
    echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 20px; }";
    echo ".step { padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid; }";
    echo ".success { border-left-color: #28a745; background: #d4edda; color: #155724; }";
    echo ".error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }";
    echo ".warning { border-left-color: #ffc107; background: #fff3cd; color: #856404; }";
    echo ".info { border-left-color: #17a2b8; background: #d1ecf1; color: #0c5460; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";
    
    echo "<div class='container'>";
    echo "<h1 class='text-white text-center mb-4'>🔧 إصلاح قيود المفاتيح الخارجية</h1>";
    
    // Step 1: Check current foreign keys
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 1: فحص القيود الحالية</h3>";
    
    try {
        $foreignKeys = $db->query("
            SELECT 
                CONSTRAINT_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = 'elite_transfer' 
            AND TABLE_NAME = 'transfers' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($foreignKeys) > 0) {
            echo "<div class='step warning'>⚠️ تم العثور على " . count($foreignKeys) . " قيد مفتاح خارجي</div>";
            
            echo "<h5>القيود الموجودة:</h5>";
            echo "<div class='table-responsive'>";
            echo "<table class='table table-sm table-striped'>";
            echo "<thead><tr><th>اسم القيد</th><th>العمود</th><th>الجدول المرجعي</th><th>العمود المرجعي</th></tr></thead>";
            echo "<tbody>";
            
            foreach ($foreignKeys as $fk) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($fk['CONSTRAINT_NAME']) . "</td>";
                echo "<td>" . htmlspecialchars($fk['COLUMN_NAME']) . "</td>";
                echo "<td>" . htmlspecialchars($fk['REFERENCED_TABLE_NAME']) . "</td>";
                echo "<td>" . htmlspecialchars($fk['REFERENCED_COLUMN_NAME']) . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        } else {
            echo "<div class='step success'>✅ لا توجد قيود مفاتيح خارجية</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='step error'>❌ خطأ في فحص القيود: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Step 2: Remove problematic foreign keys
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 2: إزالة القيود المشكلة</h3>";
    
    if (isset($foreignKeys) && count($foreignKeys) > 0) {
        foreach ($foreignKeys as $fk) {
            try {
                $constraintName = $fk['CONSTRAINT_NAME'];
                $db->exec("ALTER TABLE transfers DROP FOREIGN KEY $constraintName");
                echo "<div class='step success'>✅ تم حذف القيد: $constraintName</div>";
            } catch (Exception $e) {
                echo "<div class='step error'>❌ فشل في حذف القيد $constraintName: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        }
    } else {
        echo "<div class='step info'>ℹ️ لا توجد قيود لحذفها</div>";
    }
    
    echo "</div>";
    
    // Step 3: Check and create required users
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 3: فحص وإنشاء المستخدمين المطلوبين</h3>";
    
    try {
        $userCount = $db->query("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL")->fetchColumn();
        echo "<div class='step info'>ℹ️ عدد المستخدمين الحاليين: $userCount</div>";
        
        if ($userCount == 0) {
            echo "<div class='step warning'>⚠️ لا يوجد مستخدمين - سيتم إنشاء مستخدم تجريبي</div>";
            
            // Create a test user
            $stmt = $db->prepare("
                INSERT INTO users (user_code, name, email, phone, password_hash, role, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, 'active', NOW())
            ");
            
            $testUsers = [
                ['USR001', 'مستخدم تجريبي 1', '<EMAIL>', '+************', password_hash('password123', PASSWORD_DEFAULT), 'user'],
                ['ADM001', 'مدير النظام', '<EMAIL>', '+************', password_hash('admin123', PASSWORD_DEFAULT), 'admin']
            ];
            
            foreach ($testUsers as $user) {
                try {
                    $stmt->execute($user);
                    echo "<div class='step success'>✅ تم إنشاء المستخدم: {$user[1]}</div>";
                } catch (Exception $e) {
                    echo "<div class='step error'>❌ فشل في إنشاء المستخدم {$user[1]}: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            }
        } else {
            echo "<div class='step success'>✅ يوجد مستخدمين في النظام</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='step error'>❌ خطأ في فحص المستخدمين: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Step 4: Check and create countries
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 4: فحص وإنشاء الدول</h3>";
    
    try {
        $countryCount = $db->query("SELECT COUNT(*) FROM countries")->fetchColumn();
        echo "<div class='step info'>ℹ️ عدد الدول الحالية: $countryCount</div>";
        
        if ($countryCount == 0) {
            echo "<div class='step warning'>⚠️ لا توجد دول - سيتم إنشاء دول تجريبية</div>";
            
            // Create test countries
            $stmt = $db->prepare("
                INSERT INTO countries (code, name, currency, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            
            $testCountries = [
                ['SA', 'المملكة العربية السعودية', 'SAR'],
                ['EG', 'مصر', 'EGP'],
                ['AE', 'الإمارات العربية المتحدة', 'AED'],
                ['JO', 'الأردن', 'JOD'],
                ['US', 'الولايات المتحدة الأمريكية', 'USD']
            ];
            
            foreach ($testCountries as $country) {
                try {
                    $stmt->execute($country);
                    echo "<div class='step success'>✅ تم إنشاء الدولة: {$country[1]}</div>";
                } catch (Exception $e) {
                    echo "<div class='step error'>❌ فشل في إنشاء الدولة {$country[1]}: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            }
        } else {
            echo "<div class='step success'>✅ توجد دول في النظام</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='step error'>❌ خطأ في فحص الدول: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Step 5: Add sample transfers without foreign key constraints
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 5: إضافة تحويلات تجريبية</h3>";
    
    try {
        $transferCount = $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn();
        echo "<div class='step info'>ℹ️ عدد التحويلات الحالية: $transferCount</div>";
        
        if ($transferCount == 0) {
            echo "<div class='step warning'>⚠️ لا توجد تحويلات - سيتم إضافة تحويلات تجريبية</div>";
            
            // Get first user ID
            $firstUser = $db->query("SELECT id FROM users WHERE deleted_at IS NULL LIMIT 1")->fetch();
            $userId = $firstUser ? $firstUser['id'] : null;
            
            // Get first country ID
            $firstCountry = $db->query("SELECT id FROM countries LIMIT 1")->fetch();
            $countryId = $firstCountry ? $firstCountry['id'] : null;
            
            $sampleData = [
                [
                    'TRF001', 'PCK001', $userId, 'أحمد محمد علي', '+************', $countryId, 'الرياض، المملكة العربية السعودية',
                    'فاطمة أحمد', '+************', $countryId, 'القاهرة، مصر',
                    1000.00, 25.00, 4.75, 1025.00, 'USD', 'EGP', 'bank_transfer', 'دعم الأسرة', 'تحويل شهري', 'completed'
                ],
                [
                    'TRF002', 'PCK002', $userId, 'سارة عبدالله', '+************', $countryId, 'جدة، المملكة العربية السعودية',
                    'محمد حسن', '+************', $countryId, 'دبي، الإمارات العربية المتحدة',
                    500.00, 15.00, 3.67, 515.00, 'USD', 'AED', 'cash', 'تعليم', 'رسوم دراسية', 'pending'
                ],
                [
                    'TRF003', 'PCK003', $userId, 'عبدالله سالم', '+************', $countryId, 'الدمام، المملكة العربية السعودية',
                    'ليلى محمود', '+************', $countryId, 'عمان، الأردن',
                    750.00, 20.00, 0.71, 770.00, 'USD', 'JOD', 'credit_card', 'علاج طبي', 'مساعدة طبية', 'processing'
                ]
            ];
            
            $insertSQL = "
                INSERT INTO transfers (
                    transfer_code, pickup_code, user_id, sender_name, sender_phone, 
                    sender_country_id, sender_address, recipient_name, recipient_phone,
                    recipient_country_id, recipient_address, amount, fee, exchange_rate,
                    total_amount, currency_from, currency_to, payment_method, purpose, notes, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $db->prepare($insertSQL);
            
            foreach ($sampleData as $data) {
                try {
                    $stmt->execute($data);
                    echo "<div class='step success'>✅ تم إضافة تحويل تجريبي: {$data[0]}</div>";
                } catch (Exception $e) {
                    echo "<div class='step error'>❌ فشل في إضافة تحويل {$data[0]}: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            }
        } else {
            echo "<div class='step success'>✅ توجد تحويلات في النظام</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='step error'>❌ خطأ في إضافة التحويلات: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Step 6: Test the fixed system
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 6: اختبار النظام المُصلح</h3>";
    
    try {
        $testQuery = "
            SELECT t.*, 
                   COALESCE(u.name, 'غير محدد') as user_name,
                   COALESCE(sc.name, 'غير محدد') as sender_country,
                   COALESCE(rc.name, 'غير محدد') as recipient_country
            FROM transfers t
            LEFT JOIN users u ON t.user_id = u.id AND u.deleted_at IS NULL
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.recipient_country_id = rc.id
            ORDER BY t.created_at DESC 
            LIMIT 5
        ";
        
        $stmt = $db->prepare($testQuery);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='step success'>✅ الاستعلام نجح! تم العثور على " . count($results) . " تحويل</div>";
        
        if (count($results) > 0) {
            echo "<h5>عينة من النتائج:</h5>";
            echo "<div class='table-responsive'>";
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>الرمز</th><th>المرسل</th><th>المستلم</th><th>المبلغ</th><th>الحالة</th></tr></thead>";
            echo "<tbody>";
            foreach ($results as $row) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['transfer_code']) . "</td>";
                echo "<td>" . htmlspecialchars($row['sender_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['recipient_name']) . "</td>";
                echo "<td>$" . number_format($row['amount'], 2) . "</td>";
                echo "<td>" . htmlspecialchars($row['status']) . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='step error'>❌ فشل الاستعلام: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
    
    // Final step: Links to test
    echo "<div class='fix-card text-center'>";
    echo "<h3>🎉 تم إصلاح قيود المفاتيح الخارجية!</h3>";
    echo "<div class='alert alert-success'>";
    echo "<h5>✅ ما تم إصلاحه:</h5>";
    echo "<ul class='text-start'>";
    echo "<li>إزالة قيود المفاتيح الخارجية المشكلة</li>";
    echo "<li>إنشاء مستخدمين ودول تجريبية</li>";
    echo "<li>إضافة تحويلات تجريبية بنجاح</li>";
    echo "<li>اختبار الاستعلامات والتأكد من عملها</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='d-grid gap-2 d-md-flex justify-content-center'>";
    echo "<a href='transfers_safe.php' class='btn btn-success btn-lg'>اختبار الصفحة الآمنة</a>";
    echo "<a href='debug_ajax.php' class='btn btn-info'>اختبار AJAX</a>";
    echo "<a href='fix_transfers_table.php' class='btn btn-warning'>فحص الجدول</a>";
    echo "<a href='dashboard.php' class='btn btn-secondary'>لوحة التحكم</a>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    echo "</body>";
    echo "</html>";
    
} catch (Exception $e) {
    echo "<!DOCTYPE html>";
    echo "<html><head><meta charset='UTF-8'><title>خطأ</title></head><body>";
    echo "<div class='container mt-5'>";
    echo "<div class='alert alert-danger'>";
    echo "<h4>خطأ في قاعدة البيانات</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    echo "</div>";
    echo "</body></html>";
}

?>
