<?php

/**
 * Enhanced Session Helper v2.0
 * Elite Transfer System - Advanced session management with security features
 */

// Load configuration
require_once __DIR__ . '/config.php';

// Enhanced session security
if (session_status() === PHP_SESSION_NONE) {
    // Set secure session parameters
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Strict');
    
    session_name(SESSION_NAME);
    session_set_cookie_params([
        'lifetime' => SESSION_LIFETIME,
        'path' => '/',
        'domain' => '',
        'secure' => isset($_SERVER['HTTPS']),
        'httponly' => true,
        'samesite' => 'Strict'
    ]);
    
    session_start();
    
    // Regenerate session ID periodically
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
    
    // Check for session hijacking
    if (!isset($_SESSION['user_agent'])) {
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
    } elseif ($_SESSION['user_agent'] !== ($_SERVER['HTTP_USER_AGENT'] ?? '')) {
        session_destroy();
        session_start();
        logMessage('WARNING', 'Potential session hijacking detected', [
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    }
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Get current user data
 */
function getUserData() {
    if (!isLoggedIn()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'] ?? null,
        'name' => $_SESSION['name'] ?? '',
        'email' => $_SESSION['email'] ?? '',
        'role' => $_SESSION['role'] ?? 'user',
        'phone' => $_SESSION['phone'] ?? '',
        'status' => $_SESSION['status'] ?? 'active',
        'last_login' => $_SESSION['last_login'] ?? null,
        'permissions' => $_SESSION['permissions'] ?? []
    ];
}

/**
 * Get user ID
 */
function getUserId() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * Get user name
 */
function getUserName() {
    return $_SESSION['name'] ?? 'مستخدم';
}

/**
 * Get user email
 */
function getUserEmail() {
    return $_SESSION['email'] ?? '';
}

/**
 * Get user role
 */
function getUserRole() {
    return $_SESSION['role'] ?? 'user';
}

/**
 * Check if user has specific role
 */
function hasRole($role) {
    return getUserRole() === $role;
}

/**
 * Check if user is admin
 */
function isAdmin() {
    return hasRole(ROLE_ADMIN);
}

/**
 * Check if user is manager
 */
function isManager() {
    return hasRole(ROLE_MANAGER) || isAdmin();
}

/**
 * Check if user has permission
 */
function hasPermission($permission) {
    $permissions = $_SESSION['permissions'] ?? [];
    return in_array($permission, $permissions) || isAdmin();
}

/**
 * Login user
 */
function loginUser($userData) {
    // Regenerate session ID for security
    session_regenerate_id(true);
    
    $_SESSION['user_id'] = $userData['id'];
    $_SESSION['name'] = $userData['name'];
    $_SESSION['email'] = $userData['email'];
    $_SESSION['role'] = $userData['role'];
    $_SESSION['phone'] = $userData['phone'] ?? '';
    $_SESSION['status'] = $userData['status'] ?? 'active';
    $_SESSION['last_login'] = date('Y-m-d H:i:s');
    $_SESSION['permissions'] = $userData['permissions'] ?? [];
    $_SESSION['login_time'] = time();
    $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? '';
    
    // Update last login in database
    try {
        require_once __DIR__ . '/database_manager_v2.php';
        $db = DatabaseManager::getInstance();
        $db->update('users', 
            ['last_login' => date('Y-m-d H:i:s')], 
            'id = :id', 
            ['id' => $userData['id']]
        );
    } catch (Exception $e) {
        logMessage('ERROR', 'Failed to update last login', ['error' => $e->getMessage()]);
    }
    
    logMessage('INFO', 'User logged in successfully', [
        'user_id' => $userData['id'],
        'email' => $userData['email'],
        'role' => $userData['role']
    ]);
    
    return true;
}

/**
 * Logout user
 */
function logout() {
    $userId = getUserId();
    $userEmail = getUserEmail();
    
    // Clear session data
    $_SESSION = [];
    
    // Delete session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Destroy session
    session_destroy();
    
    logMessage('INFO', 'User logged out successfully', [
        'user_id' => $userId,
        'email' => $userEmail
    ]);
    
    return true;
}

/**
 * Auto-login for testing (admin user)
 */
function autoLoginAdmin() {
    if (isLoggedIn()) {
        return true;
    }
    
    try {
        require_once __DIR__ . '/database_manager_v2.php';
        $db = DatabaseManager::getInstance();
        
        $admin = $db->selectOne(
            "SELECT * FROM users WHERE role = :role AND status = 'active' AND (deleted_at IS NULL OR deleted_at = '') LIMIT 1",
            ['role' => ROLE_ADMIN]
        );
        
        if ($admin) {
            loginUser($admin);
            logMessage('INFO', 'Auto-login performed for admin user');
            return true;
        }
        
        // Create admin user if doesn't exist
        $adminData = [
            'user_code' => 'ADM001',
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'phone' => '+966507654321',
            'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
            'role' => ROLE_ADMIN,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $adminId = $db->insert('users', $adminData);
        if ($adminId) {
            $adminData['id'] = $adminId;
            loginUser($adminData);
            logMessage('INFO', 'Admin user created and auto-login performed');
            return true;
        }
        
    } catch (Exception $e) {
        logMessage('ERROR', 'Auto-login failed', ['error' => $e->getMessage()]);
    }
    
    return false;
}

/**
 * Check session timeout
 */
function checkSessionTimeout() {
    if (!isLoggedIn()) {
        return false;
    }
    
    $loginTime = $_SESSION['login_time'] ?? 0;
    $currentTime = time();
    
    if ($currentTime - $loginTime > SESSION_LIFETIME) {
        logout();
        logMessage('INFO', 'Session expired and user logged out');
        return false;
    }
    
    return true;
}

/**
 * Require login - redirect if not logged in
 */
function requireLogin($redirectUrl = 'login.php') {
    if (!isLoggedIn() || !checkSessionTimeout()) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Require admin role
 */
function requireAdmin($redirectUrl = 'dashboard.php') {
    requireLogin();
    
    if (!isAdmin()) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Require specific role
 */
function requireRole($role, $redirectUrl = 'dashboard.php') {
    requireLogin();
    
    if (!hasRole($role) && !isAdmin()) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(CSRF_TOKEN_LENGTH));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Get session info for debugging
 */
function getSessionInfo() {
    return [
        'logged_in' => isLoggedIn(),
        'user_data' => getUserData(),
        'session_id' => session_id(),
        'session_name' => session_name(),
        'session_lifetime' => SESSION_LIFETIME,
        'login_time' => $_SESSION['login_time'] ?? null,
        'last_regeneration' => $_SESSION['last_regeneration'] ?? null,
        'ip_address' => $_SESSION['ip_address'] ?? null,
        'user_agent' => $_SESSION['user_agent'] ?? null
    ];
}

/**
 * Flash messages system
 */
function setFlashMessage($type, $message) {
    if (!isset($_SESSION['flash_messages'])) {
        $_SESSION['flash_messages'] = [];
    }
    $_SESSION['flash_messages'][] = [
        'type' => $type,
        'message' => $message,
        'timestamp' => time()
    ];
}

function getFlashMessages() {
    $messages = $_SESSION['flash_messages'] ?? [];
    unset($_SESSION['flash_messages']);
    return $messages;
}

function hasFlashMessages() {
    return !empty($_SESSION['flash_messages']);
}

// Auto-login for development/testing
if (defined('AUTO_LOGIN_ADMIN') && AUTO_LOGIN_ADMIN === true) {
    autoLoginAdmin();
}

?>
