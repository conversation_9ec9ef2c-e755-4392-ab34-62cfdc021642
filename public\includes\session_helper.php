<?php

/**
 * Session Helper Functions
 * Handles session management safely
 */

if (!function_exists('safe_session_start')) {
    /**
     * Start session safely - only if not already started
     */
    function safe_session_start() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
}

if (!function_exists('is_logged_in')) {
    /**
     * Check if user is logged in
     */
    function is_logged_in() {
        safe_session_start();
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
}

if (!function_exists('get_user_data')) {
    /**
     * Get current user data
     */
    function get_user_data() {
        safe_session_start();
        return [
            'id' => $_SESSION['user_id'] ?? null,
            'name' => $_SESSION['name'] ?? '',
            'email' => $_SESSION['email'] ?? '',
            'role' => $_SESSION['role'] ?? 'customer',
            'last_login_at' => $_SESSION['last_login_at'] ?? null
        ];
    }
}

if (!function_exists('require_login')) {
    /**
     * Require user to be logged in
     */
    function require_login() {
        if (!is_logged_in()) {
            header('Location: /login');
            exit;
        }
    }
}

if (!function_exists('require_admin')) {
    /**
     * Require user to be admin
     */
    function require_admin() {
        require_login();
        $user = get_user_data();
        if ($user['role'] !== 'admin') {
            header('Location: /dashboard');
            exit;
        }
    }
}

if (!function_exists('logout_user')) {
    /**
     * Logout user safely
     */
    function logout_user() {
        safe_session_start();
        session_unset();
        session_destroy();
        
        // Start new session for logout message
        session_start();
        $_SESSION['logout_message'] = 'تم تسجيل الخروج بنجاح';
    }
}

if (!function_exists('set_flash_message')) {
    /**
     * Set flash message
     */
    function set_flash_message($message, $type = 'info') {
        safe_session_start();
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }
}

if (!function_exists('get_flash_message')) {
    /**
     * Get and clear flash message
     */
    function get_flash_message() {
        safe_session_start();
        $message = $_SESSION['flash_message'] ?? null;
        $type = $_SESSION['flash_type'] ?? 'info';
        
        // Clear the message
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
        
        return $message ? ['message' => $message, 'type' => $type] : null;
    }
}

// Aliases for backward compatibility
if (!function_exists('isLoggedIn')) {
    function isLoggedIn() {
        return is_logged_in();
    }
}

if (!function_exists('getUserData')) {
    function getUserData() {
        return get_user_data();
    }
}

if (!function_exists('isAdmin')) {
    function isAdmin() {
        safe_session_start();
        $user = get_user_data();
        return $user['role'] === 'admin' || $user['role'] === 'manager';
    }
}

if (!function_exists('requireLogin')) {
    function requireLogin() {
        return require_login();
    }
}

if (!function_exists('requireAdmin')) {
    function requireAdmin() {
        return require_admin();
    }
}

// Auto-start session safely
safe_session_start();

?>
