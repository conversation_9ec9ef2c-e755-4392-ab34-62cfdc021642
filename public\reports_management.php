<?php

/**
 * Reports Management Page
 * Elite Transfer System - Comprehensive reporting system
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Auto-login for testing
if (!isLoggedIn()) {
    autoLoginAdmin();
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $db = DatabaseManager::getInstance();
        
        switch ($_POST['action']) {
            case 'get_statistics':
                $dateFrom = $_POST['date_from'] ?? date('Y-m-01');
                $dateTo = $_POST['date_to'] ?? date('Y-m-d');
                
                // Basic statistics
                $stats = $db->getStatistics();
                
                // Transfers by date range
                $transfersByDate = $db->select("
                    SELECT DATE(created_at) as date, COUNT(*) as count, SUM(amount) as total_amount
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN ? AND ? 
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY DATE(created_at)
                    ORDER BY date DESC
                ", [$dateFrom, $dateTo]);
                
                // Transfers by status
                $transfersByStatus = $db->select("
                    SELECT status, COUNT(*) as count, SUM(amount) as total_amount
                    FROM transfers 
                    WHERE DATE(created_at) BETWEEN ? AND ? 
                    AND (deleted_at IS NULL OR deleted_at = '')
                    GROUP BY status
                ", [$dateFrom, $dateTo]);
                
                // Top countries
                $topCountries = $db->select("
                    SELECT c.name, COUNT(t.id) as transfer_count, SUM(t.amount) as total_amount
                    FROM transfers t
                    LEFT JOIN countries c ON t.sender_country_id = c.id
                    WHERE DATE(t.created_at) BETWEEN ? AND ? 
                    AND (t.deleted_at IS NULL OR t.deleted_at = '')
                    GROUP BY c.id, c.name
                    ORDER BY transfer_count DESC
                    LIMIT 10
                ", [$dateFrom, $dateTo]);
                
                echo json_encode([
                    'success' => true,
                    'stats' => $stats,
                    'transfers_by_date' => $transfersByDate,
                    'transfers_by_status' => $transfersByStatus,
                    'top_countries' => $topCountries
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'export_report':
                $reportType = $_POST['report_type'];
                $dateFrom = $_POST['date_from'] ?? date('Y-m-01');
                $dateTo = $_POST['date_to'] ?? date('Y-m-d');
                
                switch ($reportType) {
                    case 'transfers':
                        $data = $db->select("
                            SELECT t.transfer_code, t.pickup_code, t.sender_name, t.recipient_name,
                                   t.amount, t.fee, t.total_amount, t.status, t.created_at,
                                   sc.name as sender_country, rc.name as recipient_country
                            FROM transfers t
                            LEFT JOIN countries sc ON t.sender_country_id = sc.id
                            LEFT JOIN countries rc ON t.recipient_country_id = rc.id
                            WHERE DATE(t.created_at) BETWEEN ? AND ?
                            AND (t.deleted_at IS NULL OR t.deleted_at = '')
                            ORDER BY t.created_at DESC
                        ", [$dateFrom, $dateTo]);
                        break;
                        
                    case 'users':
                        $data = $db->select("
                            SELECT user_code, name, email, phone, role, status, created_at, last_login
                            FROM users
                            WHERE DATE(created_at) BETWEEN ? AND ?
                            AND (deleted_at IS NULL OR deleted_at = '')
                            ORDER BY created_at DESC
                        ", [$dateFrom, $dateTo]);
                        break;
                        
                    default:
                        throw new Exception('نوع تقرير غير صحيح');
                }
                
                // Generate CSV
                $csv = generateCSV($data, $reportType);
                
                echo json_encode([
                    'success' => true,
                    'csv_data' => $csv,
                    'filename' => $reportType . '_report_' . date('Y-m-d') . '.csv'
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح'], JSON_UNESCAPED_UNICODE);
        }
        
    } catch (Exception $e) {
        logMessage('ERROR', 'Reports management error', ['error' => $e->getMessage()]);
        echo json_encode(['success' => false, 'message' => 'خطأ في الخادم: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
    
    exit;
}

function generateCSV($data, $type) {
    if (empty($data)) return '';
    
    $output = '';
    
    // Add BOM for UTF-8
    $output .= "\xEF\xBB\xBF";
    
    // Add headers
    $headers = array_keys($data[0]);
    $output .= implode(',', $headers) . "\n";
    
    // Add data
    foreach ($data as $row) {
        $output .= implode(',', array_map(function($value) {
            return '"' . str_replace('"', '""', $value) . '"';
        }, $row)) . "\n";
    }
    
    return $output;
}

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8fafc;
        }
        
        .page-header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #10b981;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6b7280;
            font-weight: 500;
        }
        
        .chart-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .chart-header {
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            padding: 20px;
            font-weight: 600;
        }
        
        .chart-body {
            padding: 20px;
        }
        
        .filters-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .btn {
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
        }
        
        .export-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .table-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .table th {
            background: #f8fafc;
            border-top: none;
            font-weight: 600;
            color: #374151;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-processing { background: #dbeafe; color: #1e40af; }
        .status-completed { background: #d1fae5; color: #065f46; }
        .status-cancelled { background: #fee2e2; color: #991b1b; }
        .status-failed { background: #fee2e2; color: #991b1b; }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="bi bi-graph-up me-3"></i>
                        التقارير والإحصائيات
                    </h1>
                    <p class="mb-0 opacity-75">تقارير شاملة وإحصائيات مفصلة لجميع عمليات النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="export-buttons">
                        <button class="btn btn-light" onclick="exportReport('transfers')">
                            <i class="bi bi-download me-2"></i>
                            تصدير التحويلات
                        </button>
                        <button class="btn btn-outline-light" onclick="exportReport('users')">
                            <i class="bi bi-people me-2"></i>
                            تصدير المستخدمين
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filters -->
        <div class="filters-card">
            <h5 class="mb-3">
                <i class="bi bi-funnel me-2"></i>
                فلاتر التقارير
            </h5>
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="dateFrom" value="<?= date('Y-m-01') ?>">
                </div>
                <div class="col-md-4">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="dateTo" value="<?= date('Y-m-d') ?>">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" onclick="loadReports()">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        تحديث التقارير
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <i class="bi bi-arrow-left-right stat-icon"></i>
                <div class="stat-number" id="totalTransfers">0</div>
                <div class="stat-label">إجمالي التحويلات</div>
            </div>
            
            <div class="stat-card">
                <i class="bi bi-check-circle stat-icon" style="color: #10b981;"></i>
                <div class="stat-number" id="completedTransfers">0</div>
                <div class="stat-label">تحويلات مكتملة</div>
            </div>
            
            <div class="stat-card">
                <i class="bi bi-currency-dollar stat-icon" style="color: #f59e0b;"></i>
                <div class="stat-number" id="totalAmount">$0</div>
                <div class="stat-label">إجمالي المبلغ</div>
            </div>
            
            <div class="stat-card">
                <i class="bi bi-calendar-day stat-icon" style="color: #8b5cf6;"></i>
                <div class="stat-number" id="todayTransfers">0</div>
                <div class="stat-label">تحويلات اليوم</div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row">
            <div class="col-md-8">
                <div class="chart-card">
                    <div class="chart-header">
                        <h5 class="mb-0">
                            <i class="bi bi-bar-chart me-2"></i>
                            التحويلات حسب التاريخ
                        </h5>
                    </div>
                    <div class="chart-body">
                        <canvas id="transfersChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="chart-card">
                    <div class="chart-header">
                        <h5 class="mb-0">
                            <i class="bi bi-pie-chart me-2"></i>
                            التحويلات حسب الحالة
                        </h5>
                    </div>
                    <div class="chart-body">
                        <canvas id="statusChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Countries Table -->
        <div class="table-card">
            <div class="chart-header">
                <h5 class="mb-0">
                    <i class="bi bi-globe me-2"></i>
                    أهم البلدان المرسلة
                </h5>
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>البلد</th>
                            <th>عدد التحويلات</th>
                            <th>إجمالي المبلغ</th>
                            <th>النسبة</th>
                        </tr>
                    </thead>
                    <tbody id="countriesTableBody">
                        <tr>
                            <td colspan="4" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Back to Dashboard -->
        <div class="text-center mt-4">
            <a href="dashboard.php" class="btn btn-outline-primary">
                <i class="bi bi-arrow-right me-2"></i>
                العودة إلى لوحة التحكم
            </a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        let transfersChart, statusChart;
        
        $(document).ready(function() {
            console.log('🚀 Reports Management Page Loaded');
            loadReports();
        });
        
        function loadReports() {
            const dateFrom = $('#dateFrom').val();
            const dateTo = $('#dateTo').val();
            
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_statistics',
                    date_from: dateFrom,
                    date_to: dateTo
                },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        updateStatistics(response.stats);
                        updateTransfersChart(response.transfers_by_date);
                        updateStatusChart(response.transfers_by_status);
                        updateCountriesTable(response.top_countries);
                    }
                },
                error: function() {
                    showAlert('خطأ في تحميل التقارير', 'danger');
                }
            });
        }
        
        function updateStatistics(stats) {
            $('#totalTransfers').text(new Intl.NumberFormat('ar-SA').format(stats.total_transfers || 0));
            $('#completedTransfers').text(new Intl.NumberFormat('ar-SA').format(stats.completed_transfers || 0));
            $('#totalAmount').text('$' + new Intl.NumberFormat('ar-SA', {minimumFractionDigits: 2}).format(stats.total_amount || 0));
            $('#todayTransfers').text(new Intl.NumberFormat('ar-SA').format(stats.today_transfers || 0));
        }
        
        function updateTransfersChart(data) {
            const ctx = document.getElementById('transfersChart').getContext('2d');
            
            if (transfersChart) {
                transfersChart.destroy();
            }
            
            const labels = data.map(item => new Date(item.date).toLocaleDateString('ar-SA'));
            const counts = data.map(item => parseInt(item.count));
            const amounts = data.map(item => parseFloat(item.total_amount));
            
            transfersChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'عدد التحويلات',
                        data: counts,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: 'إجمالي المبلغ ($)',
                        data: amounts,
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }
        
        function updateStatusChart(data) {
            const ctx = document.getElementById('statusChart').getContext('2d');
            
            if (statusChart) {
                statusChart.destroy();
            }
            
            const statusLabels = {
                'pending': 'في الانتظار',
                'processing': 'قيد المعالجة',
                'completed': 'مكتمل',
                'cancelled': 'ملغي',
                'failed': 'فاشل'
            };
            
            const labels = data.map(item => statusLabels[item.status] || item.status);
            const counts = data.map(item => parseInt(item.count));
            
            statusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: counts,
                        backgroundColor: [
                            '#fbbf24',
                            '#3b82f6',
                            '#10b981',
                            '#ef4444',
                            '#6b7280'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
        
        function updateCountriesTable(countries) {
            const tbody = $('#countriesTableBody');
            tbody.empty();
            
            if (countries.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="4" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد بيانات</p>
                        </td>
                    </tr>
                `);
                return;
            }
            
            const totalTransfers = countries.reduce((sum, country) => sum + parseInt(country.transfer_count), 0);
            
            countries.forEach(country => {
                const percentage = totalTransfers > 0 ? ((parseInt(country.transfer_count) / totalTransfers) * 100).toFixed(1) : 0;
                
                const row = `
                    <tr>
                        <td>
                            <strong>${country.name || 'غير محدد'}</strong>
                        </td>
                        <td>${new Intl.NumberFormat('ar-SA').format(country.transfer_count)}</td>
                        <td>$${new Intl.NumberFormat('ar-SA', {minimumFractionDigits: 2}).format(country.total_amount)}</td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar bg-success" style="width: ${percentage}%">${percentage}%</div>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
        
        function exportReport(reportType) {
            const dateFrom = $('#dateFrom').val();
            const dateTo = $('#dateTo').val();
            
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'export_report',
                    report_type: reportType,
                    date_from: dateFrom,
                    date_to: dateTo
                },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        downloadCSV(response.csv_data, response.filename);
                        showAlert('تم تصدير التقرير بنجاح', 'success');
                    } else {
                        showAlert(response.message || 'فشل في تصدير التقرير', 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في تصدير التقرير', 'danger');
                }
            });
        }
        
        function downloadCSV(csvData, filename) {
            const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);
            
            setTimeout(function() {
                $('.alert').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    </script>
</body>
</html>
