<?php

/**
 * Enhanced Database Manager
 * Elite Transfer System - Production Database Connection
 */

class DatabaseManager {
    private static $instance = null;
    private $connection;
    private $config;
    
    private function __construct() {
        $this->loadConfig();
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function loadConfig() {
        // Load environment variables
        $envFile = __DIR__ . '/../../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value, '"\'');
                }
            }
        }
        
        // Database configuration
        $this->config = [
            'connection' => $_ENV['DB_CONNECTION'] ?? 'sqlite',
            'database' => $_ENV['DB_DATABASE'] ?? 'database/elite_transfer_production.db',
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? '3306',
            'username' => $_ENV['DB_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_PASSWORD'] ?? '',
            'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4',
            'foreign_keys' => $_ENV['DB_FOREIGN_KEYS'] ?? 'true',
            'wal_mode' => $_ENV['DB_WAL_MODE'] ?? 'true'
        ];
    }
    
    private function connect() {
        try {
            if ($this->config['connection'] === 'sqlite') {
                $this->connectSQLite();
            } else {
                $this->connectMySQL();
            }
            
            $this->optimizeConnection();
            
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    private function connectSQLite() {
        $dbPath = __DIR__ . '/../../' . $this->config['database'];
        
        // Ensure directory exists
        $dbDir = dirname($dbPath);
        if (!is_dir($dbDir)) {
            mkdir($dbDir, 0755, true);
        }
        
        $dsn = 'sqlite:' . $dbPath;
        $this->connection = new PDO($dsn);
        
        // SQLite specific optimizations
        if ($this->config['foreign_keys'] === 'true') {
            $this->connection->exec("PRAGMA foreign_keys = ON");
        }
        
        if ($this->config['wal_mode'] === 'true') {
            $this->connection->exec("PRAGMA journal_mode = WAL");
        }
        
        $this->connection->exec("PRAGMA synchronous = NORMAL");
        $this->connection->exec("PRAGMA cache_size = 10000");
        $this->connection->exec("PRAGMA temp_store = MEMORY");
    }
    
    private function connectMySQL() {
        $dsn = sprintf(
            'mysql:host=%s;port=%s;dbname=%s;charset=%s',
            $this->config['host'],
            $this->config['port'],
            $this->config['database'],
            $this->config['charset']
        );
        
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->config['charset']}"
        ];
        
        $this->connection = new PDO(
            $dsn,
            $this->config['username'],
            $this->config['password'],
            $options
        );
    }
    
    private function optimizeConnection() {
        $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $this->connection->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $this->connection->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            $this->logError("Query failed: " . $e->getMessage(), $sql, $params);
            throw new Exception("Database query failed: " . $e->getMessage());
        }
    }
    
    public function fetchAll($sql, $params = []) {
        return $this->query($sql, $params)->fetchAll();
    }
    
    public function fetchOne($sql, $params = []) {
        return $this->query($sql, $params)->fetch();
    }
    
    public function fetchColumn($sql, $params = []) {
        return $this->query($sql, $params)->fetchColumn();
    }
    
    public function insert($table, $data) {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($data);
        
        return $this->connection->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $column) {
            $setClause[] = "{$column} = :{$column}";
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $setClause) . " WHERE {$where}";
        
        $params = array_merge($data, $whereParams);
        $stmt = $this->connection->prepare($sql);
        
        return $stmt->execute($params);
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->connection->prepare($sql);
        return $stmt->execute($params);
    }
    
    public function softDelete($table, $id) {
        return $this->update($table, ['deleted_at' => date('Y-m-d H:i:s')], 'id = :id', ['id' => $id]);
    }
    
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    public function commit() {
        return $this->connection->commit();
    }
    
    public function rollback() {
        return $this->connection->rollback();
    }
    
    public function getLastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    // Utility methods
    public function tableExists($tableName) {
        if ($this->config['connection'] === 'sqlite') {
            $sql = "SELECT name FROM sqlite_master WHERE type='table' AND name = ?";
        } else {
            $sql = "SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?";
            return $this->fetchColumn($sql, [$this->config['database'], $tableName]) !== false;
        }
        return $this->fetchColumn($sql, [$tableName]) !== false;
    }
    
    public function getTableColumns($tableName) {
        if ($this->config['connection'] === 'sqlite') {
            $sql = "PRAGMA table_info({$tableName})";
        } else {
            $sql = "SELECT COLUMN_NAME FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?";
            return $this->fetchAll($sql, [$this->config['database'], $tableName]);
        }
        return $this->fetchAll($sql);
    }
    
    public function getTableCount($tableName) {
        $sql = "SELECT COUNT(*) FROM {$tableName}";
        return $this->fetchColumn($sql);
    }
    
    public function vacuum() {
        if ($this->config['connection'] === 'sqlite') {
            return $this->connection->exec("VACUUM");
        }
        return true; // MySQL doesn't need vacuum
    }
    
    public function analyze() {
        if ($this->config['connection'] === 'sqlite') {
            return $this->connection->exec("ANALYZE");
        } else {
            return $this->connection->exec("ANALYZE TABLE");
        }
    }
    
    public function getConnectionInfo() {
        return [
            'type' => $this->config['connection'],
            'database' => $this->config['database'],
            'host' => $this->config['host'] ?? 'N/A',
            'charset' => $this->config['charset'] ?? 'N/A'
        ];
    }
    
    private function logError($message, $sql = '', $params = []) {
        $logMessage = date('Y-m-d H:i:s') . " - " . $message;
        if ($sql) {
            $logMessage .= "\nSQL: " . $sql;
        }
        if ($params) {
            $logMessage .= "\nParams: " . json_encode($params);
        }
        
        error_log($logMessage, 3, __DIR__ . '/../../logs/database_errors.log');
    }
}

// Helper functions for quick access
function getDB() {
    return DatabaseManager::getInstance()->getConnection();
}

function dbQuery($sql, $params = []) {
    return DatabaseManager::getInstance()->query($sql, $params);
}

function dbFetchAll($sql, $params = []) {
    return DatabaseManager::getInstance()->fetchAll($sql, $params);
}

function dbFetchOne($sql, $params = []) {
    return DatabaseManager::getInstance()->fetchOne($sql, $params);
}

function dbFetchColumn($sql, $params = []) {
    return DatabaseManager::getInstance()->fetchColumn($sql, $params);
}

function dbInsert($table, $data) {
    return DatabaseManager::getInstance()->insert($table, $data);
}

function dbUpdate($table, $data, $where, $whereParams = []) {
    return DatabaseManager::getInstance()->update($table, $data, $where, $whereParams);
}

function dbDelete($table, $where, $params = []) {
    return DatabaseManager::getInstance()->delete($table, $where, $params);
}

function dbSoftDelete($table, $id) {
    return DatabaseManager::getInstance()->softDelete($table, $id);
}

?>
