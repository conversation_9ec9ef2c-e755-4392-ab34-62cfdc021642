<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('phone')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamp('phone_verified_at')->nullable();
            $table->string('password');
            $table->enum('role', ['super_admin', 'admin', 'agent', 'customer'])->default('customer');
            $table->foreignId('branch_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('country_id')->nullable()->constrained()->nullOnDelete();
            $table->string('national_id')->nullable();
            $table->text('address')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->enum('gender', ['male', 'female', 'other'])->nullable();
            $table->string('profile_photo')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('two_factor_enabled')->default(false);
            $table->string('two_factor_secret')->nullable();
            $table->timestamp('last_login_at')->nullable();
            $table->integer('login_attempts')->default(0);
            $table->timestamp('locked_until')->nullable();
            $table->string('preferred_language', 2)->default('ar');
            $table->string('preferred_currency', 3)->default('USD');
            $table->enum('kyc_status', ['pending', 'approved', 'rejected', 'under_review'])->default('pending');
            $table->json('kyc_documents')->nullable();
            $table->integer('risk_score')->default(0);
            $table->string('agent_code')->nullable()->unique();
            $table->decimal('commission_rate', 5, 2)->nullable();
            $table->decimal('daily_limit', 15, 2)->default(5000.00);
            $table->decimal('monthly_limit', 15, 2)->default(50000.00);
            $table->integer('total_transfers_sent')->default(0);
            $table->integer('total_transfers_received')->default(0);
            $table->decimal('total_amount_sent', 15, 2)->default(0.00);
            $table->decimal('total_amount_received', 15, 2)->default(0.00);
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['role', 'is_active']);
            $table->index(['branch_id', 'role']);
            $table->index(['country_id']);
            $table->index(['kyc_status']);
            $table->index(['agent_code']);
            $table->index(['email']);
            $table->index(['phone']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
