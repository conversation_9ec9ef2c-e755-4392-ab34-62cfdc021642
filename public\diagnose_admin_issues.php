<?php

/**
 * Diagnose Admin Issues
 * Elite Transfer System - Comprehensive diagnosis of admin page issues
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تشخيص مشاكل الصفحات الإدارية - Elite Transfer System</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }";
echo ".diagnosis-card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 20px; }";
echo ".test-item { padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid; }";
echo ".success { border-left-color: #28a745; background: #d4edda; color: #155724; }";
echo ".error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }";
echo ".warning { border-left-color: #ffc107; background: #fff3cd; color: #856404; }";
echo ".info { border-left-color: #17a2b8; background: #d1ecf1; color: #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 class='text-white text-center mb-4'>🔍 تشخيص شامل للصفحات الإدارية</h1>";

// Test 1: Session and Authentication
echo "<div class='diagnosis-card'>";
echo "<h3>1. اختبار الجلسة والمصادقة</h3>";

try {
    require_once __DIR__ . '/includes/session_helper.php';
    echo "<div class='test-item success'>✅ تم تحميل session_helper.php بنجاح</div>";
    
    $functions = ['isLoggedIn', 'getUserData', 'isAdmin'];
    foreach ($functions as $func) {
        if (function_exists($func)) {
            echo "<div class='test-item success'>✅ الوظيفة $func() متاحة</div>";
        } else {
            echo "<div class='test-item error'>❌ الوظيفة $func() غير متاحة</div>";
        }
    }
    
    if (function_exists('isLoggedIn')) {
        if (isLoggedIn()) {
            echo "<div class='test-item success'>✅ المستخدم مسجل دخول</div>";
            
            if (function_exists('getUserData')) {
                $userData = getUserData();
                echo "<div class='test-item info'>ℹ️ المستخدم: " . ($userData['name'] ?? 'غير محدد') . " (" . ($userData['role'] ?? 'غير محدد') . ")</div>";
            }
            
            if (function_exists('isAdmin') && isAdmin()) {
                echo "<div class='test-item success'>✅ المستخدم لديه صلاحيات إدارية</div>";
            } else {
                echo "<div class='test-item warning'>⚠️ المستخدم ليس لديه صلاحيات إدارية</div>";
            }
        } else {
            echo "<div class='test-item warning'>⚠️ المستخدم غير مسجل دخول</div>";
            echo "<div class='test-item info'>ℹ️ <a href='auto_login.php' class='btn btn-sm btn-primary'>تسجيل دخول تلقائي</a></div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ خطأ في session_helper: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 2: Database Connection
echo "<div class='diagnosis-card'>";
echo "<h3>2. اختبار قاعدة البيانات</h3>";

try {
    require_once __DIR__ . '/includes/database_manager.php';
    echo "<div class='test-item success'>✅ تم تحميل database_manager.php بنجاح</div>";
    
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    echo "<div class='test-item success'>✅ الاتصال بقاعدة البيانات نجح</div>";
    
    $connectionInfo = $dbManager->getConnectionInfo();
    echo "<div class='test-item info'>ℹ️ نوع قاعدة البيانات: " . $connectionInfo['type'] . "</div>";
    echo "<div class='test-item info'>ℹ️ قاعدة البيانات: " . $connectionInfo['database'] . "</div>";
    echo "<div class='test-item info'>ℹ️ الخادم: " . $connectionInfo['host'] . "</div>";
    
    // Test tables
    $tables = ['users', 'transfers', 'countries', 'exchange_rates'];
    foreach ($tables as $table) {
        try {
            $count = $db->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            echo "<div class='test-item success'>✅ جدول $table: $count سجل</div>";
        } catch (Exception $e) {
            echo "<div class='test-item error'>❌ جدول $table: " . $e->getMessage() . "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 3: Admin Pages
echo "<div class='diagnosis-card'>";
echo "<h3>3. اختبار الصفحات الإدارية</h3>";

$adminPages = [
    'admin_users_enhanced.php' => 'إدارة المستخدمين',
    'admin_transfers_enhanced.php' => 'إدارة التحويلات',
    'admin_reports_enhanced.php' => 'التقارير',
    'admin_monitoring_enhanced.php' => 'مراقبة النظام',
    'admin_compliance_enhanced.php' => 'الامتثال التنظيمي',
    'fix_transfers_page.php' => 'صفحة التحويلات المُصلحة'
];

foreach ($adminPages as $file => $title) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "<div class='test-item success'>✅ $title ($file) موجود</div>";
        
        // Test file size
        $size = filesize(__DIR__ . '/' . $file);
        if ($size > 1000) {
            echo "<div class='test-item info'>ℹ️ حجم الملف: " . number_format($size / 1024, 1) . " KB</div>";
        } else {
            echo "<div class='test-item warning'>⚠️ حجم الملف صغير: $size bytes</div>";
        }
    } else {
        echo "<div class='test-item error'>❌ $title ($file) غير موجود</div>";
    }
}

echo "</div>";

// Test 4: AJAX Endpoints
echo "<div class='diagnosis-card'>";
echo "<h3>4. اختبار نقاط AJAX</h3>";

if (isset($db)) {
    // Test transfers endpoint
    try {
        $stmt = $db->prepare("
            SELECT t.*, 
                   COALESCE(u.name, 'غير محدد') as user_name,
                   COALESCE(sc.name, 'غير محدد') as sender_country,
                   COALESCE(rc.name, 'غير محدد') as recipient_country
            FROM transfers t
            LEFT JOIN users u ON t.user_id = u.id
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.recipient_country_id = rc.id
            WHERE t.deleted_at IS NULL 
            ORDER BY t.created_at DESC 
            LIMIT 5
        ");
        $stmt->execute();
        $transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<div class='test-item success'>✅ استعلام التحويلات نجح - " . count($transfers) . " تحويل</div>";
    } catch (Exception $e) {
        echo "<div class='test-item error'>❌ استعلام التحويلات فشل: " . $e->getMessage() . "</div>";
    }
    
    // Test users endpoint
    try {
        $stmt = $db->prepare("
            SELECT u.*, c.name as country_name
            FROM users u
            LEFT JOIN countries c ON u.country_id = c.id
            WHERE u.deleted_at IS NULL
            ORDER BY u.created_at DESC
            LIMIT 5
        ");
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<div class='test-item success'>✅ استعلام المستخدمين نجح - " . count($users) . " مستخدم</div>";
    } catch (Exception $e) {
        echo "<div class='test-item error'>❌ استعلام المستخدمين فشل: " . $e->getMessage() . "</div>";
    }
}

echo "</div>";

// Test 5: JavaScript Libraries
echo "<div class='diagnosis-card'>";
echo "<h3>5. اختبار مكتبات JavaScript</h3>";

echo "<div class='test-item info'>ℹ️ اختبار تحميل المكتبات...</div>";
echo "<script src='https://code.jquery.com/jquery-3.7.0.min.js'></script>";
echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script src='https://cdn.jsdelivr.net/npm/chart.js'></script>";

echo "<script>";
echo "$(document).ready(function() {";
echo "  if (typeof jQuery !== 'undefined') {";
echo "    $('#jquery-test').html('<div class=\"test-item success\">✅ jQuery محمل بنجاح (إصدار ' + jQuery.fn.jquery + ')</div>');";
echo "  } else {";
echo "    $('#jquery-test').html('<div class=\"test-item error\">❌ jQuery غير محمل</div>');";
echo "  }";
echo "  ";
echo "  if (typeof bootstrap !== 'undefined') {";
echo "    $('#bootstrap-test').html('<div class=\"test-item success\">✅ Bootstrap محمل بنجاح</div>');";
echo "  } else {";
echo "    $('#bootstrap-test').html('<div class=\"test-item error\">❌ Bootstrap غير محمل</div>');";
echo "  }";
echo "  ";
echo "  if (typeof Chart !== 'undefined') {";
echo "    $('#chart-test').html('<div class=\"test-item success\">✅ Chart.js محمل بنجاح</div>');";
echo "  } else {";
echo "    $('#chart-test').html('<div class=\"test-item error\">❌ Chart.js غير محمل</div>');";
echo "  }";
echo "});";
echo "</script>";

echo "<div id='jquery-test'></div>";
echo "<div id='bootstrap-test'></div>";
echo "<div id='chart-test'></div>";

echo "</div>";

// Solutions
echo "<div class='diagnosis-card'>";
echo "<h3>6. الحلول والإصلاحات</h3>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>🔧 أدوات الإصلاح:</h5>";
echo "<div class='d-grid gap-2'>";
echo "<a href='fix_admin_pages.php' class='btn btn-primary'>إصلاح شامل للصفحات</a>";
echo "<a href='check_transfers_table.php' class='btn btn-info'>فحص جدول التحويلات</a>";
echo "<a href='auto_login.php' class='btn btn-success'>تسجيل دخول تلقائي</a>";
echo "<a href='database_status.php' class='btn btn-warning'>حالة قاعدة البيانات</a>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>📋 الصفحات المُصلحة:</h5>";
echo "<div class='d-grid gap-2'>";
echo "<a href='fix_transfers_page.php' class='btn btn-outline-primary'>صفحة التحويلات المُصلحة</a>";
echo "<a href='admin_users_enhanced.php' class='btn btn-outline-success'>إدارة المستخدمين</a>";
echo "<a href='admin_reports_enhanced.php' class='btn btn-outline-info'>التقارير</a>";
echo "<a href='dashboard.php' class='btn btn-outline-secondary'>لوحة التحكم</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h5>💡 نصائح لحل المشاكل:</h5>";
echo "<ul>";
echo "<li>تأكد من تشغيل خادم MySQL</li>";
echo "<li>تحقق من صحة بيانات الاتصال في ملف .env</li>";
echo "<li>استخدم أدوات الإصلاح المتوفرة</li>";
echo "<li>راجع سجلات الأخطاء في المتصفح (F12)</li>";
echo "<li>تأكد من وجود جميع الملفات المطلوبة</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

?>
