<?php

/**
 * MySQL Connection Test
 * Elite Transfer System - Test MySQL Database Connection
 */

echo "🔗 MySQL Connection Test - Elite Transfer System\n";
echo str_repeat("=", 60) . "\n\n";

// MySQL Configuration
$config = [
    'host' => 'localhost',
    'port' => '3306',
    'database' => 'elite_transfer',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4'
];

echo "📋 MySQL Configuration:\n";
echo "   Host: {$config['host']}:{$config['port']}\n";
echo "   Database: {$config['database']}\n";
echo "   Username: {$config['username']}\n";
echo "   Charset: {$config['charset']}\n\n";

// Test 1: Server Connection
echo "🔄 Step 1: Testing MySQL Server Connection...\n";
echo str_repeat("-", 40) . "\n";

try {
    $dsn = "mysql:host={$config['host']};port={$config['port']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
    ]);
    
    $version = $pdo->query("SELECT VERSION()")->fetchColumn();
    echo "✅ MySQL Server: CONNECTED\n";
    echo "   Version: $version\n";
    
    // Check if database exists
    $databases = $pdo->query("SHOW DATABASES LIKE '{$config['database']}'")->fetchAll();
    if (empty($databases)) {
        echo "⚠️  Database '{$config['database']}' does not exist\n";
        echo "🔧 Creating database...\n";
        
        $pdo->exec("CREATE DATABASE IF NOT EXISTS {$config['database']} CHARACTER SET {$config['charset']} COLLATE {$config['charset']}_unicode_ci");
        echo "✅ Database '{$config['database']}' created successfully\n";
    } else {
        echo "✅ Database '{$config['database']}' exists\n";
    }
    
} catch (PDOException $e) {
    echo "❌ MySQL Server: CONNECTION FAILED\n";
    echo "   Error: " . $e->getMessage() . "\n\n";
    
    echo "🔧 Troubleshooting Steps:\n";
    echo "1. Make sure XAMPP is installed and running\n";
    echo "2. Start MySQL service in XAMPP Control Panel\n";
    echo "3. Check if MySQL is running on port 3306\n";
    echo "4. Verify MySQL credentials\n\n";
    
    echo "💡 Quick Fix Commands:\n";
    echo "   • Open XAMPP Control Panel\n";
    echo "   • Click 'Start' next to MySQL\n";
    echo "   • Wait for green 'Running' status\n";
    
    exit(1);
}

echo "\n";

// Test 2: Database Connection
echo "🔄 Step 2: Testing Database Connection...\n";
echo str_repeat("-", 40) . "\n";

try {
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
    ]);
    
    echo "✅ Database Connection: SUCCESS\n";
    
    // Check tables
    $tables = $pdo->query("SHOW TABLES")->fetchAll();
    echo "   Tables found: " . count($tables) . "\n";
    
    if (empty($tables)) {
        echo "⚠️  No tables found in database\n";
        echo "💡 Run 'php setup_production_database.php' to create tables\n";
    } else {
        echo "✅ Database has tables\n";
        
        // List tables
        echo "\n📋 Available Tables:\n";
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            try {
                $count = $pdo->query("SELECT COUNT(*) FROM `$tableName`")->fetchColumn();
                echo "   • $tableName: " . number_format($count) . " records\n";
            } catch (Exception $e) {
                echo "   • $tableName: Error reading\n";
            }
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Database Connection: FAILED\n";
    echo "   Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n";

// Test 3: Performance Test
echo "🔄 Step 3: Performance Test...\n";
echo str_repeat("-", 40) . "\n";

$start = microtime(true);
for ($i = 0; $i < 10; $i++) {
    $pdo->query("SELECT 1")->fetchColumn();
}
$end = microtime(true);
$duration = round(($end - $start) * 1000, 2);

echo "✅ Performance Test: PASSED\n";
echo "   10 queries executed in {$duration}ms\n";
echo "   Average: " . round($duration / 10, 2) . "ms per query\n";

// Test 4: Transaction Test
echo "\n🔄 Step 4: Transaction Test...\n";
echo str_repeat("-", 40) . "\n";

try {
    $pdo->beginTransaction();
    
    // Create a test table
    $pdo->exec("CREATE TEMPORARY TABLE test_table (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50))");
    $pdo->exec("INSERT INTO test_table (name) VALUES ('Test Record')");
    
    $count = $pdo->query("SELECT COUNT(*) FROM test_table")->fetchColumn();
    
    $pdo->rollback();
    
    echo "✅ Transaction Test: PASSED\n";
    echo "   Created temporary table and inserted $count record\n";
    echo "   Transaction rolled back successfully\n";
    
} catch (Exception $e) {
    $pdo->rollback();
    echo "❌ Transaction Test: FAILED\n";
    echo "   Error: " . $e->getMessage() . "\n";
}

// Test 5: Character Set Test
echo "\n🔄 Step 5: Character Set Test...\n";
echo str_repeat("-", 40) . "\n";

try {
    $charset = $pdo->query("SELECT @@character_set_database")->fetchColumn();
    $collation = $pdo->query("SELECT @@collation_database")->fetchColumn();
    
    echo "✅ Character Set Test: PASSED\n";
    echo "   Database Charset: $charset\n";
    echo "   Database Collation: $collation\n";
    
    // Test Arabic text
    $pdo->exec("CREATE TEMPORARY TABLE test_arabic (id INT AUTO_INCREMENT PRIMARY KEY, text VARCHAR(100) CHARACTER SET utf8mb4)");
    $pdo->exec("INSERT INTO test_arabic (text) VALUES ('مرحبا بكم في نظام التحويلات المتقدم')");
    
    $arabicText = $pdo->query("SELECT text FROM test_arabic LIMIT 1")->fetchColumn();
    echo "   Arabic Text Test: $arabicText\n";
    
} catch (Exception $e) {
    echo "❌ Character Set Test: FAILED\n";
    echo "   Error: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 MySQL Connection Test Completed!\n\n";

echo "📝 Test Summary:\n";
echo "   ✅ MySQL Server: Connected\n";
echo "   ✅ Database: Available\n";
echo "   ✅ Performance: Good\n";
echo "   ✅ Transactions: Working\n";
echo "   ✅ Character Set: UTF8MB4\n\n";

echo "🔧 Next Steps:\n";
echo "1. Update .env file to use MySQL:\n";
echo "   DB_CONNECTION=mysql\n";
echo "   DB_HOST=localhost\n";
echo "   DB_PORT=3306\n";
echo "   DB_DATABASE=elite_transfer\n";
echo "   DB_USERNAME=root\n";
echo "   DB_PASSWORD=\n\n";

echo "2. Run database setup:\n";
echo "   php setup_production_database.php\n\n";

echo "3. Test the connection:\n";
echo "   php quick_db_test.php\n\n";

echo "💡 MySQL is ready for production use!\n";
echo "🚀 Better performance and scalability than SQLite\n";

?>
