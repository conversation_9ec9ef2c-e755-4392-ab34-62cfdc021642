<?php

/**
 * Database Status Dashboard
 * Elite Transfer System - Web Interface for Database Connection Status
 */

// Load environment
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

$dbType = $_ENV['DB_CONNECTION'] ?? 'sqlite';
$isConnected = false;
$connectionInfo = [];
$error = null;
$stats = [];

try {
    if ($dbType === 'sqlite') {
        $database = $_ENV['DB_DATABASE'] ?? 'database/elite_transfer_production.db';
        $fullPath = __DIR__ . '/../' . $database;
        
        if (!file_exists($fullPath)) {
            throw new Exception("SQLite database file not found: $database");
        }
        
        $pdo = new PDO("sqlite:$fullPath", null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        $version = $pdo->query("SELECT sqlite_version()")->fetchColumn();
        $connectionInfo = [
            'type' => 'SQLite',
            'version' => $version,
            'database' => $database,
            'file_size' => number_format(filesize($fullPath)) . ' bytes',
            'status' => 'Connected'
        ];
        
    } else {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? 'elite_transfer';
        $username = $_ENV['DB_USERNAME'] ?? 'root';
        $password = $_ENV['DB_PASSWORD'] ?? '';
        
        $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        $version = $pdo->query("SELECT VERSION()")->fetchColumn();
        $connectionInfo = [
            'type' => 'MySQL',
            'version' => $version,
            'host' => "$host:$port",
            'database' => $database,
            'username' => $username,
            'status' => 'Connected'
        ];
    }
    
    $isConnected = true;
    
    // Get statistics
    $stats = [
        'users' => $pdo->query("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL")->fetchColumn(),
        'active_users' => $pdo->query("SELECT COUNT(*) FROM users WHERE status = 'active' AND deleted_at IS NULL")->fetchColumn(),
        'countries' => $pdo->query("SELECT COUNT(*) FROM countries WHERE is_active = 1")->fetchColumn(),
        'transfers' => $pdo->query("SELECT COUNT(*) FROM transfers WHERE deleted_at IS NULL")->fetchColumn(),
        'exchange_rates' => $pdo->query("SELECT COUNT(*) FROM exchange_rates")->fetchColumn()
    ];
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $connectionInfo['status'] = 'Disconnected';
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة قاعدة البيانات - Elite Transfer System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .status-card {
            background: <?php echo $isConnected ? '#d4edda' : '#f8d7da'; ?>;
            border: 1px solid <?php echo $isConnected ? '#c3e6cb' : '#f5c6cb'; ?>;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .status-icon {
            font-size: 4em;
            margin-bottom: 15px;
        }
        
        .status-text {
            font-size: 1.5em;
            font-weight: bold;
            color: <?php echo $isConnected ? '#155724' : '#721c24'; ?>;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }
        
        .info-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #007bff;
            font-weight: 500;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .error-card {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .error-text {
            color: #721c24;
            font-weight: bold;
        }
        
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            margin: 5px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .refresh-info {
            text-align: center;
            margin-top: 20px;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 حالة قاعدة البيانات</h1>
            <p>Elite Transfer System - Database Status Dashboard</p>
        </div>
        
        <div class="content">
            <div class="status-card">
                <div class="status-icon">
                    <?php echo $isConnected ? '✅' : '❌'; ?>
                </div>
                <div class="status-text">
                    <?php echo $isConnected ? 'متصل بنجاح' : 'فشل في الاتصال'; ?>
                </div>
            </div>
            
            <?php if ($error): ?>
            <div class="error-card">
                <div class="error-text">خطأ في الاتصال:</div>
                <div><?php echo htmlspecialchars($error); ?></div>
            </div>
            <?php endif; ?>
            
            <?php if ($isConnected): ?>
            <div class="info-grid">
                <div class="info-card">
                    <h3>📊 معلومات الاتصال</h3>
                    <?php foreach ($connectionInfo as $key => $value): ?>
                    <div class="info-item">
                        <span class="info-label"><?php echo ucfirst($key); ?>:</span>
                        <span class="info-value"><?php echo htmlspecialchars($value); ?></span>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="info-card">
                    <h3>🔧 إعدادات البيئة</h3>
                    <div class="info-item">
                        <span class="info-label">البيئة:</span>
                        <span class="info-value"><?php echo $_ENV['APP_ENV'] ?? 'غير محدد'; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">وضع التطوير:</span>
                        <span class="info-value"><?php echo $_ENV['APP_DEBUG'] ?? 'false'; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">نوع قاعدة البيانات:</span>
                        <span class="info-value"><?php echo strtoupper($dbType); ?></span>
                    </div>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['users']); ?></div>
                    <div class="stat-label">إجمالي المستخدمين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['active_users']); ?></div>
                    <div class="stat-label">المستخدمون النشطون</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['countries']); ?></div>
                    <div class="stat-label">الدول المتاحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['transfers']); ?></div>
                    <div class="stat-label">التحويلات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['exchange_rates']); ?></div>
                    <div class="stat-label">أسعار الصرف</div>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="actions">
                <a href="dashboard.php" class="btn btn-success">🏠 العودة للوحة التحكم</a>
                <a href="?refresh=1" class="btn">🔄 تحديث الحالة</a>
                <a href="../quick_db_test.php" class="btn">🧪 اختبار سريع</a>
            </div>
            
            <div class="refresh-info">
                آخر تحديث: <?php echo date('Y-m-d H:i:s'); ?>
            </div>
        </div>
    </div>
</body>
</html>
