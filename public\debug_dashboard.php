<?php

/**
 * Debug Dashboard
 * Elite Transfer System - Debug Dashboard Issues
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';

$db = DatabaseManager::getInstance();

echo "<h2>🔍 فحص لوحة التحكم</h2>";

try {
    // Check tables existence
    echo "<h3>1. فحص الجداول:</h3>";
    
    $tables = ['users', 'transfers', 'countries'];
    foreach ($tables as $table) {
        $result = $db->select("SHOW TABLES LIKE '$table'");
        if (!empty($result)) {
            $count = $db->selectOne("SELECT COUNT(*) as count FROM $table")['count'];
            echo "<p style='color: green;'>✅ جدول $table موجود ($count سجل)</p>";
        } else {
            echo "<p style='color: red;'>❌ جدول $table غير موجود!</p>";
        }
    }
    
    // Test getStatistics function
    echo "<h3>2. اختبار دالة getStatistics:</h3>";
    
    try {
        $stats = $db->getStatistics();
        echo "<p style='color: green;'>✅ دالة getStatistics تعمل بشكل صحيح</p>";
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>الإحصائية</th><th>القيمة</th></tr>";
        foreach ($stats as $key => $value) {
            echo "<tr><td>$key</td><td><strong>$value</strong></td></tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في getStatistics: " . $e->getMessage() . "</p>";
    }
    
    // Test dashboard AJAX endpoint
    echo "<h3>3. اختبار AJAX endpoint:</h3>";
    
    // Simulate POST request
    $_POST['action'] = 'get_dashboard_data';
    
    ob_start();
    
    try {
        // Get comprehensive statistics
        try {
            $stats = $db->getStatistics();
        } catch (Exception $e) {
            $stats = [
                'total_transfers' => 0,
                'completed_transfers' => 0,
                'pending_transfers' => 0,
                'total_amount' => 0,
                'today_transfers' => 0,
                'today_amount' => 0
            ];
        }
        
        try {
            $recentTransfers = $db->getTransfers([], 10, 0);
        } catch (Exception $e) {
            $recentTransfers = [];
        }
        
        try {
            $recentUsers = $db->getUsers([], 5, 0);
        } catch (Exception $e) {
            $recentUsers = [];
        }
        
        // Get detailed counts with error handling
        try {
            $pendingCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'pending' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
            $processingCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'processing' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
            $completedToday = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'completed' AND DATE(created_at) = CURDATE() AND (deleted_at IS NULL OR deleted_at = '')")['count'];
            $failedCount = $db->selectOne("SELECT COUNT(*) as count FROM transfers WHERE status = 'failed' AND (deleted_at IS NULL OR deleted_at = '')")['count'];
        } catch (Exception $e) {
            $pendingCount = 0;
            $processingCount = 0;
            $completedToday = 0;
            $failedCount = 0;
        }
        
        // Get revenue statistics with error handling
        try {
            $todayRevenue = $db->selectOne("SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
            $monthRevenue = $db->selectOne("SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE()) AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
            $yearRevenue = $db->selectOne("SELECT COALESCE(SUM(fees), 0) as revenue FROM transfers WHERE YEAR(created_at) = YEAR(CURDATE()) AND status = 'completed' AND (deleted_at IS NULL OR deleted_at = '')")['revenue'];
        } catch (Exception $e) {
            $todayRevenue = 0;
            $monthRevenue = 0;
            $yearRevenue = 0;
        }
        
        // Get user statistics with error handling
        try {
            $totalUsers = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE (deleted_at IS NULL OR deleted_at = '')")['count'];
            $activeUsers = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1 AND (deleted_at IS NULL OR deleted_at = '')")['count'];
            $newUsersToday = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE() AND (deleted_at IS NULL OR deleted_at = '')")['count'];
        } catch (Exception $e) {
            $totalUsers = 1; // At least admin user
            $activeUsers = 1;
            $newUsersToday = 0;
        }
        
        // Get chart data for last 7 days (simplified)
        $chartData = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            try {
                $dayData = $db->selectOne("
                    SELECT 
                        COUNT(*) as total,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
                    FROM transfers 
                    WHERE DATE(created_at) = :date
                    AND (deleted_at IS NULL OR deleted_at = '')
                ", ['date' => $date]);
            } catch (Exception $e) {
                $dayData = ['total' => 0, 'completed' => 0, 'pending' => 0, 'failed' => 0];
            }
            
            $chartData[] = [
                'date' => $date,
                'total' => intval($dayData['total']),
                'completed' => intval($dayData['completed']),
                'pending' => intval($dayData['pending']),
                'failed' => intval($dayData['failed'])
            ];
        }
        
        // Get top countries
        $topCountries = [];
        try {
            $topCountries = $db->select("
                SELECT 
                    COALESCE(c.name, 'غير محدد') as country_name,
                    COUNT(t.id) as transfer_count,
                    COALESCE(SUM(t.total_amount), 0) as total_amount
                FROM transfers t
                LEFT JOIN countries c ON t.sender_country_id = c.id
                WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                AND (t.deleted_at IS NULL OR t.deleted_at = '')
                GROUP BY c.id, c.name
                ORDER BY transfer_count DESC
                LIMIT 5
            ");
        } catch (Exception $e) {
            $topCountries = [];
        }
        
        $response = [
            'success' => true,
            'stats' => $stats,
            'recent_transfers' => $recentTransfers,
            'recent_users' => $recentUsers,
            'counts' => [
                'pending' => intval($pendingCount),
                'processing' => intval($processingCount),
                'completed_today' => intval($completedToday),
                'failed' => intval($failedCount),
                'total_users' => intval($totalUsers),
                'active_users' => intval($activeUsers),
                'new_users_today' => intval($newUsersToday)
            ],
            'revenue' => [
                'today' => floatval($todayRevenue),
                'month' => floatval($monthRevenue),
                'year' => floatval($yearRevenue)
            ],
            'chart_data' => $chartData,
            'top_countries' => $topCountries
        ];
        
        echo "<p style='color: green;'>✅ AJAX endpoint يعمل بشكل صحيح!</p>";
        echo "<h4>استجابة JSON:</h4>";
        echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        echo "</pre>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في AJAX endpoint: " . $e->getMessage() . "</p>";
    }
    
    $output = ob_get_clean();
    echo $output;
    
    // Reset POST
    unset($_POST['action']);
    
    // Test JavaScript loading
    echo "<h3>4. اختبار تحميل JavaScript:</h3>";
    echo "<button onclick='testDashboardAjax()'>اختبار تحميل البيانات</button>";
    echo "<div id='testResult'></div>";
    
    echo "<h3>5. الروابط للاختبار:</h3>";
    echo "<p><a href='dashboard_advanced.php' target='_blank'>لوحة التحكم</a></p>";
    echo "<p><a href='create_transfers_table.php' target='_blank'>إنشاء جدول التحويلات</a></p>";
    echo "<p><a href='create_countries_simple.php' target='_blank'>إنشاء جدول البلدان</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص لوحة التحكم - <?= SYSTEM_NAME ?></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        h2, h3 {
            color: #333;
        }
        
        table {
            background: white;
            margin: 20px 0;
            width: 100%;
            border-collapse: collapse;
        }
        
        th {
            background: #667eea;
            color: white;
            padding: 10px;
        }
        
        td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        
        button {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        a {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
            max-height: 400px;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
</head>
<body>

<script>
function testDashboardAjax() {
    $('#testResult').html('<p>جاري الاختبار...</p>');
    
    $.ajax({
        url: 'dashboard_advanced.php',
        method: 'POST',
        data: { action: 'get_dashboard_data' },
        dataType: 'json',
        success: function(response) {
            if (response && response.success) {
                $('#testResult').html(`
                    <p style='color: green;'>✅ تحميل البيانات نجح!</p>
                    <p>إجمالي التحويلات: ${response.stats.total_transfers}</p>
                    <p>التحويلات المكتملة: ${response.stats.completed_transfers || 0}</p>
                    <p>التحويلات المعلقة: ${response.counts.pending}</p>
                    <p>إجمالي المستخدمين: ${response.counts.total_users}</p>
                `);
            } else {
                $('#testResult').html(`
                    <p style='color: red;'>❌ فشل في تحميل البيانات: ${response.message || 'خطأ غير معروف'}</p>
                `);
            }
        },
        error: function(xhr, status, error) {
            $('#testResult').html(`
                <p style='color: red;'>❌ خطأ في AJAX:</p>
                <p>Status: ${status}</p>
                <p>Error: ${error}</p>
                <p>Response: ${xhr.responseText.substring(0, 500)}...</p>
            `);
        }
    });
}
</script>

</body>
</html>
