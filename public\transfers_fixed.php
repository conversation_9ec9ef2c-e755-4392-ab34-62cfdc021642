<?php

/**
 * Transfers Management - Fixed Version
 * Elite Transfer System - Completely fixed transfers management page
 */

// Disable error output to prevent JSON corruption
error_reporting(0);
ini_set('display_errors', 0);

// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Auto login if not logged in
if (!isLoggedIn()) {
    require_once __DIR__ . '/includes/database_manager.php';
    try {
        $dbManager = DatabaseManager::getInstance();
        $db = $dbManager->getConnection();
        
        $stmt = $db->prepare("SELECT * FROM users WHERE role = 'admin' AND status = 'active' AND deleted_at IS NULL LIMIT 1");
        $stmt->execute();
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            $_SESSION['user_id'] = $admin['id'];
            $_SESSION['name'] = $admin['name'];
            $_SESSION['email'] = $admin['email'];
            $_SESSION['role'] = $admin['role'];
        }
    } catch (Exception $e) {
        // Continue without auto-login
    }
}

// Load database manager
require_once __DIR__ . '/includes/database_manager.php';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    // Clean output buffer and set JSON header
    while (ob_get_level()) {
        ob_end_clean();
    }
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $dbManager = DatabaseManager::getInstance();
        $db = $dbManager->getConnection();
        
        switch ($_POST['action']) {
            case 'get_transfers':
                $page = max(1, intval($_POST['page'] ?? 1));
                $limit = max(1, min(100, intval($_POST['limit'] ?? 10)));
                $search = trim($_POST['search'] ?? '');
                $status_filter = trim($_POST['status_filter'] ?? '');
                $date_from = trim($_POST['date_from'] ?? '');
                $date_to = trim($_POST['date_to'] ?? '');
                
                $offset = ($page - 1) * $limit;
                
                // Build WHERE clause safely
                $where = ["t.deleted_at IS NULL"];
                $params = [];
                
                if (!empty($search)) {
                    $where[] = "(t.transfer_code LIKE ? OR t.pickup_code LIKE ? OR t.sender_name LIKE ? OR t.recipient_name LIKE ?)";
                    $searchParam = "%$search%";
                    $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam]);
                }
                
                if (!empty($status_filter)) {
                    $where[] = "t.status = ?";
                    $params[] = $status_filter;
                }
                
                if (!empty($date_from)) {
                    $where[] = "DATE(t.created_at) >= ?";
                    $params[] = $date_from;
                }
                
                if (!empty($date_to)) {
                    $where[] = "DATE(t.created_at) <= ?";
                    $params[] = $date_to;
                }
                
                $whereClause = implode(' AND ', $where);
                
                // Get total count
                $countQuery = "SELECT COUNT(*) FROM transfers t WHERE $whereClause";
                $stmt = $db->prepare($countQuery);
                $stmt->execute($params);
                $total = intval($stmt->fetchColumn());
                
                // Get transfers with safe query
                $query = "
                    SELECT t.id, t.transfer_code, t.pickup_code, t.sender_name, t.sender_phone,
                           t.recipient_name, t.recipient_phone, t.amount, t.fee, t.total_amount,
                           t.status, t.payment_method, t.purpose, t.notes, t.created_at, t.updated_at,
                           COALESCE(u.name, 'غير محدد') as user_name,
                           COALESCE(sc.name, 'غير محدد') as sender_country,
                           COALESCE(rc.name, 'غير محدد') as recipient_country
                    FROM transfers t
                    LEFT JOIN users u ON t.user_id = u.id
                    LEFT JOIN countries sc ON t.sender_country_id = sc.id
                    LEFT JOIN countries rc ON t.recipient_country_id = rc.id
                    WHERE $whereClause 
                    ORDER BY t.created_at DESC 
                    LIMIT ? OFFSET ?
                ";
                
                $stmt = $db->prepare($query);
                $stmt->execute(array_merge($params, [$limit, $offset]));
                $transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Clean and format data
                foreach ($transfers as &$transfer) {
                    $transfer['amount'] = floatval($transfer['amount']);
                    $transfer['fee'] = floatval($transfer['fee'] ?? 0);
                    $transfer['total_amount'] = floatval($transfer['total_amount'] ?? $transfer['amount']);
                }
                
                echo json_encode([
                    'success' => true,
                    'transfers' => $transfers,
                    'total' => $total,
                    'page' => $page,
                    'pages' => max(1, ceil($total / $limit)),
                    'limit' => $limit
                ], JSON_UNESCAPED_UNICODE);
                exit;
                
            case 'get_transfer':
                $id = intval($_POST['id'] ?? 0);
                if ($id <= 0) {
                    echo json_encode(['success' => false, 'message' => 'معرف التحويل غير صحيح'], JSON_UNESCAPED_UNICODE);
                    exit;
                }
                
                $stmt = $db->prepare("
                    SELECT t.*, 
                           COALESCE(u.name, 'غير محدد') as user_name, 
                           COALESCE(u.email, 'غير محدد') as user_email,
                           COALESCE(sc.name, 'غير محدد') as sender_country,
                           COALESCE(rc.name, 'غير محدد') as recipient_country
                    FROM transfers t
                    LEFT JOIN users u ON t.user_id = u.id
                    LEFT JOIN countries sc ON t.sender_country_id = sc.id
                    LEFT JOIN countries rc ON t.recipient_country_id = rc.id
                    WHERE t.id = ? AND t.deleted_at IS NULL
                ");
                $stmt->execute([$id]);
                $transfer = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($transfer) {
                    // Format numeric values
                    $transfer['amount'] = floatval($transfer['amount']);
                    $transfer['fee'] = floatval($transfer['fee'] ?? 0);
                    $transfer['total_amount'] = floatval($transfer['total_amount'] ?? $transfer['amount']);
                    $transfer['exchange_rate'] = floatval($transfer['exchange_rate'] ?? 1);
                    
                    echo json_encode(['success' => true, 'transfer' => $transfer], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode(['success' => false, 'message' => 'التحويل غير موجود'], JSON_UNESCAPED_UNICODE);
                }
                exit;
                
            case 'update_transfer_status':
                $id = intval($_POST['id'] ?? 0);
                $status = trim($_POST['status'] ?? '');
                $notes = trim($_POST['notes'] ?? '');
                
                if ($id <= 0) {
                    echo json_encode(['success' => false, 'message' => 'معرف التحويل غير صحيح'], JSON_UNESCAPED_UNICODE);
                    exit;
                }
                
                $validStatuses = ['pending', 'processing', 'completed', 'cancelled', 'failed'];
                if (!in_array($status, $validStatuses)) {
                    echo json_encode(['success' => false, 'message' => 'حالة غير صحيحة'], JSON_UNESCAPED_UNICODE);
                    exit;
                }
                
                $stmt = $db->prepare("
                    UPDATE transfers 
                    SET status = ?, updated_at = NOW()
                    WHERE id = ? AND deleted_at IS NULL
                ");
                
                $result = $stmt->execute([$status, $id]);
                
                if ($result && $stmt->rowCount() > 0) {
                    echo json_encode(['success' => true, 'message' => 'تم تحديث حالة التحويل بنجاح'], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في تحديث حالة التحويل أو التحويل غير موجود'], JSON_UNESCAPED_UNICODE);
                }
                exit;
                
            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير معروف'], JSON_UNESCAPED_UNICODE);
                exit;
        }
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false, 
            'message' => 'خطأ في الخادم: ' . $e->getMessage(),
            'error_details' => [
                'file' => basename($e->getFile()),
                'line' => $e->getLine()
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Get statistics for display
try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    $stats = [
        'total_transfers' => intval($db->query("SELECT COUNT(*) FROM transfers WHERE deleted_at IS NULL")->fetchColumn()),
        'pending_transfers' => intval($db->query("SELECT COUNT(*) FROM transfers WHERE status = 'pending' AND deleted_at IS NULL")->fetchColumn()),
        'completed_transfers' => intval($db->query("SELECT COUNT(*) FROM transfers WHERE status = 'completed' AND deleted_at IS NULL")->fetchColumn()),
        'total_amount' => floatval($db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE status = 'completed' AND deleted_at IS NULL")->fetchColumn()),
        'today_transfers' => intval($db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = CURDATE() AND deleted_at IS NULL")->fetchColumn()),
        'today_amount' => floatval($db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed' AND deleted_at IS NULL")->fetchColumn())
    ];
} catch (Exception $e) {
    $stats = [
        'total_transfers' => 0,
        'pending_transfers' => 0,
        'completed_transfers' => 0,
        'total_amount' => 0,
        'today_transfers' => 0,
        'today_amount' => 0
    ];
}

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التحويلات (مُصلحة نهائياً) - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
        }
        
        .stats-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stats-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-pending { background: #fff3cd; color: #664d03; }
        .status-processing { background: #cff4fc; color: #055160; }
        .status-completed { background: #d1e7dd; color: #0f5132; }
        .status-cancelled { background: #f8d7da; color: #842029; }
        .status-failed { background: #f8d7da; color: #842029; }
        
        .transfer-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #0066cc;
        }
        
        .amount-display {
            font-weight: bold;
            color: #28a745;
        }
        
        .btn-action {
            margin: 2px;
            padding: 5px 10px;
            font-size: 0.8em;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box .bi-search {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        
        .search-box input {
            padding-left: 40px;
        }
        
        .alert-fixed {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-1">
                        <i class="bi bi-check-circle me-2"></i>
                        إدارة التحويلات (مُصلحة نهائياً)
                    </h1>
                    <p class="mb-0 opacity-75">نسخة مُصلحة بالكامل مع معالجة شاملة للأخطاء</p>
                    <small class="opacity-50">مرحباً <?= htmlspecialchars($userName) ?></small>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light me-2" onclick="loadTransfers()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        تحديث
                    </button>
                    <a href="debug_ajax.php" class="btn btn-info me-2">
                        <i class="bi bi-bug me-1"></i>
                        تشخيص AJAX
                    </a>
                    <a href="admin_transfers_enhanced.php" class="btn btn-warning">
                        <i class="bi bi-arrow-left me-1"></i>
                        الصفحة الأصلية
                    </a>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mt-4">
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['total_transfers']) ?></div>
                        <div class="stats-label">إجمالي التحويلات</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['pending_transfers']) ?></div>
                        <div class="stats-label">في الانتظار</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['completed_transfers']) ?></div>
                        <div class="stats-label">مكتملة</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number">$<?= number_format($stats['total_amount'], 0) ?></div>
                        <div class="stats-label">إجمالي المبلغ</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['today_transfers']) ?></div>
                        <div class="stats-label">تحويلات اليوم</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card">
                        <div class="stats-number">$<?= number_format($stats['today_amount'], 0) ?></div>
                        <div class="stats-label">مبلغ اليوم</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Filters -->
            <div class="filter-section">
                <div class="row">
                    <div class="col-md-3">
                        <div class="search-box">
                            <i class="bi bi-search"></i>
                            <input type="text" class="form-control" id="searchInput" placeholder="البحث برمز التحويل أو الاسم...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="pending">في الانتظار</option>
                            <option value="processing">قيد المعالجة</option>
                            <option value="completed">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                            <option value="failed">فاشل</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="dateFrom" placeholder="من تاريخ">
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="dateTo" placeholder="إلى تاريخ">
                    </div>
                    <div class="col-md-1">
                        <select class="form-select" id="limitSelect">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary w-100" onclick="exportTransfers()">
                            <i class="bi bi-download me-1"></i>
                            تصدير
                        </button>
                    </div>
                </div>
            </div>

            <!-- Transfers Table -->
            <div class="table-container position-relative">
                <div id="loadingOverlay" class="loading-overlay d-none">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل البيانات...</p>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>رمز التحويل</th>
                                <th>المرسل</th>
                                <th>المستلم</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>طريقة الدفع</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transfersTableBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <p class="mt-2">جاري تحميل التحويلات...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center p-3">
                    <div id="tableInfo" class="text-muted"></div>
                    <nav>
                        <ul class="pagination mb-0" id="pagination"></ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Details Modal -->
    <div class="modal fade" id="transferDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-info-circle me-2"></i>
                        تفاصيل التحويل
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="transferDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="printTransfer()">
                        <i class="bi bi-printer me-1"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Update Modal -->
    <div class="modal fade" id="statusUpdateModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-arrow-repeat me-2"></i>
                        تحديث حالة التحويل
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="statusUpdateForm">
                        <input type="hidden" id="transferId" name="id">

                        <div class="form-floating mb-3">
                            <select class="form-select" id="newStatus" name="status" required>
                                <option value="pending">في الانتظار</option>
                                <option value="processing">قيد المعالجة</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                                <option value="failed">فاشل</option>
                            </select>
                            <label for="newStatus">الحالة الجديدة</label>
                        </div>

                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="statusNotes" name="notes" style="height: 100px"></textarea>
                            <label for="statusNotes">ملاحظات (اختياري)</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="updateTransferStatus()">
                        <i class="bi bi-check-lg me-1"></i>
                        تحديث الحالة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        let currentPage = 1;
        let currentLimit = 10;
        let isLoading = false;

        $(document).ready(function() {
            console.log('🚀 Transfers Fixed Page Loaded');
            loadTransfers();

            // Event listeners with debouncing
            $('#searchInput').on('input', debounce(function() {
                currentPage = 1;
                loadTransfers();
            }, 500));

            $('#statusFilter').on('change', function() {
                currentPage = 1;
                loadTransfers();
            });

            $('#dateFrom, #dateTo').on('change', function() {
                currentPage = 1;
                loadTransfers();
            });

            $('#limitSelect').on('change', function() {
                currentLimit = parseInt($(this).val());
                currentPage = 1;
                loadTransfers();
            });
        });

        function loadTransfers() {
            if (isLoading) {
                console.log('⏳ Already loading, skipping...');
                return;
            }

            isLoading = true;
            showLoading(true);

            const requestData = {
                action: 'get_transfers',
                page: currentPage,
                limit: currentLimit,
                search: $('#searchInput').val().trim(),
                status_filter: $('#statusFilter').val(),
                date_from: $('#dateFrom').val(),
                date_to: $('#dateTo').val()
            };

            console.log('📤 Sending request:', requestData);

            $.ajax({
                url: '',
                method: 'POST',
                data: requestData,
                dataType: 'json',
                timeout: 30000, // 30 seconds timeout
                success: function(response) {
                    console.log('📥 Response received:', response);

                    if (response && response.success) {
                        displayTransfers(response.transfers || []);
                        updatePagination(response.page || 1, response.pages || 1, response.total || 0);
                        showSuccess('تم تحميل البيانات بنجاح');
                    } else {
                        const errorMsg = response ? response.message : 'استجابة غير صحيحة من الخادم';
                        showError('خطأ في تحميل البيانات: ' + errorMsg);
                        displayError('فشل في تحميل البيانات: ' + errorMsg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ AJAX Error Details:');
                    console.error('Status:', status);
                    console.error('Error:', error);
                    console.error('Response Status:', xhr.status);
                    console.error('Response Text:', xhr.responseText);

                    let errorMessage = 'خطأ في الاتصال بالخادم';

                    if (xhr.status === 0) {
                        errorMessage = 'لا يمكن الوصول للخادم - تحقق من الاتصال';
                    } else if (xhr.status === 404) {
                        errorMessage = 'الصفحة غير موجودة (404)';
                    } else if (xhr.status === 500) {
                        errorMessage = 'خطأ داخلي في الخادم (500)';
                    } else if (status === 'timeout') {
                        errorMessage = 'انتهت مهلة الاتصال - حاول مرة أخرى';
                    } else if (status === 'parsererror') {
                        errorMessage = 'خطأ في تحليل البيانات - الخادم لم يرسل JSON صحيح';

                        // Try to show first part of response for debugging
                        if (xhr.responseText) {
                            const preview = xhr.responseText.substring(0, 200);
                            console.error('Response preview:', preview);
                            errorMessage += '\\n\\nمعاينة الاستجابة: ' + preview;
                        }
                    }

                    showError(errorMessage);
                    displayError(errorMessage, xhr.responseText);
                },
                complete: function() {
                    isLoading = false;
                    showLoading(false);
                }
            });
        }

        function displayTransfers(transfers) {
            const tbody = $('#transfersTableBody');
            tbody.empty();

            if (!transfers || transfers.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد تحويلات</p>
                            <button class="btn btn-outline-primary btn-sm" onclick="loadTransfers()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                إعادة المحاولة
                            </button>
                        </td>
                    </tr>
                `);
                return;
            }

            transfers.forEach(transfer => {
                const row = `
                    <tr>
                        <td>
                            <span class="transfer-code">${escapeHtml(transfer.transfer_code || 'غير محدد')}</span>
                            <br>
                            <small class="text-muted">${escapeHtml(transfer.pickup_code || '')}</small>
                        </td>
                        <td>
                            <div>
                                <strong>${escapeHtml(transfer.sender_name || 'غير محدد')}</strong>
                                <br>
                                <small class="text-muted">${escapeHtml(transfer.sender_country || '')}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>${escapeHtml(transfer.recipient_name || 'غير محدد')}</strong>
                                <br>
                                <small class="text-muted">${escapeHtml(transfer.recipient_country || '')}</small>
                            </div>
                        </td>
                        <td>
                            <span class="amount-display">$${formatNumber(transfer.amount || 0)}</span>
                            <br>
                            <small class="text-muted">رسوم: $${formatNumber(transfer.fee || 0)}</small>
                        </td>
                        <td>
                            <span class="status-badge status-${transfer.status || 'pending'}">
                                ${getStatusText(transfer.status || 'pending')}
                            </span>
                        </td>
                        <td>${getPaymentMethodText(transfer.payment_method)}</td>
                        <td>
                            <div>
                                ${formatDate(transfer.created_at)}
                                <br>
                                <small class="text-muted">${formatTime(transfer.created_at)}</small>
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-info btn-action" onclick="viewTransfer(${transfer.id})" title="عرض التفاصيل">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-warning btn-action" onclick="updateStatus(${transfer.id}, '${transfer.status || 'pending'}')" title="تحديث الحالة">
                                <i class="bi bi-arrow-repeat"></i>
                            </button>
                            <button class="btn btn-sm btn-success btn-action" onclick="printTransfer(${transfer.id})" title="طباعة">
                                <i class="bi bi-printer"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        function displayError(message, details = null) {
            const tbody = $('#transfersTableBody');
            tbody.html(`
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>خطأ في تحميل البيانات</strong>
                            <p class="mt-2">${escapeHtml(message)}</p>
                            <div class="mt-3">
                                <button class="btn btn-outline-danger btn-sm me-2" onclick="loadTransfers()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    إعادة المحاولة
                                </button>
                                <a href="debug_ajax.php" class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-bug me-1"></i>
                                    تشخيص المشكلة
                                </a>
                            </div>
                            ${details ? `<details class="mt-3"><summary>تفاصيل تقنية</summary><pre class="text-start mt-2">${escapeHtml(details.substring(0, 500))}</pre></details>` : ''}
                        </div>
                    </td>
                </tr>
            `);
        }

        function updatePagination(page, pages, total) {
            const pagination = $('#pagination');
            pagination.empty();

            $('#tableInfo').text(`عرض ${total} تحويل - الصفحة ${page} من ${pages}`);

            if (pages <= 1) return;

            // Previous button
            if (page > 1) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage(${page - 1})">السابق</a>
                    </li>
                `);
            }

            // Page numbers
            const startPage = Math.max(1, page - 2);
            const endPage = Math.min(pages, page + 2);

            for (let i = startPage; i <= endPage; i++) {
                pagination.append(`
                    <li class="page-item ${i === page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `);
            }

            // Next button
            if (page < pages) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage(${page + 1})">التالي</a>
                    </li>
                `);
            }
        }

        function changePage(page) {
            if (isLoading) return;
            currentPage = page;
            loadTransfers();
        }

        function viewTransfer(id) {
            if (isLoading) return;

            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_transfer',
                    id: id
                },
                dataType: 'json',
                success: function(response) {
                    if (response && response.success && response.transfer) {
                        displayTransferDetails(response.transfer);
                        $('#transferDetailsModal').modal('show');
                    } else {
                        showError(response ? response.message : 'فشل في تحميل تفاصيل التحويل');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading transfer details:', error);
                    showError('خطأ في تحميل تفاصيل التحويل: ' + error);
                }
            });
        }

        function displayTransferDetails(transfer) {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات التحويل</h6>
                        <table class="table table-sm">
                            <tr><td><strong>رمز التحويل:</strong></td><td>${escapeHtml(transfer.transfer_code || 'غير محدد')}</td></tr>
                            <tr><td><strong>رمز الاستلام:</strong></td><td>${escapeHtml(transfer.pickup_code || 'غير محدد')}</td></tr>
                            <tr><td><strong>المبلغ:</strong></td><td>$${formatNumber(transfer.amount || 0)}</td></tr>
                            <tr><td><strong>الرسوم:</strong></td><td>$${formatNumber(transfer.fee || 0)}</td></tr>
                            <tr><td><strong>المبلغ الإجمالي:</strong></td><td>$${formatNumber(transfer.total_amount || transfer.amount || 0)}</td></tr>
                            <tr><td><strong>سعر الصرف:</strong></td><td>${transfer.exchange_rate || 1}</td></tr>
                            <tr><td><strong>الحالة:</strong></td><td><span class="status-badge status-${transfer.status || 'pending'}">${getStatusText(transfer.status || 'pending')}</span></td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات المرسل</h6>
                        <table class="table table-sm">
                            <tr><td><strong>الاسم:</strong></td><td>${escapeHtml(transfer.sender_name || 'غير محدد')}</td></tr>
                            <tr><td><strong>الهاتف:</strong></td><td>${escapeHtml(transfer.sender_phone || 'غير محدد')}</td></tr>
                            <tr><td><strong>الدولة:</strong></td><td>${escapeHtml(transfer.sender_country || 'غير محدد')}</td></tr>
                        </table>

                        <h6>معلومات المستلم</h6>
                        <table class="table table-sm">
                            <tr><td><strong>الاسم:</strong></td><td>${escapeHtml(transfer.recipient_name || 'غير محدد')}</td></tr>
                            <tr><td><strong>الهاتف:</strong></td><td>${escapeHtml(transfer.recipient_phone || 'غير محدد')}</td></tr>
                            <tr><td><strong>الدولة:</strong></td><td>${escapeHtml(transfer.recipient_country || 'غير محدد')}</td></tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <h6>معلومات إضافية</h6>
                        <table class="table table-sm">
                            <tr><td><strong>طريقة الدفع:</strong></td><td>${getPaymentMethodText(transfer.payment_method)}</td></tr>
                            <tr><td><strong>الغرض:</strong></td><td>${escapeHtml(transfer.purpose || 'غير محدد')}</td></tr>
                            <tr><td><strong>ملاحظات:</strong></td><td>${escapeHtml(transfer.notes || 'لا توجد ملاحظات')}</td></tr>
                            <tr><td><strong>تاريخ الإنشاء:</strong></td><td>${formatDateTime(transfer.created_at)}</td></tr>
                            <tr><td><strong>آخر تحديث:</strong></td><td>${formatDateTime(transfer.updated_at)}</td></tr>
                        </table>
                    </div>
                </div>
            `;
            $('#transferDetailsContent').html(content);
        }

        function updateStatus(id, currentStatus) {
            $('#transferId').val(id);
            $('#newStatus').val(currentStatus);
            $('#statusNotes').val('');
            $('#statusUpdateModal').modal('show');
        }

        function updateTransferStatus() {
            if (isLoading) return;

            const formData = {
                action: 'update_transfer_status',
                id: $('#transferId').val(),
                status: $('#newStatus').val(),
                notes: $('#statusNotes').val()
            };

            $.ajax({
                url: '',
                method: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response && response.success) {
                        showSuccess(response.message || 'تم تحديث الحالة بنجاح');
                        $('#statusUpdateModal').modal('hide');
                        loadTransfers();
                    } else {
                        showError(response ? response.message : 'فشل في تحديث الحالة');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error updating status:', error);
                    showError('خطأ في تحديث حالة التحويل: ' + error);
                }
            });
        }

        function exportTransfers() {
            showInfo('جاري تصدير التحويلات...');

            setTimeout(() => {
                const data = {
                    title: 'تقرير التحويلات',
                    exported_at: new Date().toISOString(),
                    filters: {
                        search: $('#searchInput').val(),
                        status: $('#statusFilter').val(),
                        date_from: $('#dateFrom').val(),
                        date_to: $('#dateTo').val()
                    },
                    page: currentPage,
                    limit: currentLimit
                };

                const dataStr = JSON.stringify(data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `transfers_export_${new Date().toISOString().slice(0, 10)}.json`;
                link.click();
                URL.revokeObjectURL(url);

                showSuccess('تم تصدير التحويلات بنجاح');
            }, 1000);
        }

        function printTransfer(id) {
            if (id) {
                showInfo('جاري تحضير الطباعة...');
                setTimeout(() => {
                    window.print();
                }, 1000);
            } else {
                window.print();
            }
        }

        function showLoading(show) {
            if (show) {
                $('#loadingOverlay').removeClass('d-none');
            } else {
                $('#loadingOverlay').addClass('d-none');
            }
        }

        // Utility functions
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function formatNumber(num) {
            if (num === null || num === undefined || isNaN(num)) return '0.00';
            return parseFloat(num).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('ar-SA');
            } catch (e) {
                return dateString;
            }
        }

        function formatTime(dateString) {
            if (!dateString) return '-';
            try {
                const date = new Date(dateString);
                return date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
            } catch (e) {
                return '';
            }
        }

        function formatDateTime(dateString) {
            if (!dateString) return '-';
            try {
                const date = new Date(dateString);
                return date.toLocaleString('ar-SA');
            } catch (e) {
                return dateString;
            }
        }

        function getStatusText(status) {
            const statuses = {
                'pending': 'في الانتظار',
                'processing': 'قيد المعالجة',
                'completed': 'مكتمل',
                'cancelled': 'ملغي',
                'failed': 'فاشل'
            };
            return statuses[status] || status || 'غير محدد';
        }

        function getPaymentMethodText(method) {
            const methods = {
                'cash': 'نقداً',
                'bank_transfer': 'تحويل بنكي',
                'credit_card': 'بطاقة ائتمان',
                'debit_card': 'بطاقة خصم',
                'mobile_wallet': 'محفظة إلكترونية'
            };
            return methods[method] || method || 'غير محدد';
        }

        function showSuccess(message) {
            showAlert(message, 'success');
        }

        function showError(message) {
            showAlert(message, 'danger');
        }

        function showInfo(message) {
            showAlert(message, 'info');
        }

        function showAlert(message, type) {
            // Remove existing alerts
            $('.alert-fixed').remove();

            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show alert-fixed" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${escapeHtml(message)}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);

            // Auto remove after 5 seconds
            setTimeout(function() {
                $('.alert-fixed').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }

        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
            showError('حدث خطأ غير متوقع في الصفحة');
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
            showError('حدث خطأ في العملية');
        });
    </script>
</body>
</html>
