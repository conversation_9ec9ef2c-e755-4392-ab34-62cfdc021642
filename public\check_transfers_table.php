<?php

/**
 * Check Transfers Table Structure
 * Elite Transfer System - Check and fix transfers table
 */

require_once __DIR__ . '/includes/database_manager.php';

try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
    
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<title>فحص جدول التحويلات - Elite Transfer System</title>";
    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
    echo "</head>";
    echo "<body class='bg-light'>";
    echo "<div class='container mt-4'>";
    echo "<h1 class='text-center mb-4'>🔍 فحص جدول التحويلات</h1>";
    
    // Check if transfers table exists
    echo "<div class='card mb-4'>";
    echo "<div class='card-header'><h3>1. فحص وجود الجدول</h3></div>";
    echo "<div class='card-body'>";
    
    $tables = $db->query("SHOW TABLES LIKE 'transfers'")->fetchAll();
    if (count($tables) > 0) {
        echo "<div class='alert alert-success'>✅ جدول transfers موجود</div>";
        
        // Show table structure
        echo "<h4>بنية الجدول:</h4>";
        $columns = $db->query("DESCRIBE transfers")->fetchAll();
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
        echo "<tbody>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        
    } else {
        echo "<div class='alert alert-danger'>❌ جدول transfers غير موجود</div>";
        echo "<p>سيتم إنشاء الجدول الآن...</p>";
        
        // Create transfers table
        $createTable = "
            CREATE TABLE transfers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                transfer_code VARCHAR(20) UNIQUE,
                pickup_code VARCHAR(20),
                user_id INT,
                sender_name VARCHAR(255),
                sender_phone VARCHAR(20),
                sender_country_id INT,
                sender_address TEXT,
                recipient_name VARCHAR(255),
                recipient_phone VARCHAR(20),
                recipient_country_id INT,
                recipient_address TEXT,
                amount DECIMAL(15,2),
                fee DECIMAL(15,2),
                exchange_rate DECIMAL(10,4),
                total_amount DECIMAL(15,2),
                currency_from VARCHAR(3) DEFAULT 'USD',
                currency_to VARCHAR(3) DEFAULT 'USD',
                payment_method VARCHAR(50),
                purpose TEXT,
                notes TEXT,
                status ENUM('pending', 'processing', 'completed', 'cancelled', 'failed') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                deleted_at TIMESTAMP NULL DEFAULT NULL,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (sender_country_id) REFERENCES countries(id) ON DELETE SET NULL,
                FOREIGN KEY (recipient_country_id) REFERENCES countries(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $db->exec($createTable);
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول transfers بنجاح</div>";
    }
    
    echo "</div></div>";
    
    // Check data
    echo "<div class='card mb-4'>";
    echo "<div class='card-header'><h3>2. فحص البيانات</h3></div>";
    echo "<div class='card-body'>";
    
    $count = $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn();
    echo "<div class='alert alert-info'>📊 عدد التحويلات: $count</div>";
    
    if ($count == 0) {
        echo "<p>لا توجد تحويلات. سيتم إضافة بيانات تجريبية...</p>";
        
        // Insert sample transfers
        $sampleTransfers = [
            [
                'transfer_code' => 'TRF001',
                'pickup_code' => 'PCK001',
                'user_id' => 1,
                'sender_name' => 'أحمد محمد',
                'sender_phone' => '+966501234567',
                'sender_country_id' => 1,
                'sender_address' => 'الرياض، المملكة العربية السعودية',
                'recipient_name' => 'فاطمة علي',
                'recipient_phone' => '+************',
                'recipient_country_id' => 2,
                'recipient_address' => 'القاهرة، مصر',
                'amount' => 1000.00,
                'fee' => 25.00,
                'exchange_rate' => 4.75,
                'total_amount' => 1025.00,
                'payment_method' => 'bank_transfer',
                'purpose' => 'دعم الأسرة',
                'status' => 'completed'
            ],
            [
                'transfer_code' => 'TRF002',
                'pickup_code' => 'PCK002',
                'user_id' => 1,
                'sender_name' => 'سارة أحمد',
                'sender_phone' => '+************',
                'sender_country_id' => 1,
                'sender_address' => 'جدة، المملكة العربية السعودية',
                'recipient_name' => 'محمد حسن',
                'recipient_phone' => '+************',
                'recipient_country_id' => 3,
                'recipient_address' => 'دبي، الإمارات العربية المتحدة',
                'amount' => 500.00,
                'fee' => 15.00,
                'exchange_rate' => 3.67,
                'total_amount' => 515.00,
                'payment_method' => 'cash',
                'purpose' => 'تعليم',
                'status' => 'pending'
            ],
            [
                'transfer_code' => 'TRF003',
                'pickup_code' => 'PCK003',
                'user_id' => 1,
                'sender_name' => 'عبدالله سالم',
                'sender_phone' => '+966509876543',
                'sender_country_id' => 1,
                'sender_address' => 'الدمام، المملكة العربية السعودية',
                'recipient_name' => 'ليلى محمود',
                'recipient_phone' => '+962791234567',
                'recipient_country_id' => 4,
                'recipient_address' => 'عمان، الأردن',
                'amount' => 750.00,
                'fee' => 20.00,
                'exchange_rate' => 0.71,
                'total_amount' => 770.00,
                'payment_method' => 'credit_card',
                'purpose' => 'علاج طبي',
                'status' => 'processing'
            ]
        ];
        
        $stmt = $db->prepare("
            INSERT INTO transfers (
                transfer_code, pickup_code, user_id, sender_name, sender_phone, 
                sender_country_id, sender_address, recipient_name, recipient_phone,
                recipient_country_id, recipient_address, amount, fee, exchange_rate,
                total_amount, payment_method, purpose, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        foreach ($sampleTransfers as $transfer) {
            $stmt->execute([
                $transfer['transfer_code'],
                $transfer['pickup_code'],
                $transfer['user_id'],
                $transfer['sender_name'],
                $transfer['sender_phone'],
                $transfer['sender_country_id'],
                $transfer['sender_address'],
                $transfer['recipient_name'],
                $transfer['recipient_phone'],
                $transfer['recipient_country_id'],
                $transfer['recipient_address'],
                $transfer['amount'],
                $transfer['fee'],
                $transfer['exchange_rate'],
                $transfer['total_amount'],
                $transfer['payment_method'],
                $transfer['purpose'],
                $transfer['status']
            ]);
        }
        
        echo "<div class='alert alert-success'>✅ تم إضافة " . count($sampleTransfers) . " تحويل تجريبي</div>";
    }
    
    echo "</div></div>";
    
    // Test query
    echo "<div class='card mb-4'>";
    echo "<div class='card-header'><h3>3. اختبار الاستعلام</h3></div>";
    echo "<div class='card-body'>";
    
    try {
        $query = "
            SELECT t.*, 
                   u.name as user_name,
                   sc.name as sender_country,
                   rc.name as recipient_country
            FROM transfers t
            LEFT JOIN users u ON t.user_id = u.id
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.recipient_country_id = rc.id
            WHERE t.deleted_at IS NULL 
            ORDER BY t.created_at DESC 
            LIMIT 5
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        $transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='alert alert-success'>✅ الاستعلام نجح - تم العثور على " . count($transfers) . " تحويل</div>";
        
        if (count($transfers) > 0) {
            echo "<h4>عينة من التحويلات:</h4>";
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>الرمز</th><th>المرسل</th><th>المستلم</th><th>المبلغ</th><th>الحالة</th></tr></thead>";
            echo "<tbody>";
            foreach ($transfers as $transfer) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($transfer['transfer_code']) . "</td>";
                echo "<td>" . htmlspecialchars($transfer['sender_name']) . "</td>";
                echo "<td>" . htmlspecialchars($transfer['recipient_name']) . "</td>";
                echo "<td>$" . number_format($transfer['amount'], 2) . "</td>";
                echo "<td>" . htmlspecialchars($transfer['status']) . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ في الاستعلام: " . $e->getMessage() . "</div>";
    }
    
    echo "</div></div>";
    
    // Links
    echo "<div class='text-center'>";
    echo "<a href='admin_transfers_enhanced.php' class='btn btn-primary me-2'>اختبار صفحة إدارة التحويلات</a>";
    echo "<a href='dashboard.php' class='btn btn-secondary'>العودة للوحة التحكم</a>";
    echo "</div>";
    
    echo "</div>";
    echo "</body>";
    echo "</html>";
    
} catch (Exception $e) {
    echo "<!DOCTYPE html>";
    echo "<html><head><meta charset='UTF-8'><title>خطأ</title></head><body>";
    echo "<div class='container mt-5'>";
    echo "<div class='alert alert-danger'>";
    echo "<h4>خطأ في قاعدة البيانات</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    echo "</div>";
    echo "</body></html>";
}

?>
