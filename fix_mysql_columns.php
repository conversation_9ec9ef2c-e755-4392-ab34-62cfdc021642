<?php

/**
 * Fix MySQL Missing Columns
 * Elite Transfer System - Add missing columns to MySQL tables
 */

echo "🔧 Fixing MySQL Missing Columns - Elite Transfer System\n";
echo str_repeat("=", 60) . "\n\n";

try {
    // Connect to MySQL
    $dsn = "mysql:host=localhost;port=3306;dbname=elite_transfer;charset=utf8mb4";
    $db = new PDO($dsn, 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ Connected to MySQL database\n\n";
    
    // Fix users table
    echo "🔧 Fixing users table...\n";
    $userFixes = [
        "ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'active'",
        "ALTER TABLE users ADD COLUMN role ENUM('admin', 'manager', 'agent', 'customer', 'compliance') DEFAULT 'customer'",
        "ALTER TABLE users ADD COLUMN user_code VARCHAR(20) UNIQUE",
        "ALTER TABLE users ADD COLUMN phone VARCHAR(20)",
        "ALTER TABLE users ADD COLUMN password_hash VARCHAR(255)"
    ];
    
    foreach ($userFixes as $query) {
        try {
            $db->exec($query);
            echo "  ✅ Added column to users table\n";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "  ✓ Column already exists in users table\n";
            } else {
                echo "  ⚠️  " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Fix countries table
    echo "\n🔧 Fixing countries table...\n";
    $countryFixes = [
        "ALTER TABLE countries ADD COLUMN is_active BOOLEAN DEFAULT TRUE",
        "ALTER TABLE countries ADD COLUMN code VARCHAR(3)",
        "ALTER TABLE countries ADD COLUMN name VARCHAR(100)"
    ];
    
    foreach ($countryFixes as $query) {
        try {
            $db->exec($query);
            echo "  ✅ Added column to countries table\n";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "  ✓ Column already exists in countries table\n";
            } else {
                echo "  ⚠️  " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Update existing users with missing data
    echo "\n📊 Updating existing user data...\n";
    
    // Check if users need user_code
    $usersWithoutCode = $db->query("SELECT COUNT(*) FROM users WHERE user_code IS NULL OR user_code = ''")->fetchColumn();
    if ($usersWithoutCode > 0) {
        echo "  🔄 Adding user codes to $usersWithoutCode users...\n";
        
        $users = $db->query("SELECT id, name FROM users WHERE user_code IS NULL OR user_code = ''")->fetchAll();
        $stmt = $db->prepare("UPDATE users SET user_code = ? WHERE id = ?");
        
        foreach ($users as $user) {
            $userCode = 'USR' . str_pad($user['id'], 3, '0', STR_PAD_LEFT);
            $stmt->execute([$userCode, $user['id']]);
            echo "    ✅ User '{$user['name']}' -> $userCode\n";
        }
    }
    
    // Check if users need role assignment
    $usersWithoutRole = $db->query("SELECT COUNT(*) FROM users WHERE role IS NULL OR role = ''")->fetchColumn();
    if ($usersWithoutRole > 0) {
        echo "  🔄 Assigning roles to $usersWithoutRole users...\n";
        
        // Assign first user as admin, others as customers
        $db->exec("UPDATE users SET role = 'admin' WHERE id = (SELECT MIN(id) FROM (SELECT id FROM users) AS temp)");
        $db->exec("UPDATE users SET role = 'customer' WHERE role IS NULL OR role = ''");
        
        echo "    ✅ Roles assigned\n";
    }
    
    // Check if users need status
    $usersWithoutStatus = $db->query("SELECT COUNT(*) FROM users WHERE status IS NULL OR status = ''")->fetchColumn();
    if ($usersWithoutStatus > 0) {
        echo "  🔄 Setting status for $usersWithoutStatus users...\n";
        $db->exec("UPDATE users SET status = 'active' WHERE status IS NULL OR status = ''");
        echo "    ✅ Status set to active\n";
    }
    
    // Verify the fixes
    echo "\n📊 Verification:\n";
    
    // Check users table structure
    $userColumns = $db->query("SHOW COLUMNS FROM users")->fetchAll();
    $requiredUserColumns = ['id', 'name', 'email', 'status', 'role', 'user_code', 'deleted_at'];
    
    echo "  Users table columns:\n";
    foreach ($requiredUserColumns as $col) {
        $exists = false;
        foreach ($userColumns as $column) {
            if ($column['Field'] === $col) {
                $exists = true;
                break;
            }
        }
        echo "    " . ($exists ? "✅" : "❌") . " $col\n";
    }
    
    // Test queries
    echo "\n🧪 Testing queries:\n";
    
    try {
        $activeUsers = $db->query("SELECT COUNT(*) FROM users WHERE status = 'active' AND deleted_at IS NULL")->fetchColumn();
        echo "  ✅ Active users query: $activeUsers users\n";
    } catch (Exception $e) {
        echo "  ❌ Active users query failed: " . $e->getMessage() . "\n";
    }
    
    try {
        $activeCountries = $db->query("SELECT COUNT(*) FROM countries WHERE is_active = 1")->fetchColumn();
        echo "  ✅ Active countries query: $activeCountries countries\n";
    } catch (Exception $e) {
        echo "  ❌ Active countries query failed: " . $e->getMessage() . "\n";
    }
    
    try {
        $transfers = $db->query("SELECT COUNT(*) FROM transfers WHERE deleted_at IS NULL")->fetchColumn();
        echo "  ✅ Transfers query: $transfers transfers\n";
    } catch (Exception $e) {
        echo "  ❌ Transfers query failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n🎉 MySQL column fixes completed successfully!\n";
    echo "✅ Database is now ready for use with the application\n";
    
} catch (PDOException $e) {
    echo "❌ Fix failed!\n";
    echo "Error: " . $e->getMessage() . "\n";
}

?>
