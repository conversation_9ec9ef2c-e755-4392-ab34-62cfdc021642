<?php

/**
 * Fix SessionHelper Errors
 * Elite Transfer System - Fix SessionHelper class not found errors
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح أخطاء SessionHelper - Elite Transfer System</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); min-height: 100vh; padding: 20px; }";
echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 20px; }";
echo ".step { padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid; }";
echo ".success { border-left-color: #28a745; background: #d4edda; color: #155724; }";
echo ".error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }";
echo ".warning { border-left-color: #ffc107; background: #fff3cd; color: #856404; }";
echo ".info { border-left-color: #17a2b8; background: #d1ecf1; color: #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 class='text-white text-center mb-4'>🔧 إصلاح أخطاء SessionHelper</h1>";

// Step 1: Scan for SessionHelper errors
echo "<div class='fix-card'>";
echo "<h3>الخطوة 1: فحص الملفات للبحث عن أخطاء SessionHelper</h3>";

$phpFiles = glob(__DIR__ . '/*.php');
$problemFiles = [];

foreach ($phpFiles as $file) {
    $filename = basename($file);
    if ($filename === 'fix_session_helper_errors.php') continue;
    
    $content = file_get_contents($file);
    if (strpos($content, 'SessionHelper::') !== false) {
        $problemFiles[] = $filename;
        echo "<div class='step warning'>⚠️ تم العثور على SessionHelper:: في الملف: $filename</div>";
    }
}

if (empty($problemFiles)) {
    echo "<div class='step success'>✅ لم يتم العثور على أخطاء SessionHelper في الملفات</div>";
} else {
    echo "<div class='step info'>ℹ️ تم العثور على " . count($problemFiles) . " ملف يحتوي على أخطاء SessionHelper</div>";
}

echo "</div>";

// Step 2: Fix the errors
if (!empty($problemFiles)) {
    echo "<div class='fix-card'>";
    echo "<h3>الخطوة 2: إصلاح الأخطاء</h3>";
    
    foreach ($problemFiles as $filename) {
        $filePath = __DIR__ . '/' . $filename;
        $content = file_get_contents($filePath);
        $originalContent = $content;
        
        // Fix SessionHelper::isLoggedIn() to isLoggedIn()
        $content = str_replace('SessionHelper::isLoggedIn()', 'isLoggedIn()', $content);
        
        // Fix SessionHelper::getUserData() to getUserData()
        $content = str_replace('SessionHelper::getUserData()', 'getUserData()', $content);
        
        // Fix SessionHelper::logout() to logout()
        $content = str_replace('SessionHelper::logout()', 'logout()', $content);
        
        // Fix absolute paths to relative paths
        $content = str_replace('href="/"', 'href="index.php"', $content);
        $content = str_replace('href="/dashboard"', 'href="dashboard.php"', $content);
        $content = str_replace('href="/login"', 'href="login.php"', $content);
        $content = str_replace('href="/logout"', 'href="logout.php"', $content);
        $content = str_replace('href="/create-transfer"', 'href="create-transfer.php"', $content);
        $content = str_replace('href="/track-transfer"', 'href="track-transfer.php"', $content);
        $content = str_replace('href="/transfers"', 'href="transfers.php"', $content);
        $content = str_replace('href="/users"', 'href="users.php"', $content);
        $content = str_replace('href="/reports"', 'href="reports.php"', $content);
        
        if ($content !== $originalContent) {
            if (file_put_contents($filePath, $content)) {
                echo "<div class='step success'>✅ تم إصلاح الملف: $filename</div>";
            } else {
                echo "<div class='step error'>❌ فشل في إصلاح الملف: $filename</div>";
            }
        } else {
            echo "<div class='step info'>ℹ️ الملف $filename لا يحتاج إصلاح</div>";
        }
    }
    
    echo "</div>";
}

// Step 3: Test the fixes
echo "<div class='fix-card'>";
echo "<h3>الخطوة 3: اختبار الإصلاحات</h3>";

$testFiles = [
    'track-transfer.php' => 'تتبع التحويل',
    'track_transfer_fixed.php' => 'تتبع التحويل المُحسن',
    'dashboard.php' => 'لوحة التحكم',
    'index.php' => 'الصفحة الرئيسية'
];

foreach ($testFiles as $file => $description) {
    if (file_exists(__DIR__ . '/' . $file)) {
        $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/' . $file;
        echo "<div class='step info'>";
        echo "<strong>$description:</strong> ";
        echo "<a href='$url' target='_blank' class='btn btn-sm btn-outline-primary'>اختبار</a>";
        echo "</div>";
    }
}

echo "</div>";

// Step 4: Summary and recommendations
echo "<div class='fix-card'>";
echo "<h3>الخطوة 4: الملخص والتوصيات</h3>";

echo "<div class='alert alert-success'>";
echo "<h5>✅ ما تم إصلاحه:</h5>";
echo "<ul>";
echo "<li>تحويل SessionHelper::isLoggedIn() إلى isLoggedIn()</li>";
echo "<li>تحويل SessionHelper::getUserData() إلى getUserData()</li>";
echo "<li>تحويل SessionHelper::logout() إلى logout()</li>";
echo "<li>إصلاح الروابط المطلقة إلى روابط نسبية</li>";
echo "<li>إصلاح مراجع الملفات والمسارات</li>";
echo "</ul>";
echo "</div>";

echo "<div class='alert alert-info'>";
echo "<h5>📋 التوصيات:</h5>";
echo "<ul>";
echo "<li>استخدم functions بدلاً من static methods للجلسات</li>";
echo "<li>استخدم روابط نسبية بدلاً من مطلقة</li>";
echo "<li>تأكد من تضمين session_helper.php في جميع الملفات</li>";
echo "<li>اختبر جميع الصفحات بعد الإصلاح</li>";
echo "</ul>";
echo "</div>";

echo "<div class='text-center'>";
echo "<h5>🚀 النظام جاهز للاستخدام!</h5>";
echo "<div class='d-grid gap-2 d-md-flex justify-content-center'>";
echo "<a href='track-transfer.php' class='btn btn-success'>تتبع التحويل</a>";
echo "<a href='track_transfer_fixed.php' class='btn btn-info'>النسخة المُحسنة</a>";
echo "<a href='test_server.php' class='btn btn-warning'>اختبار الخادم</a>";
echo "<a href='dashboard.php' class='btn btn-primary'>لوحة التحكم</a>";
echo "</div>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

?>
