<?php

/**
 * Countries Setup Script
 * Elite Transfer System - Initialize Countries Table
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';

$db = DatabaseManager::getInstance();

try {
    echo "<h2>إعد<PERSON> جدول البلدان</h2>";
    
    // Drop existing table if exists
    echo "<p>حذف الجدول الموجود...</p>";
    $db->query("DROP TABLE IF EXISTS countries");

    // Create countries table with proper structure
    echo "<p>إنشاء جدول البلدان...</p>";
    $db->query("
        CREATE TABLE countries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            currency VARCHAR(3) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            status ENUM('active', 'inactive') DEFAULT 'active',
            flag_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP NULL,
            INDEX idx_code (code),
            INDEX idx_status (status),
            INDEX idx_deleted_at (deleted_at)
        )
    ");
    
    // Insert sample countries
    echo "<p>إدراج البلدان العربية...</p>";
    
    $countries = [
        ['name' => 'المملكة العربية السعودية', 'code' => 'SA', 'currency' => 'SAR', 'exchange_rate' => 1.0000],
        ['name' => 'الإمارات العربية المتحدة', 'code' => 'AE', 'currency' => 'AED', 'exchange_rate' => 1.0200],
        ['name' => 'الكويت', 'code' => 'KW', 'currency' => 'KWD', 'exchange_rate' => 0.3000],
        ['name' => 'قطر', 'code' => 'QA', 'currency' => 'QAR', 'exchange_rate' => 3.6400],
        ['name' => 'البحرين', 'code' => 'BH', 'currency' => 'BHD', 'exchange_rate' => 0.3800],
        ['name' => 'عمان', 'code' => 'OM', 'currency' => 'OMR', 'exchange_rate' => 0.3800],
        ['name' => 'الأردن', 'code' => 'JO', 'currency' => 'JOD', 'exchange_rate' => 0.7100],
        ['name' => 'لبنان', 'code' => 'LB', 'currency' => 'LBP', 'exchange_rate' => 1507.5000],
        ['name' => 'مصر', 'code' => 'EG', 'currency' => 'EGP', 'exchange_rate' => 30.9000],
        ['name' => 'المغرب', 'code' => 'MA', 'currency' => 'MAD', 'exchange_rate' => 10.1000],
        ['name' => 'تونس', 'code' => 'TN', 'currency' => 'TND', 'exchange_rate' => 3.1000],
        ['name' => 'الجزائر', 'code' => 'DZ', 'currency' => 'DZD', 'exchange_rate' => 134.4000],
        ['name' => 'العراق', 'code' => 'IQ', 'currency' => 'IQD', 'exchange_rate' => 1460.0000],
        ['name' => 'سوريا', 'code' => 'SY', 'currency' => 'SYP', 'exchange_rate' => 2512.0000],
        ['name' => 'اليمن', 'code' => 'YE', 'currency' => 'YER', 'exchange_rate' => 250.0000],
        ['name' => 'السودان', 'code' => 'SD', 'currency' => 'SDG', 'exchange_rate' => 601.0000],
        ['name' => 'ليبيا', 'code' => 'LY', 'currency' => 'LYD', 'exchange_rate' => 4.8000],
        ['name' => 'الصومال', 'code' => 'SO', 'currency' => 'SOS', 'exchange_rate' => 570.0000],
        ['name' => 'جيبوتي', 'code' => 'DJ', 'currency' => 'DJF', 'exchange_rate' => 177.7000],
        ['name' => 'موريتانيا', 'code' => 'MR', 'currency' => 'MRU', 'exchange_rate' => 36.7000],
        ['name' => 'فلسطين', 'code' => 'PS', 'currency' => 'ILS', 'exchange_rate' => 3.7000],
        ['name' => 'الولايات المتحدة', 'code' => 'US', 'currency' => 'USD', 'exchange_rate' => 1.0000],
        ['name' => 'المملكة المتحدة', 'code' => 'GB', 'currency' => 'GBP', 'exchange_rate' => 0.8000],
        ['name' => 'ألمانيا', 'code' => 'DE', 'currency' => 'EUR', 'exchange_rate' => 0.9000],
        ['name' => 'فرنسا', 'code' => 'FR', 'currency' => 'EUR', 'exchange_rate' => 0.9000],
        ['name' => 'إيطاليا', 'code' => 'IT', 'currency' => 'EUR', 'exchange_rate' => 0.9000],
        ['name' => 'إسبانيا', 'code' => 'ES', 'currency' => 'EUR', 'exchange_rate' => 0.9000],
        ['name' => 'كندا', 'code' => 'CA', 'currency' => 'CAD', 'exchange_rate' => 1.3500],
        ['name' => 'أستراليا', 'code' => 'AU', 'currency' => 'AUD', 'exchange_rate' => 1.5000],
        ['name' => 'اليابان', 'code' => 'JP', 'currency' => 'JPY', 'exchange_rate' => 150.0000],
        ['name' => 'الصين', 'code' => 'CN', 'currency' => 'CNY', 'exchange_rate' => 7.2000],
        ['name' => 'الهند', 'code' => 'IN', 'currency' => 'INR', 'exchange_rate' => 83.0000],
        ['name' => 'باكستان', 'code' => 'PK', 'currency' => 'PKR', 'exchange_rate' => 280.0000],
        ['name' => 'بنغلاديش', 'code' => 'BD', 'currency' => 'BDT', 'exchange_rate' => 110.0000],
        ['name' => 'إندونيسيا', 'code' => 'ID', 'currency' => 'IDR', 'exchange_rate' => 15500.0000],
        ['name' => 'ماليزيا', 'code' => 'MY', 'currency' => 'MYR', 'exchange_rate' => 4.7000],
        ['name' => 'سنغافورة', 'code' => 'SG', 'currency' => 'SGD', 'exchange_rate' => 1.3500],
        ['name' => 'تايلاند', 'code' => 'TH', 'currency' => 'THB', 'exchange_rate' => 36.0000],
        ['name' => 'الفلبين', 'code' => 'PH', 'currency' => 'PHP', 'exchange_rate' => 56.0000],
        ['name' => 'فيتنام', 'code' => 'VN', 'currency' => 'VND', 'exchange_rate' => 24000.0000],
        ['name' => 'كوريا الجنوبية', 'code' => 'KR', 'currency' => 'KRW', 'exchange_rate' => 1320.0000],
        ['name' => 'تركيا', 'code' => 'TR', 'currency' => 'TRY', 'exchange_rate' => 28.0000],
        ['name' => 'إيران', 'code' => 'IR', 'currency' => 'IRR', 'exchange_rate' => 42000.0000],
        ['name' => 'أفغانستان', 'code' => 'AF', 'currency' => 'AFN', 'exchange_rate' => 85.0000],
        ['name' => 'روسيا', 'code' => 'RU', 'currency' => 'RUB', 'exchange_rate' => 90.0000],
        ['name' => 'البرازيل', 'code' => 'BR', 'currency' => 'BRL', 'exchange_rate' => 5.0000],
        ['name' => 'المكسيك', 'code' => 'MX', 'currency' => 'MXN', 'exchange_rate' => 17.0000],
        ['name' => 'الأرجنتين', 'code' => 'AR', 'currency' => 'ARS', 'exchange_rate' => 350.0000],
        ['name' => 'جنوب أفريقيا', 'code' => 'ZA', 'currency' => 'ZAR', 'exchange_rate' => 18.5000],
        ['name' => 'نيجيريا', 'code' => 'NG', 'currency' => 'NGN', 'exchange_rate' => 750.0000],
        ['name' => 'كينيا', 'code' => 'KE', 'currency' => 'KES', 'exchange_rate' => 150.0000]
    ];
    
    $inserted = 0;
    foreach ($countries as $country) {
        try {
            $countryData = [
                'name' => $country['name'],
                'code' => $country['code'],
                'currency' => $country['currency'],
                'exchange_rate' => $country['exchange_rate'],
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            if ($db->insert('countries', $countryData)) {
                $inserted++;
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>خطأ في إدراج {$country['name']}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p style='color: green;'>تم إدراج $inserted بلد بنجاح!</p>";
    
    // Verify the data
    $totalCountries = $db->selectOne("SELECT COUNT(*) as count FROM countries")['count'];
    echo "<p>إجمالي البلدان في قاعدة البيانات: $totalCountries</p>";
    
    // Show some sample data
    echo "<h3>عينة من البلدان المُدرجة:</h3>";
    $sampleCountries = $db->select("SELECT name, code, currency, exchange_rate FROM countries LIMIT 10");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الاسم</th><th>الرمز</th><th>العملة</th><th>سعر الصرف</th></tr>";
    
    foreach ($sampleCountries as $country) {
        echo "<tr>";
        echo "<td>{$country['name']}</td>";
        echo "<td>{$country['code']}</td>";
        echo "<td>{$country['currency']}</td>";
        echo "<td>{$country['exchange_rate']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<br><p style='color: blue;'><strong>تم إعداد جدول البلدان بنجاح!</strong></p>";
    echo "<p><a href='countries_management.php'>انتقل إلى إدارة البلدان</a></p>";
    echo "<p><a href='dashboard_advanced.php'>العودة إلى لوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد البلدان - <?= SYSTEM_NAME ?></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        h2, h3 {
            color: #333;
        }
        
        table {
            background: white;
            margin: 20px 0;
        }
        
        th {
            background: #667eea;
            color: white;
            padding: 10px;
        }
        
        td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        
        a {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
