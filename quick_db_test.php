<?php

/**
 * Quick Database Connection Test
 * Elite Transfer System
 */

echo "🚀 Quick Database Connection Test\n";
echo str_repeat("=", 50) . "\n\n";

// Load environment
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

$dbType = $_ENV['DB_CONNECTION'] ?? 'sqlite';
echo "📊 Database Type: " . strtoupper($dbType) . "\n\n";

try {
    if ($dbType === 'sqlite') {
        // SQLite Connection Test
        $database = $_ENV['DB_DATABASE'] ?? 'database/elite_transfer_production.db';
        $fullPath = __DIR__ . '/' . $database;
        
        if (!file_exists($fullPath)) {
            throw new Exception("SQLite database file not found: $database");
        }
        
        $pdo = new PDO("sqlite:$fullPath", null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        $version = $pdo->query("SELECT sqlite_version()")->fetchColumn();
        echo "✅ SQLite Connection: SUCCESS\n";
        echo "   Version: $version\n";
        echo "   Database: $database\n";
        echo "   File Size: " . number_format(filesize($fullPath)) . " bytes\n\n";
        
    } else {
        // MySQL Connection Test
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? 'elite_transfer';
        $username = $_ENV['DB_USERNAME'] ?? 'root';
        $password = $_ENV['DB_PASSWORD'] ?? '';
        
        $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        $version = $pdo->query("SELECT VERSION()")->fetchColumn();
        echo "✅ MySQL Connection: SUCCESS\n";
        echo "   Version: $version\n";
        echo "   Host: $host:$port\n";
        echo "   Database: $database\n";
        echo "   Username: $username\n\n";
    }
    
    // Test basic operations
    echo "🔍 Testing Basic Operations:\n";
    echo str_repeat("-", 30) . "\n";
    
    // Count records in main tables
    $tables = ['users', 'countries', 'transfers', 'exchange_rates'];
    foreach ($tables as $table) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            echo "   $table: " . number_format($count) . " records\n";
        } catch (Exception $e) {
            echo "   $table: ❌ Error - " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n🎯 Performance Test:\n";
    echo str_repeat("-", 30) . "\n";
    
    $start = microtime(true);
    for ($i = 0; $i < 10; $i++) {
        $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    }
    $end = microtime(true);
    $duration = round(($end - $start) * 1000, 2);
    echo "   10 queries in {$duration}ms\n";
    echo "   Average: " . round($duration / 10, 2) . "ms per query\n";
    
    echo "\n📈 Database Statistics:\n";
    echo str_repeat("-", 30) . "\n";
    
    // Get some statistics
    $stats = [
        'Total Users' => $pdo->query("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL")->fetchColumn(),
        'Active Users' => $pdo->query("SELECT COUNT(*) FROM users WHERE status = 'active' AND deleted_at IS NULL")->fetchColumn(),
        'Countries' => $pdo->query("SELECT COUNT(*) FROM countries WHERE is_active = 1")->fetchColumn(),
        'Exchange Rates' => $pdo->query("SELECT COUNT(*) FROM exchange_rates")->fetchColumn()
    ];
    
    foreach ($stats as $label => $count) {
        echo "   $label: " . number_format($count) . "\n";
    }
    
    // Test user roles
    echo "\n👥 User Roles:\n";
    echo str_repeat("-", 30) . "\n";
    
    $roles = $pdo->query("
        SELECT role, COUNT(*) as count 
        FROM users 
        WHERE deleted_at IS NULL 
        GROUP BY role 
        ORDER BY count DESC
    ")->fetchAll();
    
    foreach ($roles as $role) {
        echo "   {$role['role']}: {$role['count']} users\n";
    }
    
    echo "\n🎉 All tests passed successfully!\n";
    echo "✅ Database is ready for use.\n\n";
    
    // Show connection info
    echo "🔧 Connection Details:\n";
    echo str_repeat("-", 30) . "\n";
    echo "   Type: " . strtoupper($dbType) . "\n";
    echo "   Status: 🟢 Connected\n";
    echo "   Environment: " . ($_ENV['APP_ENV'] ?? 'production') . "\n";
    echo "   Debug Mode: " . ($_ENV['APP_DEBUG'] ?? 'false') . "\n";
    
} catch (Exception $e) {
    echo "❌ Database Connection FAILED!\n";
    echo "Error: " . $e->getMessage() . "\n\n";
    
    echo "🔧 Troubleshooting Steps:\n";
    echo str_repeat("-", 30) . "\n";
    
    if ($dbType === 'sqlite') {
        echo "1. Check if database file exists: {$_ENV['DB_DATABASE']}\n";
        echo "2. Run: php setup_production_database.php\n";
        echo "3. Check file permissions\n";
    } else {
        echo "1. Start MySQL in XAMPP Control Panel\n";
        echo "2. Create database 'elite_transfer'\n";
        echo "3. Check MySQL credentials in .env file\n";
        echo "4. Run: php setup_production_database.php\n";
    }
    
    echo "\n💡 Quick Fixes:\n";
    echo "   • Run: php database_connection_manager.php\n";
    echo "   • Or: php test_database_connection.php\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "🔚 Test completed at " . date('Y-m-d H:i:s') . "\n";

?>
