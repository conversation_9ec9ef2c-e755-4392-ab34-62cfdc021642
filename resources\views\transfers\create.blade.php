@extends('layouts.app')

@section('title', 'Send Money - Elite Financial Transfer System')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-white mb-1">
                        <i class="bi bi-send me-2"></i>
                        {{ __('Send Money') }}
                    </h2>
                    <p class="text-white-50 mb-0">{{ __('Send money worldwide quickly and securely') }}</p>
                </div>
                <a href="{{ route('dashboard') }}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>
                    {{ __('Back to Dashboard') }}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Transfer Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body p-4">
                    <form id="transferForm" method="POST" action="{{ route('transfers.store') }}">
                        @csrf
                        
                        <!-- Step 1: Transfer Details -->
                        <div class="step" id="step1">
                            <h4 class="mb-4">
                                <span class="badge bg-primary me-2">1</span>
                                {{ __('Transfer Details') }}
                            </h4>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sender_country_id" class="form-label">
                                            <i class="bi bi-geo-alt me-1"></i>
                                            {{ __('Sending From') }}
                                        </label>
                                        <select class="form-select" id="sender_country_id" name="sender_country_id" required>
                                            <option value="">{{ __('Select country') }}</option>
                                            @foreach($countries->where('is_sender_country', true) as $country)
                                                <option value="{{ $country->id }}" data-currency="{{ $country->currency_id }}">
                                                    {{ app()->getLocale() === 'ar' ? $country->name_ar : $country->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="receiver_country_id" class="form-label">
                                            <i class="bi bi-geo-alt-fill me-1"></i>
                                            {{ __('Sending To') }}
                                        </label>
                                        <select class="form-select" id="receiver_country_id" name="receiver_country_id" required>
                                            <option value="">{{ __('Select country') }}</option>
                                            @foreach($countries->where('is_receiver_country', true) as $country)
                                                <option value="{{ $country->id }}" data-currency="{{ $country->currency_id }}">
                                                    {{ app()->getLocale() === 'ar' ? $country->name_ar : $country->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sender_currency_id" class="form-label">
                                            <i class="bi bi-currency-exchange me-1"></i>
                                            {{ __('You Send') }}
                                        </label>
                                        <select class="form-select" id="sender_currency_id" name="sender_currency_id" required>
                                            <option value="">{{ __('Select currency') }}</option>
                                            @foreach($currencies as $currency)
                                                <option value="{{ $currency->id }}">
                                                    {{ $currency->code }} - {{ $currency->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="receiver_currency_id" class="form-label">
                                            <i class="bi bi-currency-dollar me-1"></i>
                                            {{ __('They Receive') }}
                                        </label>
                                        <select class="form-select" id="receiver_currency_id" name="receiver_currency_id" required>
                                            <option value="">{{ __('Select currency') }}</option>
                                            @foreach($currencies as $currency)
                                                <option value="{{ $currency->id }}">
                                                    {{ $currency->code }} - {{ $currency->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="amount" class="form-label">
                                            <i class="bi bi-cash me-1"></i>
                                            {{ __('Amount to Send') }}
                                        </label>
                                        <input type="number" 
                                               class="form-control" 
                                               id="amount" 
                                               name="amount" 
                                               min="1" 
                                               step="0.01" 
                                               required
                                               placeholder="0.00">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">{{ __('Transfer Type') }}</label>
                                        <div class="d-flex gap-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="type" id="individual" value="individual" checked>
                                                <label class="form-check-label" for="individual">
                                                    {{ __('Individual') }}
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="type" id="business" value="business">
                                                <label class="form-check-label" for="business">
                                                    {{ __('Business') }}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="priority" class="form-label">
                                            <i class="bi bi-speedometer me-1"></i>
                                            {{ __('Priority') }}
                                        </label>
                                        <select class="form-select" id="priority" name="priority" required>
                                            <option value="normal">{{ __('Normal (24-48 hours)') }}</option>
                                            <option value="high">{{ __('High (12-24 hours) +$5') }}</option>
                                            <option value="urgent">{{ __('Urgent (1-6 hours) +$15') }}</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="scheduled_at" class="form-label">
                                            <i class="bi bi-calendar me-1"></i>
                                            {{ __('Schedule Transfer (Optional)') }}
                                        </label>
                                        <input type="datetime-local" 
                                               class="form-control" 
                                               id="scheduled_at" 
                                               name="scheduled_at"
                                               min="{{ now()->addHour()->format('Y-m-d\TH:i') }}">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                                    {{ __('Next: Receiver Details') }}
                                    <i class="bi bi-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Step 2: Receiver Information -->
                        <div class="step d-none" id="step2">
                            <h4 class="mb-4">
                                <span class="badge bg-primary me-2">2</span>
                                {{ __('Receiver Information') }}
                            </h4>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="receiver_name" class="form-label">
                                            <i class="bi bi-person me-1"></i>
                                            {{ __('Full Name') }}
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="receiver_name" 
                                               name="receiver_name" 
                                               required
                                               placeholder="{{ __('Enter receiver full name') }}">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="receiver_phone" class="form-label">
                                            <i class="bi bi-phone me-1"></i>
                                            {{ __('Phone Number') }}
                                        </label>
                                        <input type="tel" 
                                               class="form-control" 
                                               id="receiver_phone" 
                                               name="receiver_phone" 
                                               required
                                               placeholder="{{ __('Enter phone number') }}">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="receiver_address" class="form-label">
                                    <i class="bi bi-geo-alt me-1"></i>
                                    {{ __('Address') }}
                                </label>
                                <textarea class="form-control" 
                                          id="receiver_address" 
                                          name="receiver_address" 
                                          rows="3"
                                          placeholder="{{ __('Enter receiver address') }}"></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="receiver_id_number" class="form-label">
                                            <i class="bi bi-card-text me-1"></i>
                                            {{ __('ID Number (Optional)') }}
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="receiver_id_number" 
                                               name="receiver_id_number"
                                               placeholder="{{ __('National ID or Passport') }}">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="purpose" class="form-label">
                                            <i class="bi bi-info-circle me-1"></i>
                                            {{ __('Purpose of Transfer') }}
                                        </label>
                                        <select class="form-select" id="purpose" name="purpose">
                                            <option value="">{{ __('Select purpose') }}</option>
                                            <option value="family_support">{{ __('Family Support') }}</option>
                                            <option value="education">{{ __('Education') }}</option>
                                            <option value="medical">{{ __('Medical') }}</option>
                                            <option value="business">{{ __('Business') }}</option>
                                            <option value="investment">{{ __('Investment') }}</option>
                                            <option value="other">{{ __('Other') }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">
                                    <i class="bi bi-chat-text me-1"></i>
                                    {{ __('Notes (Optional)') }}
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3"
                                          placeholder="{{ __('Any additional notes or instructions') }}"></textarea>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" onclick="prevStep(1)">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    {{ __('Previous') }}
                                </button>
                                <button type="button" class="btn btn-primary" onclick="nextStep(3)">
                                    {{ __('Next: Review & Confirm') }}
                                    <i class="bi bi-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Step 3: Review & Confirm -->
                        <div class="step d-none" id="step3">
                            <h4 class="mb-4">
                                <span class="badge bg-primary me-2">3</span>
                                {{ __('Review & Confirm') }}
                            </h4>
                            
                            <div id="transferSummary">
                                <!-- Summary will be populated by JavaScript -->
                            </div>
                            
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    {{ __('I agree to the') }} 
                                    <a href="#" class="text-decoration-none">{{ __('Terms and Conditions') }}</a>
                                    {{ __('and') }}
                                    <a href="#" class="text-decoration-none">{{ __('Privacy Policy') }}</a>
                                </label>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" onclick="prevStep(2)">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    {{ __('Previous') }}
                                </button>
                                <button type="submit" class="btn btn-success" data-original-text="{{ __('Send Money') }}">
                                    <i class="bi bi-send me-2"></i>
                                    {{ __('Send Money') }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Transfer Summary Sidebar -->
        <div class="col-lg-4">
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-calculator me-2"></i>
                        {{ __('Transfer Summary') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div id="calculationResult" class="d-none">
                        <div class="d-flex justify-content-between mb-2">
                            <span>{{ __('You send:') }}</span>
                            <strong id="sendAmount">-</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>{{ __('Transfer fee:') }}</span>
                            <span id="feeAmount">-</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>{{ __('Exchange rate:') }}</span>
                            <span id="exchangeRate">-</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-2">
                            <span>{{ __('Total amount:') }}</span>
                            <strong class="text-primary" id="totalAmount">-</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span>{{ __('Recipient gets:') }}</span>
                            <strong class="text-success" id="receiveAmount">-</strong>
                        </div>
                        <div class="text-center">
                            <small class="text-muted" id="estimatedDelivery">-</small>
                        </div>
                    </div>
                    
                    <div id="calculationPlaceholder">
                        <div class="text-center text-muted">
                            <i class="bi bi-calculator fs-1 mb-3"></i>
                            <p>{{ __('Fill in the transfer details to see the calculation') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentStep = 1;
let calculationData = null;

// Step navigation
function nextStep(step) {
    if (validateCurrentStep()) {
        document.getElementById(`step${currentStep}`).classList.add('d-none');
        document.getElementById(`step${step}`).classList.remove('d-none');
        currentStep = step;
        
        if (step === 3) {
            generateSummary();
        }
    }
}

function prevStep(step) {
    document.getElementById(`step${currentStep}`).classList.add('d-none');
    document.getElementById(`step${step}`).classList.remove('d-none');
    currentStep = step;
}

function validateCurrentStep() {
    const currentStepElement = document.getElementById(`step${currentStep}`);
    const requiredFields = currentStepElement.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// Auto-calculate fees when form data changes
function setupAutoCalculation() {
    const fields = ['sender_country_id', 'receiver_country_id', 'sender_currency_id', 'receiver_currency_id', 'amount'];
    
    fields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('change', calculateFees);
            field.addEventListener('input', debounce(calculateFees, 500));
        }
    });
}

function calculateFees() {
    const formData = new FormData(document.getElementById('transferForm'));
    
    // Check if all required fields for calculation are filled
    const requiredFields = ['sender_country_id', 'receiver_country_id', 'sender_currency_id', 'receiver_currency_id', 'amount'];
    const hasAllFields = requiredFields.every(field => formData.get(field));
    
    if (!hasAllFields) {
        document.getElementById('calculationResult').classList.add('d-none');
        document.getElementById('calculationPlaceholder').classList.remove('d-none');
        return;
    }
    
    axios.post('{{ route("transfers.calculate-fees") }}', formData)
        .then(response => {
            if (response.data.success) {
                calculationData = response.data.data;
                updateCalculationDisplay(calculationData);
            }
        })
        .catch(error => {
            console.error('Calculation error:', error);
        });
}

function updateCalculationDisplay(data) {
    document.getElementById('sendAmount').textContent = `${data.amount} ${data.sender_currency}`;
    document.getElementById('feeAmount').textContent = `${data.fee_amount} ${data.sender_currency}`;
    document.getElementById('exchangeRate').textContent = `1 ${data.sender_currency} = ${data.exchange_rate} ${data.receiver_currency}`;
    document.getElementById('totalAmount').textContent = `${data.total_amount} ${data.sender_currency}`;
    document.getElementById('receiveAmount').textContent = `${data.converted_amount} ${data.receiver_currency}`;
    document.getElementById('estimatedDelivery').textContent = `Estimated delivery: ${data.estimated_delivery}`;
    
    document.getElementById('calculationResult').classList.remove('d-none');
    document.getElementById('calculationPlaceholder').classList.add('d-none');
}

function generateSummary() {
    if (!calculationData) {
        calculateFees();
        return;
    }
    
    const formData = new FormData(document.getElementById('transferForm'));
    
    const summary = `
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">{{ __('Transfer Details') }}</h6>
                <div class="row">
                    <div class="col-6">
                        <p class="mb-1"><strong>{{ __('You send:') }}</strong></p>
                        <p class="mb-1"><strong>{{ __('Fee:') }}</strong></p>
                        <p class="mb-1"><strong>{{ __('Total:') }}</strong></p>
                        <p class="mb-1"><strong>{{ __('Recipient gets:') }}</strong></p>
                    </div>
                    <div class="col-6 text-end">
                        <p class="mb-1">${calculationData.amount} ${calculationData.sender_currency}</p>
                        <p class="mb-1">${calculationData.fee_amount} ${calculationData.sender_currency}</p>
                        <p class="mb-1 text-primary"><strong>${calculationData.total_amount} ${calculationData.sender_currency}</strong></p>
                        <p class="mb-1 text-success"><strong>${calculationData.converted_amount} ${calculationData.receiver_currency}</strong></p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card bg-light mt-3">
            <div class="card-body">
                <h6 class="card-title">{{ __('Receiver Information') }}</h6>
                <p class="mb-1"><strong>{{ __('Name:') }}</strong> ${formData.get('receiver_name')}</p>
                <p class="mb-1"><strong>{{ __('Phone:') }}</strong> ${formData.get('receiver_phone')}</p>
                ${formData.get('receiver_address') ? `<p class="mb-1"><strong>{{ __('Address:') }}</strong> ${formData.get('receiver_address')}</p>` : ''}
                ${formData.get('purpose') ? `<p class="mb-1"><strong>{{ __('Purpose:') }}</strong> ${formData.get('purpose')}</p>` : ''}
            </div>
        </div>
    `;
    
    document.getElementById('transferSummary').innerHTML = summary;
}

// Form submission
document.getElementById('transferForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (!validateCurrentStep()) {
        return;
    }
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    
    setLoading(submitBtn, true);
    
    axios.post(this.action, formData)
        .then(response => {
            if (response.data.success) {
                showNotification(response.data.message, 'success');
                
                // Show success modal with transfer details
                showTransferSuccess(response.data.data);
            } else {
                showNotification(response.data.message, 'error');
            }
        })
        .catch(error => {
            if (error.response?.status === 422) {
                // Validation errors
                const errors = error.response.data.errors;
                Object.keys(errors).forEach(field => {
                    const input = document.querySelector(`[name="${field}"]`);
                    if (input) {
                        input.classList.add('is-invalid');
                    }
                });
                showNotification('Please check the form for errors', 'error');
            } else {
                const message = error.response?.data?.message || 'Transfer failed. Please try again.';
                showNotification(message, 'error');
            }
        })
        .finally(() => {
            setLoading(submitBtn, false);
        });
});

function showTransferSuccess(data) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-check-circle me-2"></i>
                        {{ __('Transfer Created Successfully') }}
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <div class="alert alert-success">
                            <h4>{{ __('Transfer Code:') }} <strong>${data.transfer_code}</strong></h4>
                            <p class="mb-0">{{ __('Please save this code for tracking your transfer') }}</p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>{{ __('Transfer Details') }}</h6>
                            <p><strong>{{ __('Amount:') }}</strong> ${data.amount}</p>
                            <p><strong>{{ __('Fee:') }}</strong> ${data.fee_amount}</p>
                            <p><strong>{{ __('Total:') }}</strong> ${data.total_amount}</p>
                            <p><strong>{{ __('Status:') }}</strong> <span class="badge bg-warning">${data.status}</span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>{{ __('Pickup Information') }}</h6>
                            <p><strong>{{ __('Pickup Code:') }}</strong> ${data.pickup_code}</p>
                            <p><strong>{{ __('Recipient Gets:') }}</strong> ${data.converted_amount}</p>
                            <p><strong>{{ __('Estimated Delivery:') }}</strong> ${data.estimated_delivery}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="window.location.href='{{ route('transfers.my') }}'">
                        {{ __('View My Transfers') }}
                    </button>
                    <button type="button" class="btn btn-success" onclick="window.location.href='{{ route('transfers.create') }}'">
                        {{ __('Send Another Transfer') }}
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    setupAutoCalculation();
});
</script>
@endpush
