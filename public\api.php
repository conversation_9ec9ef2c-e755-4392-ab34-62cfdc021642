<?php
// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Set JSON headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Get the API endpoint from query parameter
$endpoint = $_GET['endpoint'] ?? 'health';
$method = $_SERVER['REQUEST_METHOD'];

try {
    // Connect to database
    $db = new PDO('sqlite:' . __DIR__ . '/../database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    switch ($endpoint) {
        case 'health':
            handleHealthCheck($db);
            break;
            
        case 'stats':
            handleStats($db);
            break;
            
        case 'users':
            handleUsers($db, $method);
            break;
            
        case 'transfers':
            handleTransfers($db, $method);
            break;
            
        case 'countries':
            handleCountries($db);
            break;
            
        default:
            throw new Exception('Unknown API endpoint: ' . $endpoint, 404);
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ];
    
    http_response_code($e->getCode() ?: 500);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

function handleHealthCheck($db) {
    // Get system statistics
    $stats = [
        'users_total' => $db->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'transfers_total' => $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn(),
        'countries_total' => $db->query("SELECT COUNT(*) FROM countries")->fetchColumn()
    ];
    
    $response = [
        'success' => true,
        'status' => 'healthy',
        'timestamp' => date('Y-m-d H:i:s'),
        'database' => 'connected',
        'statistics' => $stats,
        'system' => [
            'name' => 'Elite Transfer System',
            'version' => '7.0',
            'php_version' => PHP_VERSION
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

function handleStats($db) {
    $stats = [
        'users' => [
            'total' => $db->query("SELECT COUNT(*) FROM users")->fetchColumn(),
            'active' => $db->query("SELECT COUNT(*) FROM users WHERE is_active = 1")->fetchColumn(),
            'admins' => $db->query("SELECT COUNT(*) FROM users WHERE role = 'admin'")->fetchColumn(),
            'customers' => $db->query("SELECT COUNT(*) FROM users WHERE role = 'customer'")->fetchColumn()
        ],
        'transfers' => [
            'total' => $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn(),
            'completed' => $db->query("SELECT COUNT(*) FROM transfers WHERE status = 'completed'")->fetchColumn(),
            'pending' => $db->query("SELECT COUNT(*) FROM transfers WHERE status = 'pending'")->fetchColumn(),
            'processing' => $db->query("SELECT COUNT(*) FROM transfers WHERE status = 'processing'")->fetchColumn(),
            'today' => $db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = DATE('now')")->fetchColumn()
        ],
        'countries' => [
            'total' => $db->query("SELECT COUNT(*) FROM countries")->fetchColumn(),
            'active' => $db->query("SELECT COUNT(*) FROM countries WHERE is_active = 1")->fetchColumn()
        ]
    ];
    
    $response = [
        'success' => true,
        'data' => $stats,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

function handleUsers($db, $method) {
    // Require admin access for user data
    if (!is_logged_in() || get_user_data()['role'] !== 'admin') {
        throw new Exception('Unauthorized access', 401);
    }
    
    if ($method === 'GET') {
        $users = $db->query("
            SELECT id, name, email, role, kyc_status, is_active, created_at 
            FROM users 
            ORDER BY created_at DESC 
            LIMIT 50
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        $response = [
            'success' => true,
            'data' => $users,
            'count' => count($users),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('Method not allowed', 405);
    }
}

function handleTransfers($db, $method) {
    // Require login for transfer data
    if (!is_logged_in()) {
        throw new Exception('Authentication required', 401);
    }
    
    if ($method === 'GET') {
        $userData = get_user_data();
        
        if ($userData['role'] === 'admin') {
            // Admin can see all transfers
            $transfers = $db->query("
                SELECT t.*, sc.name as sender_country, rc.name as receiver_country
                FROM transfers t
                LEFT JOIN countries sc ON t.sender_country_id = sc.id
                LEFT JOIN countries rc ON t.receiver_country_id = rc.id
                ORDER BY t.created_at DESC 
                LIMIT 50
            ")->fetchAll(PDO::FETCH_ASSOC);
        } else {
            // Regular users can only see their own transfers
            $stmt = $db->prepare("
                SELECT t.*, sc.name as sender_country, rc.name as receiver_country
                FROM transfers t
                LEFT JOIN countries sc ON t.sender_country_id = sc.id
                LEFT JOIN countries rc ON t.receiver_country_id = rc.id
                WHERE t.sender_id = ?
                ORDER BY t.created_at DESC 
                LIMIT 50
            ");
            $stmt->execute([$userData['id']]);
            $transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        $response = [
            'success' => true,
            'data' => $transfers,
            'count' => count($transfers),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('Method not allowed', 405);
    }
}

function handleCountries($db) {
    $countries = $db->query("
        SELECT id, name, code, currency, is_active 
        FROM countries 
        WHERE is_active = 1 
        ORDER BY name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    $response = [
        'success' => true,
        'data' => $countries,
        'count' => count($countries),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
