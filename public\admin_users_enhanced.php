<?php

// Elite Transfer System - Enhanced Admin Users Management
// Complete functionality with all features

// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

// Load database manager
require_once __DIR__ . '/includes/database_manager.php';

try {
    $dbManager = DatabaseManager::getInstance();
    $db = $dbManager->getConnection();
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'get_users':
            $page = intval($_POST['page'] ?? 1);
            $limit = intval($_POST['limit'] ?? 10);
            $search = $_POST['search'] ?? '';
            $role_filter = $_POST['role_filter'] ?? '';
            $status_filter = $_POST['status_filter'] ?? '';
            
            $offset = ($page - 1) * $limit;
            
            // Build WHERE clause
            $where = ["deleted_at IS NULL"];
            $params = [];
            
            if ($search) {
                $where[] = "(name LIKE ? OR email LIKE ? OR phone LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }
            
            if ($role_filter) {
                $where[] = "role = ?";
                $params[] = $role_filter;
            }
            
            if ($status_filter) {
                $where[] = "status = ?";
                $params[] = $status_filter;
            }
            
            $whereClause = implode(' AND ', $where);
            
            // Get total count
            $countQuery = "SELECT COUNT(*) FROM users WHERE $whereClause";
            $stmt = $db->prepare($countQuery);
            $stmt->execute($params);
            $total = $stmt->fetchColumn();
            
            // Get users
            $query = "
                SELECT u.id, u.user_code, u.name, u.email, u.phone, u.role, u.status, 
                       u.created_at, u.last_login, u.email_verified_at, u.phone_verified_at,
                       u.kyc_status, u.country_id, u.daily_limit, u.monthly_limit,
                       c.name as country_name
                FROM users u
                LEFT JOIN countries c ON u.country_id = c.id
                WHERE $whereClause 
                ORDER BY u.created_at DESC 
                LIMIT $limit OFFSET $offset
            ";
            
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'users' => $users,
                'total' => $total,
                'page' => $page,
                'pages' => ceil($total / $limit)
            ]);
            exit;
            
        case 'add_user':
            $name = $_POST['name'] ?? '';
            $email = $_POST['email'] ?? '';
            $phone = $_POST['phone'] ?? '';
            $role = $_POST['role'] ?? 'customer';
            $password = $_POST['password'] ?? '';
            $country_id = $_POST['country_id'] ?? 1;
            
            // Validation
            if (empty($name) || empty($email) || empty($password)) {
                echo json_encode(['success' => false, 'message' => 'جميع الحقول مطلوبة']);
                exit;
            }
            
            // Check if email exists
            $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND deleted_at IS NULL");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                echo json_encode(['success' => false, 'message' => 'البريد الإلكتروني مستخدم بالفعل']);
                exit;
            }
            
            // Generate user code
            $stmt = $db->prepare("SELECT MAX(id) FROM users");
            $stmt->execute();
            $maxId = $stmt->fetchColumn() ?: 0;
            $userCode = 'USR' . str_pad($maxId + 1, 4, '0', STR_PAD_LEFT);
            
            // Insert user
            $stmt = $db->prepare("
                INSERT INTO users (user_code, name, email, phone, password_hash, role, status, country_id, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, 'active', ?, NOW())
            ");
            
            $result = $stmt->execute([
                $userCode,
                $name,
                $email,
                $phone,
                password_hash($password, PASSWORD_DEFAULT),
                $role,
                $country_id
            ]);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم إضافة المستخدم بنجاح']);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في إضافة المستخدم']);
            }
            exit;
            
        case 'update_user':
            $id = intval($_POST['id']);
            $name = $_POST['name'] ?? '';
            $email = $_POST['email'] ?? '';
            $phone = $_POST['phone'] ?? '';
            $role = $_POST['role'] ?? '';
            $status = $_POST['status'] ?? '';
            $daily_limit = floatval($_POST['daily_limit'] ?? 0);
            $monthly_limit = floatval($_POST['monthly_limit'] ?? 0);
            
            // Check if email exists for other users
            $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND id != ? AND deleted_at IS NULL");
            $stmt->execute([$email, $id]);
            if ($stmt->fetch()) {
                echo json_encode(['success' => false, 'message' => 'البريد الإلكتروني مستخدم بالفعل']);
                exit;
            }
            
            // Update user
            $stmt = $db->prepare("
                UPDATE users 
                SET name = ?, email = ?, phone = ?, role = ?, status = ?, 
                    daily_limit = ?, monthly_limit = ?, updated_at = NOW()
                WHERE id = ? AND deleted_at IS NULL
            ");
            
            $result = $stmt->execute([
                $name, $email, $phone, $role, $status,
                $daily_limit, $monthly_limit, $id
            ]);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم تحديث المستخدم بنجاح']);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في تحديث المستخدم']);
            }
            exit;
            
        case 'delete_user':
            $id = intval($_POST['id']);
            
            // Soft delete
            $stmt = $db->prepare("UPDATE users SET deleted_at = NOW() WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم حذف المستخدم بنجاح']);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في حذف المستخدم']);
            }
            exit;
            
        case 'get_user':
            $id = intval($_POST['id']);
            $stmt = $db->prepare("
                SELECT * FROM users 
                WHERE id = ? AND deleted_at IS NULL
            ");
            $stmt->execute([$id]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo json_encode(['success' => true, 'user' => $user]);
            } else {
                echo json_encode(['success' => false, 'message' => 'المستخدم غير موجود']);
            }
            exit;
            
        case 'reset_password':
            $id = intval($_POST['id']);
            $new_password = $_POST['new_password'] ?? '';
            
            if (empty($new_password)) {
                echo json_encode(['success' => false, 'message' => 'كلمة المرور مطلوبة']);
                exit;
            }
            
            $stmt = $db->prepare("
                UPDATE users 
                SET password_hash = ?, updated_at = NOW()
                WHERE id = ? AND deleted_at IS NULL
            ");
            
            $result = $stmt->execute([
                password_hash($new_password, PASSWORD_DEFAULT),
                $id
            ]);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح']);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في تغيير كلمة المرور']);
            }
            exit;
    }
}

// Get statistics
$stats = [
    'total_users' => $db->query("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL")->fetchColumn(),
    'active_users' => $db->query("SELECT COUNT(*) FROM users WHERE status = 'active' AND deleted_at IS NULL")->fetchColumn(),
    'pending_kyc' => $db->query("SELECT COUNT(*) FROM users WHERE kyc_status = 'pending' AND deleted_at IS NULL")->fetchColumn(),
    'new_today' => $db->query("SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE() AND deleted_at IS NULL")->fetchColumn()
];

// Get countries for dropdown
$countries = $db->query("SELECT id, name FROM countries WHERE is_active = 1 ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);

$userData = getUserData();
$userName = $userData['name'] ?? 'مدير النظام';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
        }
        
        .stats-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stats-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .role-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .role-admin { background: #dc3545; color: white; }
        .role-manager { background: #fd7e14; color: white; }
        .role-agent { background: #20c997; color: white; }
        .role-customer { background: #6f42c1; color: white; }
        .role-compliance { background: #0dcaf0; color: white; }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-active { background: #d1e7dd; color: #0f5132; }
        .status-inactive { background: #f8d7da; color: #842029; }
        .status-suspended { background: #fff3cd; color: #664d03; }
        .status-pending { background: #cff4fc; color: #055160; }
        
        .btn-action {
            margin: 2px;
            padding: 5px 10px;
            font-size: 0.8em;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .form-floating label {
            color: #6c757d;
        }
        
        .pagination {
            justify-content: center;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box .bi-search {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        
        .search-box input {
            padding-left: 40px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-1">
                        <i class="bi bi-people-fill me-2"></i>
                        إدارة المستخدمين
                    </h1>
                    <p class="mb-0 opacity-75">إدارة شاملة لحسابات المستخدمين والصلاحيات</p>
                    <small class="opacity-50">مرحباً <?= htmlspecialchars($userName) ?></small>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light me-2" onclick="loadUsers()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        تحديث
                    </button>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="bi bi-person-plus me-1"></i>
                        إضافة مستخدم
                    </button>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['total_users']) ?></div>
                        <div class="stats-label">إجمالي المستخدمين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['active_users']) ?></div>
                        <div class="stats-label">المستخدمون النشطون</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['pending_kyc']) ?></div>
                        <div class="stats-label">في انتظار التحقق</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($stats['new_today']) ?></div>
                        <div class="stats-label">جديد اليوم</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Filters -->
            <div class="filter-section">
                <div class="row">
                    <div class="col-md-4">
                        <div class="search-box">
                            <i class="bi bi-search"></i>
                            <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو البريد أو الهاتف...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="roleFilter">
                            <option value="">جميع الأدوار</option>
                            <option value="admin">مدير النظام</option>
                            <option value="manager">مدير</option>
                            <option value="agent">وكيل</option>
                            <option value="customer">عميل</option>
                            <option value="compliance">مسؤول الامتثال</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="suspended">معلق</option>
                            <option value="pending">في الانتظار</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="limitSelect">
                            <option value="10">10 لكل صفحة</option>
                            <option value="25">25 لكل صفحة</option>
                            <option value="50">50 لكل صفحة</option>
                            <option value="100">100 لكل صفحة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary w-100" onclick="exportUsers()">
                            <i class="bi bi-download me-1"></i>
                            تصدير
                        </button>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>التحقق</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center p-3">
                    <div id="tableInfo" class="text-muted"></div>
                    <nav>
                        <ul class="pagination mb-0" id="pagination"></ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة مستخدم جديد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="addName" name="name" required>
                                    <label for="addName">الاسم الكامل</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="addEmail" name="email" required>
                                    <label for="addEmail">البريد الإلكتروني</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="addPhone" name="phone">
                                    <label for="addPhone">رقم الهاتف</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="addRole" name="role" required>
                                        <option value="customer">عميل</option>
                                        <option value="agent">وكيل</option>
                                        <option value="manager">مدير</option>
                                        <option value="compliance">مسؤول الامتثال</option>
                                        <option value="admin">مدير النظام</option>
                                    </select>
                                    <label for="addRole">الدور</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="addPassword" name="password" required>
                                    <label for="addPassword">كلمة المرور</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="addCountry" name="country_id">
                                        <?php foreach ($countries as $country): ?>
                                            <option value="<?= $country['id'] ?>"><?= htmlspecialchars($country['name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <label for="addCountry">الدولة</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="addUser()">
                        <i class="bi bi-check-lg me-1"></i>
                        إضافة المستخدم
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-gear me-2"></i>
                        تعديل المستخدم
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" id="editUserId" name="id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="editName" name="name" required>
                                    <label for="editName">الاسم الكامل</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="editEmail" name="email" required>
                                    <label for="editEmail">البريد الإلكتروني</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="editPhone" name="phone">
                                    <label for="editPhone">رقم الهاتف</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="editRole" name="role" required>
                                        <option value="customer">عميل</option>
                                        <option value="agent">وكيل</option>
                                        <option value="manager">مدير</option>
                                        <option value="compliance">مسؤول الامتثال</option>
                                        <option value="admin">مدير النظام</option>
                                    </select>
                                    <label for="editRole">الدور</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="editStatus" name="status" required>
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                        <option value="suspended">معلق</option>
                                        <option value="pending">في الانتظار</option>
                                    </select>
                                    <label for="editStatus">الحالة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" class="form-control" id="editDailyLimit" name="daily_limit" step="0.01">
                                    <label for="editDailyLimit">الحد اليومي</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" class="form-control" id="editMonthlyLimit" name="monthly_limit" step="0.01">
                                    <label for="editMonthlyLimit">الحد الشهري</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="updateUser()">
                        <i class="bi bi-check-lg me-1"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reset Password Modal -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-key me-2"></i>
                        إعادة تعيين كلمة المرور
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="resetPasswordForm">
                        <input type="hidden" id="resetUserId" name="id">
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="newPassword" name="new_password" required>
                            <label for="newPassword">كلمة المرور الجديدة</label>
                        </div>
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="confirmPassword" required>
                            <label for="confirmPassword">تأكيد كلمة المرور</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="resetPassword()">
                        <i class="bi bi-check-lg me-1"></i>
                        تغيير كلمة المرور
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        let currentPage = 1;
        let currentLimit = 10;
        let currentSearch = '';
        let currentRoleFilter = '';
        let currentStatusFilter = '';

        // Load users on page load
        $(document).ready(function() {
            loadUsers();

            // Search functionality
            $('#searchInput').on('input', function() {
                currentSearch = $(this).val();
                currentPage = 1;
                loadUsers();
            });

            // Filter functionality
            $('#roleFilter, #statusFilter').on('change', function() {
                currentRoleFilter = $('#roleFilter').val();
                currentStatusFilter = $('#statusFilter').val();
                currentPage = 1;
                loadUsers();
            });

            // Limit functionality
            $('#limitSelect').on('change', function() {
                currentLimit = parseInt($(this).val());
                currentPage = 1;
                loadUsers();
            });
        });

        function loadUsers() {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_users',
                    page: currentPage,
                    limit: currentLimit,
                    search: currentSearch,
                    role_filter: currentRoleFilter,
                    status_filter: currentStatusFilter
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        displayUsers(response.users);
                        updatePagination(response.page, response.pages, response.total);
                    } else {
                        showAlert('خطأ في تحميل البيانات', 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في الاتصال بالخادم', 'danger');
                }
            });
        }

        function displayUsers(users) {
            const tbody = $('#usersTableBody');
            tbody.empty();

            if (users.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد بيانات للعرض</p>
                        </td>
                    </tr>
                `);
                return;
            }

            users.forEach(user => {
                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    ${user.name.charAt(0).toUpperCase()}
                                </div>
                                <div>
                                    <strong>${user.name}</strong>
                                    <br>
                                    <small class="text-muted">${user.user_code || ''}</small>
                                </div>
                            </div>
                        </td>
                        <td>${user.email}</td>
                        <td>${user.phone || '-'}</td>
                        <td>
                            <span class="role-badge role-${user.role}">
                                ${getRoleText(user.role)}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-${user.status}">
                                ${getStatusText(user.status)}
                            </span>
                        </td>
                        <td>
                            ${user.email_verified_at ? '<i class="bi bi-check-circle-fill text-success" title="البريد محقق"></i>' : '<i class="bi bi-x-circle-fill text-danger" title="البريد غير محقق"></i>'}
                            ${user.phone_verified_at ? '<i class="bi bi-check-circle-fill text-success ms-1" title="الهاتف محقق"></i>' : '<i class="bi bi-x-circle-fill text-danger ms-1" title="الهاتف غير محقق"></i>'}
                        </td>
                        <td>${formatDate(user.created_at)}</td>
                        <td>
                            <button class="btn btn-sm btn-primary btn-action" onclick="editUser(${user.id})" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-warning btn-action" onclick="resetUserPassword(${user.id})" title="إعادة تعيين كلمة المرور">
                                <i class="bi bi-key"></i>
                            </button>
                            <button class="btn btn-sm btn-danger btn-action" onclick="deleteUser(${user.id}, '${user.name}')" title="حذف">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        function updatePagination(page, pages, total) {
            const pagination = $('#pagination');
            pagination.empty();

            const start = ((page - 1) * currentLimit) + 1;
            const end = Math.min(page * currentLimit, total);

            $('#tableInfo').text(`عرض ${start} إلى ${end} من ${total} مستخدم`);

            if (pages <= 1) return;

            // Previous button
            if (page > 1) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage(${page - 1})">السابق</a>
                    </li>
                `);
            }

            // Page numbers
            const startPage = Math.max(1, page - 2);
            const endPage = Math.min(pages, page + 2);

            for (let i = startPage; i <= endPage; i++) {
                pagination.append(`
                    <li class="page-item ${i === page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `);
            }

            // Next button
            if (page < pages) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage(${page + 1})">التالي</a>
                    </li>
                `);
            }
        }

        function changePage(page) {
            currentPage = page;
            loadUsers();
        }

        function addUser() {
            const formData = new FormData($('#addUserForm')[0]);
            formData.append('action', 'add_user');

            $.ajax({
                url: '',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showAlert(response.message, 'success');
                        $('#addUserModal').modal('hide');
                        $('#addUserForm')[0].reset();
                        loadUsers();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في الاتصال بالخادم', 'danger');
                }
            });
        }

        function editUser(id) {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_user',
                    id: id
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const user = response.user;
                        $('#editUserId').val(user.id);
                        $('#editName').val(user.name);
                        $('#editEmail').val(user.email);
                        $('#editPhone').val(user.phone);
                        $('#editRole').val(user.role);
                        $('#editStatus').val(user.status);
                        $('#editDailyLimit').val(user.daily_limit);
                        $('#editMonthlyLimit').val(user.monthly_limit);
                        $('#editUserModal').modal('show');
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في الاتصال بالخادم', 'danger');
                }
            });
        }

        function updateUser() {
            const formData = new FormData($('#editUserForm')[0]);
            formData.append('action', 'update_user');

            $.ajax({
                url: '',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showAlert(response.message, 'success');
                        $('#editUserModal').modal('hide');
                        loadUsers();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في الاتصال بالخادم', 'danger');
                }
            });
        }

        function deleteUser(id, name) {
            if (confirm(`هل أنت متأكد من حذف المستخدم "${name}"؟`)) {
                $.ajax({
                    url: '',
                    method: 'POST',
                    data: {
                        action: 'delete_user',
                        id: id
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.message, 'success');
                            loadUsers();
                        } else {
                            showAlert(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showAlert('خطأ في الاتصال بالخادم', 'danger');
                    }
                });
            }
        }

        function resetUserPassword(id) {
            $('#resetUserId').val(id);
            $('#resetPasswordModal').modal('show');
        }

        function resetPassword() {
            const newPassword = $('#newPassword').val();
            const confirmPassword = $('#confirmPassword').val();

            if (newPassword !== confirmPassword) {
                showAlert('كلمات المرور غير متطابقة', 'danger');
                return;
            }

            if (newPassword.length < 6) {
                showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'danger');
                return;
            }

            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'reset_password',
                    id: $('#resetUserId').val(),
                    new_password: newPassword
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showAlert(response.message, 'success');
                        $('#resetPasswordModal').modal('hide');
                        $('#resetPasswordForm')[0].reset();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في الاتصال بالخادم', 'danger');
                }
            });
        }

        function exportUsers() {
            // Create CSV export
            const csvContent = "data:text/csv;charset=utf-8,";
            // Implementation for CSV export would go here
            showAlert('ميزة التصدير قيد التطوير', 'info');
        }

        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);

            // Auto dismiss after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        }

        function getRoleText(role) {
            const roles = {
                'admin': 'مدير النظام',
                'manager': 'مدير',
                'agent': 'وكيل',
                'customer': 'عميل',
                'compliance': 'مسؤول الامتثال'
            };
            return roles[role] || role;
        }

        function getStatusText(status) {
            const statuses = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'suspended': 'معلق',
                'pending': 'في الانتظار'
            };
            return statuses[status] || status;
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    </script>
</body>
</html>
