<?php

/**
 * Enhanced Logout Page
 * Elite Transfer System - Secure logout with confirmation
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// <PERSON>le logout request
if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
    // Perform logout
    logout();

    // Redirect to index page with success message
    header('Location: index.php?message=logout_success');
    exit;
}

// If not confirmed, show confirmation page
$userData = getUserData();
$userName = $userData['name'] ?? 'مستخدم';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الخروج - <?= SYSTEM_NAME ?></title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logout-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        .logout-icon {
            font-size: 4rem;
            color: #ef4444;
            margin-bottom: 20px;
        }

        .logout-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 15px;
        }

        .logout-message {
            color: #6b7280;
            font-size: 1.1rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .user-info {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .user-avatar {
            font-size: 3rem;
            color: #3b82f6;
            margin-bottom: 10px;
        }

        .user-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .user-role {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .btn {
            border-radius: 25px;
            font-weight: 600;
            padding: 12px 30px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            border: none;
        }

        .btn-danger:hover {
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        .btn-secondary {
            background: #6b7280;
            border: none;
        }

        .btn-secondary:hover {
            background: #4b5563;
            box-shadow: 0 10px 20px rgba(107, 114, 128, 0.3);
        }

        .system-info {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 15px;
            padding: 15px;
            margin-top: 30px;
            font-size: 0.9rem;
            color: #1e40af;
        }

        .countdown {
            font-size: 1.2rem;
            font-weight: 600;
            color: #ef4444;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .logout-container {
                padding: 30px 20px;
            }

            .logout-title {
                font-size: 1.5rem;
            }

            .btn {
                display: block;
                width: 100%;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <i class="bi bi-box-arrow-right logout-icon"></i>

        <h1 class="logout-title">تسجيل الخروج</h1>

        <p class="logout-message">
            هل أنت متأكد من رغبتك في تسجيل الخروج من النظام؟<br>
            سيتم إنهاء جلستك الحالية وستحتاج إلى تسجيل الدخول مرة أخرى.
        </p>

        <div class="user-info">
            <i class="bi bi-person-circle user-avatar"></i>
            <div class="user-name"><?= htmlspecialchars($userName) ?></div>
            <div class="user-role"><?= htmlspecialchars($userData['role'] ?? 'مستخدم') ?></div>
        </div>

        <div class="d-flex justify-content-center flex-wrap">
            <a href="?confirm=yes" class="btn btn-danger">
                <i class="bi bi-box-arrow-right me-2"></i>
                نعم، تسجيل الخروج
            </a>

            <a href="dashboard.php" class="btn btn-secondary">
                <i class="bi bi-arrow-right me-2"></i>
                إلغاء والعودة
            </a>
        </div>

        <div class="countdown" id="countdown">
            سيتم تسجيل الخروج تلقائياً خلال <span id="timer">30</span> ثانية
        </div>

        <div class="system-info">
            <div class="row text-center">
                <div class="col-md-4">
                    <i class="bi bi-shield-check me-1"></i>
                    جلسة آمنة
                </div>
                <div class="col-md-4">
                    <i class="bi bi-clock me-1"></i>
                    <?= date('H:i:s') ?>
                </div>
                <div class="col-md-4">
                    <i class="bi bi-calendar me-1"></i>
                    <?= date('Y-m-d') ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto logout countdown
        let timeLeft = 30;
        const timerElement = document.getElementById('timer');

        const countdown = setInterval(function() {
            timeLeft--;
            timerElement.textContent = timeLeft;

            if (timeLeft <= 0) {
                clearInterval(countdown);
                window.location.href = '?confirm=yes';
            }
        }, 1000);

        // Cancel countdown if user interacts
        document.addEventListener('click', function() {
            clearInterval(countdown);
            document.getElementById('countdown').style.display = 'none';
        });

        document.addEventListener('keypress', function() {
            clearInterval(countdown);
            document.getElementById('countdown').style.display = 'none';
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === 'y' || e.key === 'Y') {
                window.location.href = '?confirm=yes';
            } else if (e.key === 'Escape' || e.key === 'n' || e.key === 'N') {
                window.location.href = 'dashboard.php';
            }
        });

        console.log('🚪 Logout confirmation page loaded');
    </script>
</body>
</html>
