<?php

/**
 * System Configuration
 * Elite Transfer System - Central configuration file
 */

// Prevent direct access
if (!defined('ELITE_TRANSFER_SYSTEM')) {
    define('ELITE_TRANSFER_SYSTEM', true);
}

// System Settings
define('SYSTEM_NAME', 'Elite Transfer System');
define('SYSTEM_VERSION', '2.0.0');
define('SYSTEM_AUTHOR', 'Augment Agent');

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'elite_transfer');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// System Paths
define('BASE_PATH', dirname(__DIR__));
define('INCLUDES_PATH', BASE_PATH . '/includes');
define('ASSETS_PATH', BASE_PATH . '/assets');

// URLs
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
$basePath = dirname($scriptName);

define('BASE_URL', $protocol . '://' . $host . $basePath);
define('ASSETS_URL', BASE_URL . '/assets');

// System Settings
define('DEFAULT_LANGUAGE', 'ar');
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DEFAULT_CURRENCY', 'USD');

// Transfer Settings
define('MIN_TRANSFER_AMOUNT', 1);
define('MAX_TRANSFER_AMOUNT', 50000);
define('DEFAULT_FEE_RATE', 0.025); // 2.5%
define('DEFAULT_EXCHANGE_RATE', 1.0);

// Session Settings
define('SESSION_LIFETIME', 3600 * 24); // 24 hours
define('SESSION_NAME', 'elite_transfer_session');

// Security Settings
define('CSRF_TOKEN_LENGTH', 32);
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// File Upload Settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);

// Email Settings (for future use)
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Elite Transfer System');

// API Settings
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 100); // requests per hour
define('API_TOKEN_EXPIRY', 3600); // 1 hour

// Cache Settings
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 300); // 5 minutes
define('CACHE_PATH', BASE_PATH . '/cache');

// Log Settings
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_PATH', BASE_PATH . '/logs');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// Status Constants
define('STATUS_PENDING', 'pending');
define('STATUS_PROCESSING', 'processing');
define('STATUS_COMPLETED', 'completed');
define('STATUS_CANCELLED', 'cancelled');
define('STATUS_FAILED', 'failed');

// User Roles
define('ROLE_ADMIN', 'admin');
define('ROLE_MANAGER', 'manager');
define('ROLE_AGENT', 'agent');
define('ROLE_USER', 'user');

// Transfer Types
define('TRANSFER_TYPE_CASH', 'cash');
define('TRANSFER_TYPE_BANK', 'bank_deposit');
define('TRANSFER_TYPE_WALLET', 'mobile_wallet');

// Payment Methods
define('PAYMENT_CREDIT_CARD', 'credit_card');
define('PAYMENT_BANK_TRANSFER', 'bank_transfer');
define('PAYMENT_CASH', 'cash');
define('PAYMENT_WALLET', 'wallet');

// Error Codes
define('ERROR_INVALID_INPUT', 1001);
define('ERROR_DATABASE', 1002);
define('ERROR_AUTHENTICATION', 1003);
define('ERROR_AUTHORIZATION', 1004);
define('ERROR_NOT_FOUND', 1005);
define('ERROR_VALIDATION', 1006);
define('ERROR_SYSTEM', 1007);

// Success Codes
define('SUCCESS_CREATED', 2001);
define('SUCCESS_UPDATED', 2002);
define('SUCCESS_DELETED', 2003);
define('SUCCESS_RETRIEVED', 2004);

// Validation Rules
$VALIDATION_RULES = [
    'name' => [
        'required' => true,
        'min_length' => 2,
        'max_length' => 100,
        'pattern' => '/^[a-zA-Z\s\u0600-\u06FF]+$/'
    ],
    'phone' => [
        'required' => true,
        'pattern' => '/^\+?[1-9]\d{1,14}$/'
    ],
    'email' => [
        'required' => true,
        'pattern' => '/^[^\s@]+@[^\s@]+\.[^\s@]+$/'
    ],
    'amount' => [
        'required' => true,
        'min' => MIN_TRANSFER_AMOUNT,
        'max' => MAX_TRANSFER_AMOUNT,
        'type' => 'numeric'
    ],
    'transfer_code' => [
        'required' => true,
        'pattern' => '/^TRF\d{5}$/'
    ],
    'pickup_code' => [
        'required' => true,
        'pattern' => '/^PCK\d{5}$/'
    ]
];

// Status Labels (Arabic)
$STATUS_LABELS = [
    STATUS_PENDING => 'في الانتظار',
    STATUS_PROCESSING => 'قيد المعالجة',
    STATUS_COMPLETED => 'مكتمل',
    STATUS_CANCELLED => 'ملغي',
    STATUS_FAILED => 'فاشل'
];

// Role Labels (Arabic)
$ROLE_LABELS = [
    ROLE_ADMIN => 'مدير النظام',
    ROLE_MANAGER => 'مدير',
    ROLE_AGENT => 'وكيل',
    ROLE_USER => 'مستخدم'
];

// Transfer Type Labels (Arabic)
$TRANSFER_TYPE_LABELS = [
    TRANSFER_TYPE_CASH => 'استلام نقدي',
    TRANSFER_TYPE_BANK => 'إيداع بنكي',
    TRANSFER_TYPE_WALLET => 'محفظة إلكترونية'
];

// Payment Method Labels (Arabic)
$PAYMENT_METHOD_LABELS = [
    PAYMENT_CREDIT_CARD => 'بطاقة ائتمان',
    PAYMENT_BANK_TRANSFER => 'تحويل بنكي',
    PAYMENT_CASH => 'نقداً',
    PAYMENT_WALLET => 'محفظة إلكترونية'
];

// System Functions
function getConfig($key, $default = null) {
    return defined($key) ? constant($key) : $default;
}

function getStatusLabel($status) {
    global $STATUS_LABELS;
    return $STATUS_LABELS[$status] ?? $status;
}

function getRoleLabel($role) {
    global $ROLE_LABELS;
    return $ROLE_LABELS[$role] ?? $role;
}

function getTransferTypeLabel($type) {
    global $TRANSFER_TYPE_LABELS;
    return $TRANSFER_TYPE_LABELS[$type] ?? $type;
}

function getPaymentMethodLabel($method) {
    global $PAYMENT_METHOD_LABELS;
    return $PAYMENT_METHOD_LABELS[$method] ?? $method;
}

function formatCurrency($amount, $currency = DEFAULT_CURRENCY) {
    return $currency . ' ' . number_format($amount, 2);
}

function formatDate($date, $format = 'Y-m-d H:i:s') {
    if (empty($date)) return '-';
    try {
        return date($format, strtotime($date));
    } catch (Exception $e) {
        return $date;
    }
}

function generateTransferCode() {
    return 'TRF' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
}

function generatePickupCode() {
    return 'PCK' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
}

function calculateFee($amount, $rate = DEFAULT_FEE_RATE) {
    return $amount * $rate;
}

function calculateTotal($amount, $fee = null) {
    if ($fee === null) {
        $fee = calculateFee($amount);
    }
    return $amount + $fee;
}

function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateInput($value, $rules) {
    $errors = [];
    
    if (isset($rules['required']) && $rules['required'] && empty($value)) {
        $errors[] = 'هذا الحقل مطلوب';
    }
    
    if (!empty($value)) {
        if (isset($rules['min_length']) && strlen($value) < $rules['min_length']) {
            $errors[] = "الحد الأدنى {$rules['min_length']} أحرف";
        }
        
        if (isset($rules['max_length']) && strlen($value) > $rules['max_length']) {
            $errors[] = "الحد الأقصى {$rules['max_length']} حرف";
        }
        
        if (isset($rules['pattern']) && !preg_match($rules['pattern'], $value)) {
            $errors[] = 'تنسيق غير صحيح';
        }
        
        if (isset($rules['type']) && $rules['type'] === 'numeric' && !is_numeric($value)) {
            $errors[] = 'يجب أن يكون رقماً';
        }
        
        if (isset($rules['min']) && is_numeric($value) && $value < $rules['min']) {
            $errors[] = "الحد الأدنى {$rules['min']}";
        }
        
        if (isset($rules['max']) && is_numeric($value) && $value > $rules['max']) {
            $errors[] = "الحد الأقصى {$rules['max']}";
        }
    }
    
    return $errors;
}

function logMessage($level, $message, $context = []) {
    if (!LOG_ENABLED) return;
    
    $logFile = LOG_PATH . '/system_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : '';
    $logEntry = "[$timestamp] [$level] $message $contextStr" . PHP_EOL;
    
    // Create log directory if it doesn't exist
    if (!is_dir(LOG_PATH)) {
        mkdir(LOG_PATH, 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Set timezone
date_default_timezone_set(DEFAULT_TIMEZONE);

// Start session with custom settings
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_set_cookie_params(SESSION_LIFETIME);
    session_start();
}

?>
