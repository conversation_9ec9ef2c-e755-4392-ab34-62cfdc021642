<?php

/**
 * Advanced Backup System
 * Elite Transfer System - Comprehensive Backup & Recovery Management
 */

// Load core system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/database_manager_v2.php';
require_once __DIR__ . '/includes/session_helper_v2.php';

// Check authentication and admin privileges
if (!isLoggedIn() || !in_array(getUserRole(), ['admin', 'super_admin'])) {
    header('Location: login.php');
    exit;
}

$db = DatabaseManager::getInstance();

// Create backup logs table if not exists
$db->execute("
    CREATE TABLE IF NOT EXISTS backup_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        backup_type ENUM('full', 'incremental', 'differential') DEFAULT 'full',
        backup_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500),
        file_size BIGINT DEFAULT 0,
        status ENUM('pending', 'in_progress', 'completed', 'failed') DEFAULT 'pending',
        started_at TIMESTAMP NULL,
        completed_at TIMESTAMP NULL,
        error_message TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        INDEX idx_backup_type (backup_type)
    )
");

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'create_backup':
                $backupType = $_POST['backup_type'] ?? 'full';
                $includeTables = $_POST['include_tables'] ?? [];
                $compression = isset($_POST['compression']) && $_POST['compression'] === 'true';
                
                // Generate backup name
                $timestamp = date('Y-m-d_H-i-s');
                $backupName = "backup_{$backupType}_{$timestamp}";
                $backupDir = __DIR__ . '/backups/';
                
                // Create backup directory if not exists
                if (!is_dir($backupDir)) {
                    mkdir($backupDir, 0755, true);
                }
                
                $backupFile = $backupDir . $backupName . '.sql';
                if ($compression) {
                    $backupFile .= '.gz';
                }
                
                // Log backup start
                $backupLogId = $db->insert('backup_logs', [
                    'backup_type' => $backupType,
                    'backup_name' => $backupName,
                    'file_path' => $backupFile,
                    'status' => 'in_progress',
                    'started_at' => date('Y-m-d H:i:s'),
                    'created_by' => getUserId()
                ]);
                
                try {
                    // Get database configuration
                    $config = [
                        'host' => DB_HOST,
                        'username' => DB_USER,
                        'password' => DB_PASS,
                        'database' => DB_NAME
                    ];
                    
                    // Create backup
                    $backupResult = createDatabaseBackup($config, $backupFile, $backupType, $includeTables, $compression);
                    
                    if ($backupResult['success']) {
                        // Update backup log
                        $db->update('backup_logs', [
                            'status' => 'completed',
                            'completed_at' => date('Y-m-d H:i:s'),
                            'file_size' => filesize($backupFile)
                        ], 'id = :id', ['id' => $backupLogId]);
                        
                        echo json_encode([
                            'success' => true,
                            'message' => 'تم إنشاء النسخة الاحتياطية بنجاح',
                            'backup_id' => $backupLogId,
                            'backup_name' => $backupName,
                            'file_size' => formatFileSize(filesize($backupFile))
                        ], JSON_UNESCAPED_UNICODE);
                    } else {
                        throw new Exception($backupResult['error']);
                    }
                } catch (Exception $e) {
                    // Update backup log with error
                    $db->update('backup_logs', [
                        'status' => 'failed',
                        'completed_at' => date('Y-m-d H:i:s'),
                        'error_message' => $e->getMessage()
                    ], 'id = :id', ['id' => $backupLogId]);
                    
                    throw $e;
                }
                break;
                
            case 'get_backups':
                $page = intval($_POST['page'] ?? 1);
                $limit = intval($_POST['limit'] ?? 10);
                $offset = ($page - 1) * $limit;
                
                // Get total count
                $total = $db->selectOne("SELECT COUNT(*) as total FROM backup_logs")['total'];
                
                // Get backups
                $backups = $db->select("
                    SELECT 
                        bl.*,
                        u.name as created_by_name
                    FROM backup_logs bl
                    LEFT JOIN users u ON bl.created_by = u.id
                    ORDER BY bl.created_at DESC
                    LIMIT $limit OFFSET $offset
                ");
                
                // Format backup data
                foreach ($backups as &$backup) {
                    $backup['file_size_formatted'] = formatFileSize($backup['file_size']);
                    $backup['duration'] = calculateDuration($backup['started_at'], $backup['completed_at']);
                    $backup['file_exists'] = file_exists($backup['file_path']);
                }
                
                echo json_encode([
                    'success' => true,
                    'backups' => $backups,
                    'total' => intval($total),
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'download_backup':
                $backupId = intval($_POST['backup_id'] ?? 0);
                
                if (!$backupId) {
                    throw new Exception('معرف النسخة الاحتياطية مطلوب');
                }
                
                $backup = $db->selectOne("SELECT * FROM backup_logs WHERE id = :id", ['id' => $backupId]);
                
                if (!$backup) {
                    throw new Exception('النسخة الاحتياطية غير موجودة');
                }
                
                if (!file_exists($backup['file_path'])) {
                    throw new Exception('ملف النسخة الاحتياطية غير موجود');
                }
                
                echo json_encode([
                    'success' => true,
                    'download_url' => 'download_backup.php?id=' . $backupId,
                    'filename' => basename($backup['file_path'])
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'delete_backup':
                $backupId = intval($_POST['backup_id'] ?? 0);
                
                if (!$backupId) {
                    throw new Exception('معرف النسخة الاحتياطية مطلوب');
                }
                
                $backup = $db->selectOne("SELECT * FROM backup_logs WHERE id = :id", ['id' => $backupId]);
                
                if (!$backup) {
                    throw new Exception('النسخة الاحتياطية غير موجودة');
                }
                
                // Delete file if exists
                if (file_exists($backup['file_path'])) {
                    unlink($backup['file_path']);
                }
                
                // Delete record
                $db->delete('backup_logs', 'id = :id', ['id' => $backupId]);
                
                logMessage('INFO', 'Backup deleted', [
                    'backup_id' => $backupId,
                    'backup_name' => $backup['backup_name'],
                    'deleted_by' => getUserId()
                ]);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم حذف النسخة الاحتياطية بنجاح'
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'restore_backup':
                $backupId = intval($_POST['backup_id'] ?? 0);
                
                if (!$backupId) {
                    throw new Exception('معرف النسخة الاحتياطية مطلوب');
                }
                
                $backup = $db->selectOne("SELECT * FROM backup_logs WHERE id = :id", ['id' => $backupId]);
                
                if (!$backup) {
                    throw new Exception('النسخة الاحتياطية غير موجودة');
                }
                
                if (!file_exists($backup['file_path'])) {
                    throw new Exception('ملف النسخة الاحتياطية غير موجود');
                }
                
                // Restore database
                $restoreResult = restoreDatabase($backup['file_path']);
                
                if ($restoreResult['success']) {
                    logMessage('INFO', 'Database restored from backup', [
                        'backup_id' => $backupId,
                        'backup_name' => $backup['backup_name'],
                        'restored_by' => getUserId()
                    ]);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم استعادة قاعدة البيانات بنجاح'
                    ], JSON_UNESCAPED_UNICODE);
                } else {
                    throw new Exception($restoreResult['error']);
                }
                break;
                
            case 'get_backup_stats':
                $stats = [
                    'total_backups' => $db->selectOne("SELECT COUNT(*) as count FROM backup_logs")['count'],
                    'successful_backups' => $db->selectOne("SELECT COUNT(*) as count FROM backup_logs WHERE status = 'completed'")['count'],
                    'failed_backups' => $db->selectOne("SELECT COUNT(*) as count FROM backup_logs WHERE status = 'failed'")['count'],
                    'total_size' => $db->selectOne("SELECT COALESCE(SUM(file_size), 0) as size FROM backup_logs WHERE status = 'completed'")['size'],
                    'latest_backup' => $db->selectOne("SELECT created_at FROM backup_logs WHERE status = 'completed' ORDER BY created_at DESC LIMIT 1")['created_at'] ?? null,
                    'backup_types' => [
                        'full' => $db->selectOne("SELECT COUNT(*) as count FROM backup_logs WHERE backup_type = 'full' AND status = 'completed'")['count'],
                        'incremental' => $db->selectOne("SELECT COUNT(*) as count FROM backup_logs WHERE backup_type = 'incremental' AND status = 'completed'")['count'],
                        'differential' => $db->selectOne("SELECT COUNT(*) as count FROM backup_logs WHERE backup_type = 'differential' AND status = 'completed'")['count']
                    ]
                ];
                
                $stats['total_size_formatted'] = formatFileSize($stats['total_size']);
                $stats['success_rate'] = $stats['total_backups'] > 0 ? round(($stats['successful_backups'] / $stats['total_backups']) * 100, 2) : 0;
                
                echo json_encode([
                    'success' => true,
                    'stats' => $stats
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'schedule_backup':
                $schedule = $_POST['schedule'] ?? 'daily';
                $backupType = $_POST['backup_type'] ?? 'full';
                $time = $_POST['time'] ?? '02:00';
                
                // Save schedule to configuration (this would typically be saved to a config file or database)
                $scheduleConfig = [
                    'enabled' => true,
                    'schedule' => $schedule,
                    'backup_type' => $backupType,
                    'time' => $time,
                    'last_run' => null,
                    'next_run' => calculateNextRun($schedule, $time)
                ];
                
                // Save to file (in a real implementation, you might use a proper scheduler like cron)
                file_put_contents(__DIR__ . '/config/backup_schedule.json', json_encode($scheduleConfig, JSON_PRETTY_PRINT));
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم جدولة النسخ الاحتياطي بنجاح',
                    'next_run' => $scheduleConfig['next_run']
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'get_database_tables':
                $tables = $db->select("SHOW TABLES");
                $tableList = [];
                
                foreach ($tables as $table) {
                    $tableName = array_values($table)[0];
                    $tableInfo = $db->selectOne("SELECT COUNT(*) as row_count FROM `$tableName`");
                    $tableList[] = [
                        'name' => $tableName,
                        'rows' => $tableInfo['row_count']
                    ];
                }
                
                echo json_encode([
                    'success' => true,
                    'tables' => $tableList
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'إجراء غير صحيح'
                ], JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

// Helper Functions
function createDatabaseBackup($config, $backupFile, $backupType = 'full', $includeTables = [], $compression = false) {
    try {
        $mysqldumpPath = 'mysqldump'; // Adjust path if needed
        
        $command = "$mysqldumpPath --host={$config['host']} --user={$config['username']} --password={$config['password']} ";
        $command .= "--single-transaction --routines --triggers ";
        
        if (!empty($includeTables)) {
            $command .= implode(' ', $includeTables) . " ";
        }
        
        $command .= $config['database'];
        
        if ($compression) {
            $command .= " | gzip > $backupFile";
        } else {
            $command .= " > $backupFile";
        }
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0) {
            return ['success' => true];
        } else {
            return ['success' => false, 'error' => 'فشل في تنفيذ أمر النسخ الاحتياطي'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function restoreDatabase($backupFile) {
    try {
        $mysqlPath = 'mysql'; // Adjust path if needed
        $config = [
            'host' => DB_HOST,
            'username' => DB_USER,
            'password' => DB_PASS,
            'database' => DB_NAME
        ];
        
        $command = "$mysqlPath --host={$config['host']} --user={$config['username']} --password={$config['password']} {$config['database']}";
        
        if (pathinfo($backupFile, PATHINFO_EXTENSION) === 'gz') {
            $command = "gunzip < $backupFile | $command";
        } else {
            $command .= " < $backupFile";
        }
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0) {
            return ['success' => true];
        } else {
            return ['success' => false, 'error' => 'فشل في استعادة قاعدة البيانات'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

function calculateDuration($startTime, $endTime) {
    if (!$startTime || !$endTime) {
        return 'غير محدد';
    }
    
    $start = new DateTime($startTime);
    $end = new DateTime($endTime);
    $interval = $start->diff($end);
    
    if ($interval->h > 0) {
        return $interval->h . ' ساعة ' . $interval->i . ' دقيقة';
    } elseif ($interval->i > 0) {
        return $interval->i . ' دقيقة ' . $interval->s . ' ثانية';
    } else {
        return $interval->s . ' ثانية';
    }
}

function calculateNextRun($schedule, $time) {
    $now = new DateTime();
    $nextRun = new DateTime();
    
    switch ($schedule) {
        case 'daily':
            $nextRun->setTime(...explode(':', $time));
            if ($nextRun <= $now) {
                $nextRun->add(new DateInterval('P1D'));
            }
            break;
        case 'weekly':
            $nextRun->setTime(...explode(':', $time));
            $nextRun->modify('next sunday');
            break;
        case 'monthly':
            $nextRun->setTime(...explode(':', $time));
            $nextRun->modify('first day of next month');
            break;
    }
    
    return $nextRun->format('Y-m-d H:i:s');
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام النسخ الاحتياطي المتقدم - <?= SYSTEM_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
</head>
